import { ClientSideDashboard } from "@/components/dashboard/client-dashboard";
import { getAuthUser, getUserData } from "@/lib/auth-utils";

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default async function DashboardLayout({ children }: DashboardLayoutProps) {
  // Middleware already ensures the user is authenticated, so we just need to get the user data
  // Use our centralized auth utilities instead of direct Supabase calls
  const authUser = await getAuthUser();
  const userData = await getUserData();

  // This should never happen since middleware protects this route
  if (!authUser) {
    console.error("User not found in dashboard layout despite middleware protection");
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold">Session Error</h1>
        <p>Please try <a href="/sign-in?reset_auth=true" className="text-primary underline">signing in again</a>.</p>
      </div>
    );
  }

  // Handle missing user data without redirecting
  let userId = userData?.id || "unknown";
  if (!userData) {
    console.error("Error fetching user data");
    // Continue with a fallback ID instead of redirecting
  }

  const navigationItems = [
    {
      href: "/dashboard",
      label: "Overview",
      iconName: "LayoutDashboard",
    },
    {
      href: "/dashboard/events",
      label: "Events",
      iconName: "Calendar",
    },
    {
      href: "/dashboard/organizations",
      label: "Organizations",
      iconName: "Users",
    },
    {
      href: "/dashboard/tickets",
      label: "Tickets",
      iconName: "Ticket",
    },
    {
      href: "/dashboard/payments",
      label: "Payments",
      iconName: "CreditCard",
    },
    {
      href: "/dashboard/profile",
      label: "Profile",
      iconName: "UserCircle",
    },
  ];

  return (
    <div className="dashboard-layout flex h-screen bg-[hsl(var(--background))] dark:bg-[hsl(var(--dark-background))] text-[hsl(var(--foreground))] overflow-hidden">
      <ClientSideDashboard userId={userId} navItems={navigationItems}>
        {children}
      </ClientSideDashboard>
    </div>
  );
}