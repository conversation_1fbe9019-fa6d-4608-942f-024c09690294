import { logger } from '@/lib/logger';
import { cache } from 'react'

export interface Country {
  name: string
  code: string
  flag: string
}

export interface State {
  name: string
  code: string
}

// Fallback countries data in case the API fails
export const FALLBACK_COUNTRIES: Country[] = [
  { name: 'United States', code: 'US', flag: '🇺🇸' },
  { name: 'United Kingdom', code: 'GB', flag: '🇬🇧' },
  { name: 'Canada', code: 'CA', flag: '🇨🇦' },
  { name: 'Australia', code: 'AU', flag: '🇦🇺' },
  { name: 'Germany', code: 'DE', flag: '🇩🇪' },
  { name: 'France', code: 'FR', flag: '🇫🇷' },
  { name: 'Japan', code: 'JP', flag: '🇯🇵' },
  { name: 'China', code: 'CN', flag: '🇨🇳' },
  { name: 'India', code: 'IN', flag: '🇮🇳' },
  { name: 'Brazil', code: 'BR', flag: '🇧🇷' },
  { name: 'Mexico', code: 'MX', flag: '🇲🇽' },
  { name: 'South Africa', code: 'ZA', flag: '🇿🇦' },
  { name: 'Nigeria', code: 'NG', flag: '🇳🇬' },
  { name: 'Singapore', code: 'SG', flag: '🇸🇬' },
  { name: 'New Zealand', code: 'NZ', flag: '🇳🇿' },
  { name: 'Malaysia', code: 'MY', flag: '🇲🇾' },
  { name: 'Indonesia', code: 'ID', flag: '🇮🇩' },
  { name: 'Thailand', code: 'TH', flag: '🇹🇭' },
  { name: 'Philippines', code: 'PH', flag: '🇵🇭' },
  { name: 'Vietnam', code: 'VN', flag: '🇻🇳' },
];

// Using our internal API route with proper caching
export const getCountries = cache(async (): Promise<Country[]> => {
  try {
    const response = await fetch('/api/countries', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      // Optimize caching for performance
      next: { revalidate: 86400 }, // Cache for 24 hours
    });
    
    if (!response.ok) {
      logger.warn('Countries API returned non-OK response, using fallback data');
      return FALLBACK_COUNTRIES;
    }
    
    return await response.json();
  } catch (_error) {
    logger.warn('Error fetching countries, using fallback data');
    // Return fallback data on error
    return FALLBACK_COUNTRIES;
  }
});

// Common states for popular countries as fallback
export const FALLBACK_STATES: Record<string, State[]> = {
  'US': [
    { name: 'Alabama', code: 'AL' },
    { name: 'Alaska', code: 'AK' },
    { name: 'Arizona', code: 'AZ' },
    { name: 'California', code: 'CA' },
    { name: 'Colorado', code: 'CO' },
    { name: 'Florida', code: 'FL' },
    { name: 'New York', code: 'NY' },
    { name: 'Texas', code: 'TX' },
    { name: 'Washington', code: 'WA' },
  ],
  'CA': [
    { name: 'Alberta', code: 'AB' },
    { name: 'British Columbia', code: 'BC' },
    { name: 'Ontario', code: 'ON' },
    { name: 'Quebec', code: 'QC' },
  ],
  'GB': [
    { name: 'England', code: 'ENG' },
    { name: 'Scotland', code: 'SCT' },
    { name: 'Wales', code: 'WLS' },
    { name: 'Northern Ireland', code: 'NIR' },
  ],
};

// Optimized states fetch with caching
export const getStates = cache(async (countryCode: string): Promise<State[]> => {
  if (!countryCode) {
    return [];
  }
  
  try {
    const response = await fetch(`/api/states?country=${countryCode}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      // Optimize caching for performance
      next: { revalidate: 86400 }, // Cache for 24 hours
    });
    
    if (!response.ok) {
      logger.warn(`States API returned non-OK response for ${countryCode}, using fallback data`);
      return FALLBACK_STATES[countryCode] || [];
    }
    
    return await response.json();
  } catch (_error) {
    logger.warn(`Error fetching states for ${countryCode}, using fallback data`);
    return FALLBACK_STATES[countryCode] || [];
  }
}); 