import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/pages-client';
import { adminSupabase } from '@/lib/supabase/admin-client';

/**
 * API endpoint to download a data export
 * GET /api/export/[id]
 */
export async function GET(request: NextRequest) {
  // Extract the ID from the URL path
  const id = request.nextUrl.pathname.split('/').pop();
  try {
    // Authenticate the user
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id;

    const exportId = id;
    if (!exportId) {
      return NextResponse.json(
        { error: 'Export ID is required' },
        { status: 400 }
      );
    }

    // Use admin client for database operations
    const adminClient = await adminSupabase();

    // Get the export request from the database
    const { data: exportRequests, error: fetchError } = await adminClient
      .from('data_exports')
      .select('*')
      .eq('id', exportId)
      .limit(1);

    if (fetchError) {
      console.error('Error fetching export:', fetchError);
      return NextResponse.json(
        { error: 'Failed to fetch export' },
        { status: 500 }
      );
    }

    if (!exportRequests || exportRequests.length === 0) {
      return NextResponse.json(
        { error: 'Export not found' },
        { status: 404 }
      );
    }

    // Make sure exportRequests exists and has at least one element
    if (!exportRequests || exportRequests.length === 0) {
      return NextResponse.json(
        { error: 'Export request not found' },
        { status: 404 }
      );
    }

    const exportRequest = exportRequests[0];

    // Security check: ensure the user can only access their own exports
    if (exportRequest && exportRequest.user_id !== userId) {
      return NextResponse.json(
        { error: 'Unauthorized: You can only access your own exports' },
        { status: 403 }
      );
    }

    // Check if the export has expired
    if (exportRequest && exportRequest.expires_at && new Date(exportRequest.expires_at) < new Date()) {
      return NextResponse.json(
        { error: 'Export has expired. Please request a new export.' },
        { status: 410 }
      );
    }

    // Make sure exportRequest is defined before accessing its properties
    if (!exportRequest) {
      return NextResponse.json(
        { error: 'Export request not found or is invalid' },
        { status: 404 }
      );
    }

    // Generate the export data based on the type and format
    // In a real implementation, this would be generated by a background job
    // and the download URL would be stored in the database
    const exportData = await generateExportData(userId, exportRequest.export_type || 'all');

    // Increment the download count
    // Use type assertion to handle the download_count as a string or number
    const downloadCount = exportRequest.download_count as string | number | undefined;
    const currentCount = typeof downloadCount === 'string'
      ? parseInt(downloadCount || '0')
      : (downloadCount || 0);

    // Use a type assertion to avoid TypeScript errors
    const updateData: Record<string, unknown> = {
      download_count: currentCount + 1,
      status: 'completed',
      completed_at: new Date().toISOString()
    };

    const { error: updateError } = await adminClient
      .from('data_exports')
      .update(updateData)
      .eq('id', exportId);

    if (updateError) {
      console.error('Error updating download count:', updateError);
    }

    // Set the appropriate headers based on the export format
    const headers = new Headers();
    const exportType = exportRequest.export_type || 'all';
    const filename = `data-export-${exportType}-${new Date().toISOString().split('T')[0]}`;

    // Default to JSON if export_format is not specified
    const exportFormat = exportRequest.export_format || 'json';

    if (exportFormat === 'json') {
      headers.set('Content-Type', 'application/json');
      headers.set('Content-Disposition', `attachment; filename="${filename}.json"`);
      return new NextResponse(JSON.stringify(exportData, null, 2), {
        status: 200,
        headers
      });
    } else {
      // Generate CSV format
      const csv = convertToCSV(exportData);
      headers.set('Content-Type', 'text/csv');
      headers.set('Content-Disposition', `attachment; filename="${filename}.csv"`);
      return new NextResponse(csv, {
        status: 200,
        headers
      });
    }
  } catch (error) {
    console.error('Error generating export:', error);
    return NextResponse.json(
      { error: 'Failed to generate export' },
      { status: 500 }
    );
  }
}

/**
 * Generate export data based on the type
 */
async function generateExportData(userId: string, exportType: string) {
  // In a real implementation, you would query the database to get the user's data
  // For this example, we'll generate some dummy data based on the export type

  if (exportType === 'all') {
    return {
      profile: await getProfileData(userId),
      activity: await getActivityData(userId),
      consents: await getConsentData(userId)
    };
  } else if (exportType === 'profile') {
    return await getProfileData(userId);
  } else if (exportType === 'activity') {
    return await getActivityData(userId);
  } else if (exportType === 'consents') {
    return await getConsentData(userId);
  }

  return { message: 'No data found for the requested export type' };
}

/**
 * Get the user's profile data
 */
async function getProfileData(userId: string) {
  // In a real implementation, you would query the database
  // For this example, we'll return dummy data
  return {
    userId,
    profile: {
      name: 'Test User',
      email: '<EMAIL>',
      created_at: new Date().toISOString(),
      preferences: {
        theme: 'light',
        notifications: true
      }
    },
    contacts: [
      {
        id: '1',
        name: 'Contact 1',
        email: '<EMAIL>',
        relationship: 'friend'
      },
      {
        id: '2',
        name: 'Contact 2',
        email: '<EMAIL>',
        relationship: 'family'
      }
    ]
  };
}

/**
 * Get the user's activity data
 */
async function getActivityData(userId: string) {
  // In a real implementation, you would query the database
  // For this example, we'll return dummy data
  return {
    userId,
    activities: [
      {
        id: '1',
        type: 'login',
        timestamp: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
        details: 'Logged in from Chrome on MacOS'
      },
      {
        id: '2',
        type: 'profile_update',
        timestamp: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
        details: 'Updated profile picture'
      },
      {
        id: '3',
        type: 'contact_added',
        timestamp: new Date(Date.now() - 259200000).toISOString(), // 3 days ago
        details: 'Added new emergency contact'
      }
    ]
  };
}

/**
 * Get the user's consent data
 */
async function getConsentData(userId: string) {
  // In a real implementation, you would query the database
  // For this example, we'll return dummy data
  return {
    userId,
    consents: [
      {
        type: 'marketing',
        given: true,
        timestamp: new Date(Date.now() - 86400000).toISOString(),
        version: '1.0'
      },
      {
        type: 'analytics',
        given: false,
        timestamp: new Date(Date.now() - 172800000).toISOString(),
        version: '1.0'
      },
      {
        type: 'thirdpartysharing',
        given: false,
        timestamp: new Date(Date.now() - 259200000).toISOString(),
        version: '1.0'
      }
    ]
  };
}

/**
 * Convert JSON data to CSV format
 */
function convertToCSV(data: unknown): string {
  if (!data || typeof data !== 'object') return '';

  // Flatten nested objects
  const flattenObject = (obj: Record<string, unknown>, prefix = ''): Record<string, unknown> => {
    return Object.keys(obj).reduce((acc: Record<string, unknown>, k: string) => {
      const pre = prefix.length ? `${prefix}.` : '';
      if (typeof obj[k] === 'object' && obj[k] !== null && !Array.isArray(obj[k])) {
        Object.assign(acc, flattenObject(obj[k] as Record<string, unknown>, pre + k));
      } else if (Array.isArray(obj[k])) {
        // For arrays, we'll stringify the value
        acc[pre + k] = JSON.stringify(obj[k]);
      } else {
        acc[pre + k] = obj[k];
      }
      return acc;
    }, {});
  };

  // Get all headers from the data
  let headers: string[] = [];
  let rows: unknown[] = [];

  // Handle array data
  if (Array.isArray(data)) {
    data.forEach(item => {
      const flatItem = flattenObject(item as Record<string, unknown>);
      Object.keys(flatItem).forEach(header => {
        if (!headers.includes(header)) {
          headers.push(header);
        }
      });
      rows.push(flatItem);
    });
  } else {
    // Handle object data
    const flatData = flattenObject(data as Record<string, unknown>);
    headers = Object.keys(flatData);
    rows = [flatData];
  }

  // Create CSV string
  let csv = headers.join(',') + '\n';

  rows.forEach(row => {
    const rowObj = row as Record<string, unknown>;
    const values = headers.map(header => {
      const value = rowObj[header] !== undefined ? rowObj[header] : '';
      // Escape quotes and wrap in quotes if needed
      if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
        return `"${value.replace(/"/g, '""')}"`;
      }
      return value;
    });
    csv += values.join(',') + '\n';
  });

  return csv;
}