import { <PERSON>, <PERSON><PERSON><PERSON>er, CardContent } from '@/components/ui/card';
import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';
export default function EventRegistrationLoading() {
  return (
    <div className="container max-w-4xl mx-auto px-4 py-12">
      <Skeleton className="h-5 w-24 mb-8" />
      
      <div className="mb-8">
        <Skeleton className="h-10 w-3/4 mb-2" />
        <Skeleton className="h-5 w-1/2" />
      </div>
      
      <Card className="mb-8">
        <CardHeader>
          <Skeleton className="h-7 w-48 mb-2" />
          <Skeleton className="h-5 w-64" />
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="border rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <Skeleton className="h-6 w-32" />
                  <Skeleton className="h-6 w-20" />
                </div>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-3/4 mb-2" />
                <Skeleton className="h-3 w-32 mb-3" />
                <Skeleton className="h-10 w-full" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
