'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, CheckCircle2 } from 'lucide-react';

export default function RegistrationFieldsMigrationPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null);

  const applyMigration = async () => {
    try {
      setIsLoading(true);
      setResult(null);

      const response = await fetch('/api/migrations/registration-fields-table', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok) {
        setResult({
          success: true,
          message: data.message || 'Migration applied successfully',
        });
      } else {
        setResult({
          success: false,
          message: data.message || 'Failed to apply migration',
        });
      }
    } catch (error) {
      setResult({
        success: false,
        message: error instanceof Error ? error.message : 'An unexpected error occurred',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container py-10">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle>Registration Fields Migration</CardTitle>
          <CardDescription>
            Create the registration_fields table in the database
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            This migration will create the registration_fields table if it doesn&apos;t exist. This table is used to store custom registration fields for events.
          </p>

          {result && (
            <Alert variant={result.success ? 'default' : 'destructive'} className="mb-4">
              {result.success ? (
                <CheckCircle2 className="h-4 w-4" />
              ) : (
                <AlertCircle className="h-4 w-4" />
              )}
              <AlertTitle>{result.success ? 'Success' : 'Error'}</AlertTitle>
              <AlertDescription>{result.message}</AlertDescription>
            </Alert>
          )}
        </CardContent>
        <CardFooter>
          <Button onClick={applyMigration} disabled={isLoading} className="w-full">
            {isLoading ? 'Applying Migration...' : 'Apply Migration'}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
