"use client"

import { Accordion, Accordion<PERSON><PERSON>, Accordion<PERSON>rigger, AccordionContent } from '@/components/ui/accordion'

// FAQ data structure for easier maintenance
const faqItems = [
  {
    id: 'response-time',
    question: 'How quickly can I expect a response?',
    answer: 'We aim to respond to all inquiries within 24-48 hours during business days.'
  },
  {
    id: 'technical-issues',
    question: "I'm having technical issues with the platform. What should I do?",
    answer: 'Please select "Technical Issue" in the subject field and provide as much detail as possible about the problem you\'re experiencing.'
  },
  {
    id: 'partnerships',
    question: 'Do you offer partnership opportunities?',
    answer: 'Yes! We\'re always open to exploring partnerships that align with our mission. Select "Partnership" in the subject field, and we\'ll connect you with our partnerships team.'
  },
  {
    id: 'office-visit',
    question: 'Can I visit your office?',
    answer: 'We welcome visitors by appointment. Please contact us in advance to schedule a visit.'
  },
  {
    id: 'event-support',
    question: 'What kind of support do you offer for event organizers?',
    answer: 'We provide comprehensive support for event organizers, including technical assistance, marketing guidance, and attendee management tools. Select "Event Support" in the contact form for specialized assistance.'
  }
]

export default function FaqSection() {
  return (
    <section className="mt-24 max-w-4xl mx-auto">
      <h2 className="text-3xl font-bold mb-12 text-center">Frequently Asked Questions</h2>
      
      <Accordion className="space-y-4">
        {faqItems.map((item) => (
          <AccordionItem key={item.id} value={item.id} className="bg-white rounded-lg shadow-sm overflow-hidden mb-4">
            <AccordionTrigger className="px-6 py-4 text-left font-semibold text-lg">
              {item.question}
            </AccordionTrigger>
            <AccordionContent className="px-6 text-gray-600">
              {item.answer}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </section>
  )
} 