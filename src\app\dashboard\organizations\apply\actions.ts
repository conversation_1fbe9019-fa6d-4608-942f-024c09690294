'use server';

import { isAdmin } from "../../../../lib/auth";
import { OrganizationApplication, OrganizationApplicationData, UserRole } from "../../../../types/roles";
import { v4 as uuidv4 } from 'uuid';
import { revalidatePath } from "next/cache";
import { z } from 'zod';
import { createClient } from '@/lib/supabase/pages-client';
import { adminSupabase } from '@/lib/supabase/admin-client';

// Validation schemas
const applicationDataSchema = z.object({
  // Company Information
  organizationName: z.string().min(2, "Organization name is required"),
  organizationType: z.string().min(1, "Organization type is required"),
  description: z.string().min(20, "Please provide a more detailed description"),
  website: z.string().url().optional().nullable(),
  logo: z.string().optional().nullable(),

  // Contact Details
  contactName: z.string().min(2, "Contact name is required"),
  contactEmail: z.string().email("Please enter a valid email"),
  contactPhone: z.string().min(10, "Please enter a valid phone number"),
  address: z.object({
    street: z.string().min(2, "Street address is required"),
    city: z.string().min(2, "City is required"),
    state: z.string().min(2, "State/Province is required"),
    postalCode: z.string().min(2, "Postal code is required"),
    country: z.string().min(2, "Country is required"),
  }),

  // Event Experience
  experienceDescription: z.string().min(20, "Please provide more details about your experience").optional(),
  pastEvents: z.array(
    z.object({
      name: z.string(),
      date: z.string(),
      attendees: z.number(),
      description: z.string(),
      photos: z.array(z.string()).optional(),
    })
  ).optional(),

  // Documents
  documents: z.object({
    businessRegistration: z.string().optional(),
    taxDocuments: z.string().optional(),
    identificationDocument: z.string().optional(),
    otherDocuments: z.array(z.string()).optional(),
  }).optional(),

  // Completed form steps
  completedSteps: z.array(z.string()),
});

const applicationDraftSchema = z.object({
  userId: z.string().min(1, "User ID is required"),
  data: applicationDataSchema,
  currentStep: z.number().int().min(1)
});

const applicationSubmitSchema = z.object({
  userId: z.string().min(1, "User ID is required"),
  data: applicationDataSchema
});

const applicationReviewSchema = z.object({
  applicationId: z.string().min(1, "Application ID is required"),
  reason: z.string().optional()
});

const userIdSchema = z.object({
  userId: z.string().min(1, "User ID is required")
});

const applicationIdSchema = z.object({
  applicationId: z.string().min(1, "Application ID is required")
});

const applicationFilterSchema = z.object({
  status: z.enum(['all', 'draft', 'submitted', 'approved', 'rejected']).default('all'),
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(10),
  sortBy: z.enum(['created_at', 'updated_at', 'organization_name']).default('updated_at'),
  sortDirection: z.enum(['asc', 'desc']).default('desc')
});

// Mock database for applications
const applications: Record<string, OrganizationApplication> = {};

/**
 * Save a draft of the application
 * @param userId The user ID
 * @param data The application data
 * @param currentStep The current step
 * @returns A message indicating success or failure
 */
export async function saveApplicationDraft(
  userId: string,
  data: OrganizationApplicationData,
  currentStep: number
): Promise<{ success: boolean; message: string }> {
  try {
    // Validate input
    const parsedData = {
      userId,
      data,
      currentStep
    };
    applicationDraftSchema.parse(parsedData);

    const now = new Date().toISOString();

    // Add the current step to the completed steps
    const completedSteps = [...(data.completedSteps || [])];
    if (!completedSteps.includes(`step-${currentStep}`)) {
      completedSteps.push(`step-${currentStep}`);
    }

    // Get Supabase connection
    const supabase = await adminSupabase();

    // Check if application already exists in DB
    const { data: existingApp } = await supabase
      .from('organization_applications')
      .select('id')
      .eq('user_id', userId)
      .maybeSingle();

    if (existingApp) {
      // Update existing application
      const { error } = await supabase
        .from('organization_applications')
        .update({
          data: {
            ...data,
            completedSteps,
          },
          updated_at: now,
          status: 'draft'
        })
        .eq('user_id', userId);

      if (error) {
        return {
          success: false,
          message: 'Failed to save application draft to database',
        };
      }
    } else {
      // Create new application
      const { error } = await supabase
        .from('organization_applications')
        .insert({
          id: uuidv4(),
          user_id: userId,
          status: 'draft',
          data: {
            ...data,
            completedSteps,
          },
          created_at: now,
          updated_at: now,
        });

      if (error) {
        return {
          success: false,
          message: 'Failed to create application draft in database',
        };
      }
    }

    // Revalidate paths
    revalidatePath(`/dashboard/organizations/apply`);

    return {
      success: true,
      message: 'Application draft saved successfully',
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        message: `Validation error: ${error.errors?.[0]?.message || 'Invalid input data'}`,
      };
    }
    return {
      success: false,
      message: 'Failed to save application draft',
    };
  }
}

/**
 * Create an organizer application
 * @param userId The user ID
 * @param data The application data
 * @returns A message indicating success or failure
 */
export async function createOrganizerApplication(
  userId: string,
  data: OrganizationApplicationData
): Promise<{ success: boolean; message: string }> {
  try {
    // Validate input
    const parsedData = {
      userId,
      data
    };
    applicationSubmitSchema.parse(parsedData);

    const now = new Date().toISOString();
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.user) {
      return {
        success: false,
        message: 'User not authenticated',
      };
    }

    // Get Supabase admin connection
    const adminClient = await adminSupabase();

    // Check if application already exists
    const { data: existingApp } = await adminClient
      .from('organization_applications')
      .select('id')
      .eq('user_id', userId)
      .maybeSingle();

    if (existingApp) {
      // Update existing application
      const { error } = await adminClient
        .from('organization_applications')
        .update({
          status: 'submitted',
          data,
          updated_at: now,
          submitted_at: now,
        })
        .eq('user_id', userId);

      if (error) {
        console.error('Error updating application:', error);
        return {
          success: false,
          message: 'Failed to submit application to database',
        };
      }
    } else {
      // Create new application
      const { error } = await adminClient
        .from('organization_applications')
        .insert({
          id: uuidv4(),
          user_id: userId,
          status: 'submitted',
          data,
          created_at: now,
          updated_at: now,
          submitted_at: now,
        });

      if (error) {
        console.error('Error creating application:', error);
        return {
          success: false,
          message: 'Failed to create application in database',
        };
      }
    }

    // Notify admins (this could be implemented with a webhook or email service)
    // For example: await notifyAdminsOfNewApplication(userId, data.organizationName);

    // Revalidate paths
    revalidatePath(`/dashboard/organizations`);
    revalidatePath(`/admin/applications`);

    return {
      success: true,
      message: 'Application submitted successfully',
    };
  } catch (error) {
    console.error('Error submitting application:', error);
    if (error instanceof z.ZodError) {
      return {
        success: false,
        message: `Validation error: ${error.errors?.[0]?.message || 'Invalid input data'}`,
      };
    }
    return {
      success: false,
      message: 'Failed to submit application',
    };
  }
}

/**
 * Approve an organizer application
 * @param applicationId The application ID
 * @returns A message indicating success or failure
 */
export async function approveApplication(
  applicationId: string
): Promise<{ success: boolean; message: string }> {
  // Verify the current user is an admin
  if (!(await isAdmin())) {
    return { success: false, message: "Unauthorized. Only admins can approve applications." };
  }

  try {
    // Validate input
    applicationIdSchema.parse({ applicationId });

    // Get Supabase connection
    const supabase = await adminSupabase();

    // Get the application
    const { data: application, error: fetchError } = await supabase
      .from('organization_applications')
      .select('*')
      .eq('id', applicationId)
      .single();

    if (fetchError || !application) {
      return { success: false, message: "Application not found" };
    }

    // Update the application status
    const now = new Date().toISOString();
    const authClient = await createClient();
    const { data: { session } } = await authClient.auth.getSession();
    const adminUserId = session?.user?.id;

    // Prepare update data with proper null handling
    const updateData: Record<string, string | null> = {
      status: 'approved',
      reviewed_at: now
    };

    // Add reviewed_by if available, otherwise set to null
    updateData.reviewed_by = adminUserId || null;

    const { error: updateError } = await supabase
      .from('organization_applications')
      .update(updateData)
      .eq('id', applicationId);

    if (updateError) {
      console.error('Error approving application:', updateError);
      return {
        success: false,
        message: "Failed to update application status in database"
      };
    }

    // Update the user's role in Supabase Auth
    try {
      // Get the auth user ID from the application user
      // First get the user from the users table
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('id', application.user_id)
        .single();

      if (userError || !userData) {
        console.error("Error getting user data:", userError);
        return {
          success: false,
          message: "Application approved but failed to update user role. Please try again."
        };
      }

      // Use type assertion to access auth_user_id
      const typedUserData = userData as { auth_user_id?: string };
      const authUserId = typedUserData.auth_user_id;

      if (!authUserId) {
        console.error("No auth user ID found for user");
        return {
          success: false,
          message: "Application approved but failed to update user role. Please try again."
        };
      }

      // Update the user's metadata in Supabase Auth
      const { error: authError } = await supabase.auth.admin.updateUserById(
        authUserId,
        {
          user_metadata: {
            role: UserRole.EVENT_ORGANIZER,
            organizationId: applicationId
          }
        }
      );

      if (authError) {
        console.error("Error updating user role in Supabase Auth:", authError);
        return {
          success: false,
          message: "Application approved but failed to update user role. Please try again."
        };
      }
    } catch (authError) {
      console.error("Error updating user role:", authError);
      return {
        success: false,
        message: "Application approved but failed to update user role. Please try again."
      };
    }

    // Also update the role in Supabase users table if it exists
    try {
      await supabase
        .from('users')
        .update({
          role: UserRole.EVENT_ORGANIZER,
          organization_id: applicationId,
        })
        .eq('id', application.user_id);
    } catch (userUpdateError) {
      console.error("Error updating user in Supabase:", userUpdateError);
      // Continue execution even if this update fails
    }

    // Revalidate paths
    revalidatePath(`/admin/applications`);
    revalidatePath(`/admin/applications/${applicationId}`);
    revalidatePath(`/dashboard/organizations`);

    return {
      success: true,
      message: "Application approved and user promoted to event organizer"
    };
  } catch (error) {
    console.error("Error approving application:", error);
    if (error instanceof z.ZodError) {
      return {
        success: false,
        message: `Validation error: ${error.errors?.[0]?.message || 'Invalid input data'}`,
      };
    }
    return {
      success: false,
      message: "Failed to approve application. Please try again later."
    };
  }
}

/**
 * Reject an organizer application
 * @param applicationId The application ID
 * @param reason The reason for rejection
 * @returns A message indicating success or failure
 */
export async function rejectApplication(
  applicationId: string,
  reason: string
): Promise<{ success: boolean; message: string }> {
  // Verify the current user is an admin
  if (!(await isAdmin())) {
    return { success: false, message: "Unauthorized. Only admins can reject applications." };
  }

  try {
    // Validate input
    applicationReviewSchema.parse({ applicationId, reason });

    // Get Supabase connection
    const supabase = await adminSupabase();

    // Get the application
    const { data: application, error: fetchError } = await supabase
      .from('organization_applications')
      .select('*')
      .eq('id', applicationId)
      .single();

    if (fetchError || !application) {
      return { success: false, message: "Application not found" };
    }

    // Update the application status
    const now = new Date().toISOString();
    const authClient = await createClient();
    const { data: { session } } = await authClient.auth.getSession();
    const adminUserId = session?.user?.id;

    // Prepare update data with proper null handling
    const updateData: Record<string, string | null> = {
      status: 'rejected',
      reviewed_at: now,
      rejection_reason: reason
    };

    // Add reviewed_by if available, otherwise set to null
    updateData.reviewed_by = adminUserId || null;

    const { error: updateError } = await supabase
      .from('organization_applications')
      .update(updateData)
      .eq('id', applicationId);

    if (updateError) {
      console.error('Error rejecting application:', updateError);
      return {
        success: false,
        message: "Failed to update application status in database"
      };
    }

    // Revalidate paths
    revalidatePath(`/admin/applications`);
    revalidatePath(`/admin/applications/${applicationId}`);
    revalidatePath(`/dashboard/organizations`);

    return {
      success: true,
      message: "Application rejected"
    };
  } catch (error) {
    console.error("Error rejecting application:", error);
    if (error instanceof z.ZodError) {
      return {
        success: false,
        message: `Validation error: ${error.errors?.[0]?.message || 'Invalid input data'}`,
      };
    }
    return {
      success: false,
      message: "Failed to reject application. Please try again later."
    };
  }
}

/**
 * Get all applications with pagination and filtering
 */
export async function getApplications(
  filters: z.infer<typeof applicationFilterSchema> = {
    status: 'all',
    page: 1,
    limit: 10,
    sortBy: 'updated_at',
    sortDirection: 'desc'
  }
): Promise<{ applications: OrganizationApplication[], pagination: { total: number; page: number; limit: number; totalPages: number } }> {
  // Verify the current user is an admin
  if (!(await isAdmin())) {
    return { applications: [], pagination: { total: 0, page: 1, limit: 10, totalPages: 0 } };
  }

  try {
    // Validate and parse filters
    const {
      status = 'all',
      page = 1,
      limit = 10,
      sortBy = 'updated_at',
      sortDirection = 'desc'
    } = applicationFilterSchema.parse(filters);

    // Get Supabase connection
    const supabase = await adminSupabase();

    // Build query
    let query = supabase
      .from('organization_applications')
      .select('*', { count: 'exact' });

    // Apply status filter if not 'all'
    if (status !== 'all') {
      query = query.eq('status', status);
    }

    // Get count first
    const { count, error: countError } = await query;

    if (countError) {
      console.error('Error counting applications:', countError);
      return {
        applications: [],
        pagination: { total: 0, page, limit, totalPages: 0 }
      };
    }

    // Apply pagination and sorting
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    const { data: applications, error: fetchError } = await query
      .order(sortBy, { ascending: sortDirection === 'asc' })
      .range(from, to);

    if (fetchError) {
      console.error('Error fetching applications:', fetchError);
      return {
        applications: [],
        pagination: { total: count || 0, page, limit, totalPages: Math.ceil((count || 0) / limit) }
      };
    }

    // Map the database records to the OrganizationApplication format
    const mappedApplications: OrganizationApplication[] = applications.map(app => ({
      id: app.id,
      userId: app.user_id,
      status: app.status as 'draft' | 'submitted' | 'approved' | 'rejected',
      data: app.data as OrganizationApplicationData,
      createdAt: app.created_at,
      updatedAt: app.updated_at,
      submittedAt: app.submitted_at || null,
      reviewedAt: app.reviewed_at || null,
      reviewedBy: app.reviewed_by || null,
      rejectionReason: app.rejection_reason || null
    }));

    return {
      applications: mappedApplications,
      pagination: {
        total: count || 0,
        page,
        limit,
        totalPages: Math.ceil((count || 0) / limit)
      }
    };
  } catch (error) {
    console.error('Error getting applications:', error);
    return {
      applications: [],
      pagination: { total: 0, page: 1, limit: 10, totalPages: 0 }
    };
  }
}

/**
 * Get a user's application
 * @param userId The user ID
 * @returns The user's application or null if not found
 */
export async function getUserApplication(userId: string): Promise<OrganizationApplication | null> {
  try {
    // Validate input
    userIdSchema.parse({ userId });

    // Get Supabase connection
    const supabase = await adminSupabase();

    // Check if the table exists first
    const { error: tableCheckError } = await supabase
      .from('organization_applications')
      .select('id')
      .limit(1);

    // If there's an error with the table check, the table might not exist yet
    if (tableCheckError) {
      console.warn('organization_applications table might not exist yet:', tableCheckError.message);
      return null;
    }

    // Get the user's application
    const { data, error } = await supabase
      .from('organization_applications')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle();

    if (error) {
      console.error('Error fetching user application:', error);
      return null;
    }

    if (!data) {
      return null;
    }

    // Convert database record to OrganizationApplication type
    return {
      id: data.id,
      userId: data.user_id,
      status: data.status as 'draft' | 'submitted' | 'approved' | 'rejected',
      data: data.data as OrganizationApplicationData,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      submittedAt: data.submitted_at || null,
      reviewedAt: data.reviewed_at || null,
      reviewedBy: data.reviewed_by || null,
      rejectionReason: data.rejection_reason || null
    };
  } catch (error) {
    console.error('Error getting user application:', error);
    return null;
  }
}

/**
 * Get application by ID
 */
export async function getApplicationById(applicationId: string): Promise<OrganizationApplication | null> {
  try {
    // Validate input
    applicationIdSchema.parse({ applicationId });

    // Check if user is admin or the owner of the application
    const authClient = await createClient();
    const { data: { session } } = await authClient.auth.getSession();
    if (!session || !session.user) return null;

    // Get Supabase connection
    const supabase = await adminSupabase();

    // Get the application
    const { data, error } = await supabase
      .from('organization_applications')
      .select('*')
      .eq('id', applicationId)
      .single();

    if (error || !data) {
      console.error('Error fetching application:', error);
      return null;
    }

    // Check if user is admin or the owner
    const isAppOwner = data.user_id === session.user.id;
    const isUserAdmin = await isAdmin();

    if (!isAppOwner && !isUserAdmin) {
      console.error('Unauthorized access to application');
      return null;
    }

    // Convert database record to OrganizationApplication type
    return {
      id: data.id,
      userId: data.user_id,
      status: data.status as 'draft' | 'submitted' | 'approved' | 'rejected',
      data: data.data as OrganizationApplicationData,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
      submittedAt: data.submitted_at || null,
      reviewedAt: data.reviewed_at || null,
      reviewedBy: data.reviewed_by || null,
      rejectionReason: data.rejection_reason || null
    };
  } catch (error) {
    console.error('Error getting application:', error);
    return null;
  }
}