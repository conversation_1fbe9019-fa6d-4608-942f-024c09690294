import { logger } from '@/lib/logger';
/**
 * Cookie consent utilities
 * Handles storing and retrieving cookie consent preferences
 */

export type CookieConsentOptions = {
  necessary: boolean; // Always true, required cookies
  analytics: boolean; // Analytics/performance cookies
  marketing: boolean; // Marketing/advertising cookies
  preferences: boolean; // Preference/functionality cookies
};

export type CookieConsentData = {
  accepted: boolean;
  options: CookieConsentOptions;
  expires: number; // Timestamp when consent expires
  version: number; // Policy version, increment when policy changes
};

// Current cookie policy version - increment when policy changes significantly
export const COOKIE_POLICY_VERSION = 1;

// Default consent duration - 12 months in milliseconds
export const DEFAULT_CONSENT_DURATION = 365 * 24 * 60 * 60 * 1000;

// LocalStorage key for cookie consent
export const CONSENT_STORAGE_KEY = 'fuiyoo_cookie_consent';

/**
 * Get the current cookie consent data
 * @returns The current consent data or null if not set
 */
export function getConsentData(): CookieConsentData | null {
  if (typeof window === 'undefined') return null;
  
  try {
    const _data = localStorage.getItem(CONSENT_STORAGE_KEY);
    if (!data) return null;
    
    return JSON.parse(data) as CookieConsentData;
  } catch (_error) {
    logger.error('Error retrieving cookie consent data:', _error);
    return null;
  }
}

/**
 * Check if cookie consent is valid
 * @returns True if consent is valid, false otherwise
 */
export function isConsentValid(): boolean {
  const consent = getConsentData();
  
  if (!consent) return false;
  
  // Check if consent has expired
  const now = new Date().getTime();
  if (now > consent.expires) return false;
  
  // Check if policy version has changed
  if (consent.version !== COOKIE_POLICY_VERSION) return false;
  
  return consent.accepted;
}

/**
 * Save cookie consent data
 * @param options Cookie consent options
 * @param accepted Whether consent was accepted
 */
export function saveConsent(options: CookieConsentOptions, accepted: boolean = true): void {
  if (typeof window === 'undefined') return;
  
  try {
    // Calculate expiration date (12 months from now)
    const expires = new Date().getTime() + DEFAULT_CONSENT_DURATION;
    
    const consentData: CookieConsentData = {
      accepted,
      options,
      expires,
      version: COOKIE_POLICY_VERSION,
    };
    
    localStorage.setItem(CONSENT_STORAGE_KEY, JSON.stringify(consentData));
  } catch (_error) {
    logger.error('Error saving cookie consent data:', _error);
  }
}

/**
 * Accept all cookies
 */
export function acceptAllCookies(): void {
  saveConsent({
    necessary: true,
    analytics: true,
    marketing: true,
    preferences: true,
  });
}

/**
 * Accept only necessary cookies
 */
export function acceptNecessaryCookies(): void {
  saveConsent({
    necessary: true,
    analytics: false,
    marketing: false,
    preferences: false,
  });
}

/**
 * Update cookie consent options
 * @param options New cookie consent options
 */
export function updateConsentOptions(options: Partial<CookieConsentOptions>): void {
  const currentConsent = getConsentData();
  
  if (!currentConsent) {
    // If no consent exists, create a new one with default values
    saveConsent({
      necessary: true,
      analytics: options.analytics ?? false,
      marketing: options.marketing ?? false,
      preferences: options.preferences ?? false,
    });
    return;
  }
  
  // Update existing consent with new options
  saveConsent({
    ...currentConsent.options,
    ...options,
    necessary: true, // Always true
  });
}

/**
 * Clear cookie consent data
 */
export function clearConsent(): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.removeItem(CONSENT_STORAGE_KEY);
  } catch (_error) {
    logger.error('Error clearing cookie consent data:', _error);
  }
}

/**
 * Get the expiration date of the current consent
 * @returns The expiration date or null if no consent
 */
export function getConsentExpiration(): Date | null {
  const consent = getConsentData();
  
  if (!consent) return null;
  
  return new Date(consent.expires);
}

/**
 * Format the expiration date as a string
 * @returns Formatted expiration date or empty string
 */
export function getFormattedExpiration(): string {
  const expirationDate = getConsentExpiration();
  
  if (!expirationDate) return '';
  
  return expirationDate.toLocaleDateString(undefined, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}
