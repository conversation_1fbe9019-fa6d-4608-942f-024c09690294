import { logger } from '@/lib/logger';
import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { applyRateLimit } from '../../../../lib/rate-limit'

// This API endpoint calculates and returns profile completion percentage
export async function GET(request: Request) {
  try {
    // Apply rate limiting - 20 requests per minute
    const rateLimitResponse = await applyRateLimit(request, {
      limit: 20,
      window: 60, // 1 minute
    });

    if (rateLimitResponse) {
      return rateLimitResponse;
    }

    const supabase = await createClient();

    // Get the current session
    const { data: { session } } = await supabase.auth.getSession();

    // Also get the user directly for more reliable auth check
    const { data: { user: authUser } } = await supabase.auth.getUser();

    if (!session && !authUser) {
      logger.info('[DEBUG] Profile Completion API - No session or auth user found');
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Use either session user or auth user
    const currentAuthUserId = (session?.user?.id || authUser?.id);

    if (!currentAuthUserId) {
      logger.info('[DEBUG] Profile Completion API - No user ID found in session or auth');
      return new NextResponse('Unauthorized', { status: 401 });
    }

    logger.info('[DEBUG] Profile Completion API - Auth user ID:', currentAuthUserId);

    // Get query parameters
    const url = new URL(request.url);
    const requestedUserId = url.searchParams.get('userId');
    logger.info('[DEBUG] Profile Completion API - Requested user ID:', requestedUserId);

    // First try to find user by auth_user_id
    const { data: userData, error: userIdError } = await supabase
      .from('users')
      .select('id, role')
      .eq('auth_user_id', currentAuthUserId)
      .single();

    // If not found by auth_user_id, try by email as fallback
    let userId, userRole;

    if (userIdError || !userData) {
      logger.info('[DEBUG] Profile Completion API - User not found by auth_user_id, trying email');

      // Try to find by email if available
      if (authUser?.email) {
        const { data: userByEmail, error: emailError } = await supabase
          .from('users')
          .select('id, role')
          .eq('email', authUser.email)
          .single();

        if (emailError || !userByEmail) {
          logger.error('[DEBUG] Profile Completion API - User not found by email either:', emailError);
          return new NextResponse("User not found", { status: 404 });
        }

        // Use the user found by email
        userId = userByEmail.id;
        userRole = userByEmail.role;

        // Update the auth_user_id for future queries
        logger.info('[DEBUG] Profile Completion API - Updating auth_user_id for user:', userId);
        const { error: updateError } = await supabase
          .from('users')
          .update({ auth_user_id: currentAuthUserId })
          .eq('id', userId);

        if (updateError) {
          logger.error('[DEBUG] Profile Completion API - Error updating auth_user_id:', updateError);
        }
      } else {
        logger.error('[DEBUG] Profile Completion API - No email available to find user');
        return new NextResponse("User not found", { status: 404 });
      }
    } else {
      // Use the user found by auth_user_id
      userId = userData.id;
      userRole = userData.role;
    }

    // Only allow users to request their own profile completion
    // or admins to access any profile
    if (requestedUserId && requestedUserId !== userId) {
      // Verify the user is an admin before allowing access to other profiles
      if (userRole !== 'admin' && userRole !== 'super_admin') {
        return new NextResponse('Forbidden: Cannot access other user profiles', { status: 403 });
      }
    }

    // Get user data to calculate completion percentage
    const { data: user, error: _error } = await supabase
      .from('users')
      .select('*')
      .eq('id', requestedUserId || userId)
      .single();

    if (error || !user) {
      logger.error("Error fetching user profile:", _error);
      return new NextResponse("Error fetching user profile", { status: 500 });
    }

    // Define profile fields that contribute to completion score
    const requiredFields = [
      !!user.first_name,                // First name
      !!user.last_name,                 // Last name
      !!user.email,                     // Email
      !!user.username,                  // Username
      !!user.gender,                    // Gender
      !!user.country,                   // Country
      !!user.nationality,               // Nationality
      !!user.contactNo,                 // Phone
      !!user.avatar,                    // Profile image
    ];

    // Calculate completion percentage
    const completedFields = requiredFields.filter(Boolean).length;
    const totalFields = requiredFields.length;
    const completionPercentage = Math.round((completedFields / totalFields) * 100);

    // Return the calculation
    return NextResponse.json({
      completionPercentage,
      completedFields,
      totalFields,
    })
  } catch (_error) {
    logger.error('Error calculating profile completion:', _error)
    return new NextResponse('Internal Server Error', { status: 500 })
  }
}