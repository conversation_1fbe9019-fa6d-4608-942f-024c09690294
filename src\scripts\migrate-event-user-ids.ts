import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { logger } from '@/lib/logger';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  logger.error('Missing environment variables. Please check your .env.local file.');
  process.exit(1);
}

// Create Supabase client with service role key for admin access
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function migrateEventUserIds() {
  logger.info('Starting migration of event user IDs...');

  // Get the user mapping
  const { data: users, error: userError } = await supabase
    .from('users')
    .select('id, auth_user_id, email')
    .eq('email', '<EMAIL>')
    .single();

  if (userError || !users) {
    logger.error('Error fetching user:', userError);
    return;
  }

  logger.info(`Found user: ${users.email} with ID: ${users.id} and auth_user_id: ${users.auth_user_id}`);

  // Get all draft events with Clerk user IDs
  const { data: events, error: eventsError } = await supabase
    .from('events')
    .select('id, title, organizer_id, created_by, clerk_user_id')
    .eq('status', 'draft');

  if (eventsError) {
    logger.error('Error fetching events:', eventsError);
    return;
  }

  logger.info(`Found ${events.length} draft events`);

  // Update each event
  for (const event of events) {
    logger.info(`Processing event: ${event.id} - ${event.title}`);
    logger.info(`  Current organizer_id: ${event.organizer_id}`);
    logger.info(`  Current created_by: ${event.created_by}`);

    const { error: updateError } = await supabase
      .from('events')
      .update({
        organizer_id: users.id,
        created_by: users.auth_user_id
      })
      .eq('id', event.id);

    if (updateError) {
      logger.error(`Error updating event ${event.id}:`, updateError);
    } else {
      logger.info(`  Updated event ${event.id} successfully`);
    }
  }

  logger.info('Migration completed');
}

// Run the migration
migrateEventUserIds()
  .then(() => {
    logger.info('Script execution completed');
    process.exit(0);
  })
  .catch((error) => {
    logger.error('Script execution failed:', error);
    process.exit(1);
  });
