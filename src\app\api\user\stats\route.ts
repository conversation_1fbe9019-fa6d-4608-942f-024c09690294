import { NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/pages-client';
import { EventRepository } from '@/repositories/event-repository';
import { UserRepository } from '@/repositories/user-repository';
import { logger } from '@/lib/logger';

export const dynamic = 'force-dynamic';

/**
 * Get user statistics for the dashboard
 * Returns counts of events, organizations, tickets, and payments
 */
export async function GET() {
  try {
    const supabase = await createClient();

    // Get the current user
    const { data: { user: authUser } } = await supabase.auth.getUser();

    if (!authUser) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Get the internal user ID from the users table
    const { data: userData, error: userIdError } = await supabase
      .from('users')
      .select('id')
      .eq('auth_user_id', authUser.id)
      .single();

    if (userIdError || !userData) {
      logger.error("Error fetching user ID:", userIdError);
      return new NextResponse("User not found", { status: 404 });
    }

    const userId = userData.id;

    // Initialize repositories
    const eventRepository = new EventRepository();
    const userRepository = new UserRepository();

    // Get event counts
    const eventCounts = await eventRepository.getEventCountsByStatus(userId);

    // Get organization count
    const organizationCount = await userRepository.getUserOrganizationCount(userId);

    // Get ticket count (registrations)
    const { data: ticketData, error: ticketError } = await supabase
      .from('registrations')
      .select('count')
      .eq('user_id', userId)
      .single();

    const ticketCount = ticketError ? 0 : (ticketData?.count || 0);

    // Get payment count
    const { data: paymentData, error: paymentError } = await supabase
      .from('payments')
      .select('count')
      .eq('user_id', userId)
      .single();

    const paymentCount = paymentError ? 0 : (paymentData?.count || 0);

    // Return all stats
    return NextResponse.json({
      events: {
        total: eventCounts.total,
        draft: eventCounts.draft,
        published: eventCounts.published,
        cancelled: eventCounts.cancelled,
        completed: eventCounts.completed
      },
      organizations: organizationCount,
      tickets: ticketCount,
      payments: paymentCount
    });
  } catch (error) {
    logger.error("Error fetching user stats:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
