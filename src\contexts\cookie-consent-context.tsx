'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'


import {
  CookieConsentOptions,
  getConsentData,
  isConsentValid,
  saveConsent,
  acceptAllCookies,
  acceptNecessaryCookies,
  updateConsentOptions,
  clearConsent,
  getFormattedExpiration,
} from '@/lib/cookies/consent'

type CookieConsentContextType = {
  consentGiven: boolean
  showBanner: boolean
  cookieOptions: CookieConsentOptions
  acceptAll: () => void
  acceptNecessary: () => void
  updateOptions: (options: Partial<CookieConsentOptions>) => void
  resetConsent: () => void
  openPreferences: () => void
  closePreferences: () => void
  showPreferences: boolean
  expirationDate: string
}

const defaultCookieOptions: CookieConsentOptions = {
  necessary: true,
  analytics: false,
  marketing: false,
  preferences: false,
}

const CookieConsentContext = createContext<CookieConsentContextType>({
  consentGiven: false,
  showBanner: false,
  cookieOptions: defaultCookieOptions,
  acceptAll: () => {},
  acceptNecessary: () => {},
  updateOptions: () => {},
  resetConsent: () => {},
  openPreferences: () => {},
  closePreferences: () => {},
  showPreferences: false,
  expirationDate: '',
})

export const useCookieConsent = () => useContext(CookieConsentContext)

export function CookieConsentProvider({ children }: { children: ReactNode }) {
  const [consentGiven, setConsentGiven] = useState(false)
  const [showBanner, setShowBanner] = useState(false)
  const [showPreferences, setShowPreferences] = useState(false)
  const [cookieOptions, setCookieOptions] = useState<CookieConsentOptions>(defaultCookieOptions)
  const [expirationDate, setExpirationDate] = useState('')

  // Check if consent is valid on mount
  useEffect(() => {
    // Don't show banner during SSR
    if (typeof window === 'undefined') return
    
    // Check if consent is valid
    const valid = isConsentValid()
    setConsentGiven(valid)
    
    // If consent is not valid, show the banner
    setShowBanner(!valid)
    
    // Get current consent data
    const consentData = getConsentData()
    if (consentData) {
      setCookieOptions(consentData.options)
    }
    
    // Get expiration date
    setExpirationDate(getFormattedExpiration())
  }, [])

  // Accept all cookies
  const acceptAll = () => {
    acceptAllCookies()
    setConsentGiven(true)
    setShowBanner(false)
    setShowPreferences(false)
    setCookieOptions({
      necessary: true,
      analytics: true,
      marketing: true,
      preferences: true,
    })
    setExpirationDate(getFormattedExpiration())
  }

  // Accept only necessary cookies
  const acceptNecessary = () => {
    acceptNecessaryCookies()
    setConsentGiven(true)
    setShowBanner(false)
    setShowPreferences(false)
    setCookieOptions({
      necessary: true,
      analytics: false,
      marketing: false,
      preferences: false,
    })
    setExpirationDate(getFormattedExpiration())
  }

  // Update cookie options
  const updateOptions = (options: Partial<CookieConsentOptions>) => {
    const newOptions = {
      ...cookieOptions,
      ...options,
      necessary: true, // Always true
    }
    
    updateConsentOptions(newOptions)
    setCookieOptions(newOptions)
    setConsentGiven(true)
    setShowBanner(false)
    setExpirationDate(getFormattedExpiration())
  }

  // Reset consent
  const resetConsent = () => {
    clearConsent()
    setConsentGiven(false)
    setShowBanner(true)
    setCookieOptions(defaultCookieOptions)
    setExpirationDate('')
  }

  // Open preferences
  const openPreferences = () => {
    setShowPreferences(true)
    setShowBanner(false)
  }

  // Close preferences
  const closePreferences = () => {
    setShowPreferences(false)
    
    // If consent hasn't been given, show the banner again
    if (!consentGiven) {
      setShowBanner(true)
    }
  }

  return (
    <CookieConsentContext.Provider
      value={{
        consentGiven,
        showBanner,
        cookieOptions,
        acceptAll,
        acceptNecessary,
        updateOptions,
        resetConsent,
        openPreferences,
        closePreferences,
        showPreferences,
        expirationDate,
      }}
    >
      {children}
    </CookieConsentContext.Provider>
  )
}
