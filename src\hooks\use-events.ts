import { useState, useEffect } from 'react';
import { Event } from '@/repositories/event-repository';
import { getAllEvents, getEventById, getUpcomingEvents, getPastEvents, getRecommendedEvents } from '@/lib/api/events';

// Type for event registration data
interface EventRegistration {
  id: string;
  eventId: string;
  userId: string;
  ticketNumber: string;
  qrCode: string;
  attendanceStatus: 'pending' | 'attended';
  collectionStatus: 'pending' | 'collected';
  paymentStatus: 'paid' | 'pending' | 'failed';
  formData: Record<string, unknown>;
  createdAt: string;
  updatedAt: string;
}

/**
 * Hook for fetching a single event by ID
 */
export function useEvent(id: string) {
  const [event, setEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchEvent = async () => {
      try {
        setLoading(true);
        const _data = await getEventById(id);
        setEvent(data);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to fetch event'));
        setEvent(null);
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchEvent();
    }
  }, [id]);

  return { event, loading, error };
}

/**
 * Hook for fetching all events
 */
export function useEvents() {
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchEvents = async () => {
      try {
        setLoading(true);
        const _data = await getAllEvents();
        setEvents(data);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to fetch events'));
        setEvents([]);
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();
  }, []);

  return { events, loading, error };
}

/**
 * Hook for fetching upcoming events for the current user
 */
export function useUpcomingEvents() {
  const [events, setEvents] = useState<(Event & { registration?: EventRegistration })[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchEvents = async () => {
      try {
        setLoading(true);
        const _data = await getUpcomingEvents();
        setEvents(data);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to fetch upcoming events'));
        setEvents([]);
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();
  }, []);

  return { events, loading, error };
}

/**
 * Hook for fetching past events for the current user
 */
export function usePastEvents() {
  const [events, setEvents] = useState<(Event & { registration?: EventRegistration })[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchEvents = async () => {
      try {
        setLoading(true);
        const _data = await getPastEvents();
        setEvents(data);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to fetch past events'));
        setEvents([]);
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();
  }, []);

  return { events, loading, error };
}

/**
 * Hook for fetching recommended events for the current user
 */
export function useRecommendedEvents() {
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchEvents = async () => {
      try {
        setLoading(true);
        const _data = await getRecommendedEvents();
        setEvents(data);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to fetch recommended events'));
        setEvents([]);
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();
  }, []);

  return { events, loading, error };
}