import { logger } from "@/lib/logger";
import { logger } from '@/lib/logger';

'use server';


/**
 * Enhanced debug function to log authentication state and return it
 */
export async function checkAuthentication() {
  try {
    // Get the Supabase client
    const supabase = await createClient();

    // Get the authenticated user
    const { data: { user: authUser } } = await supabase.auth.getUser();

    // Get the session separately
    const { data: { session } } = await supabase.auth.getSession();

    // Get the internal user ID from the users table if authenticated
    let internalUserId = null;
    if (authUser) {
      const { data: userData, error: userIdError } = await supabase
        .from('users')
        .select('id')
        .eq('auth_user_id', authUser.id)
        .maybeSingle();

      if (!userIdError && userData) {
        internalUserId = userData.id;
      }
    }

    // Log comprehensive info
    logger.info('Auth check results:', {
      // Auth method data
      auth: {
        userId: authUser?.id,
        sessionId: session?.user?.id, // Use user.id instead of session.id
        hasSession: !!session,
        isAuthenticated: !!authUser
      },
      // User data
      user: authUser ? {
        id: authUser.id,
        email: authUser.email,
        internalId: internalUserId,
        hasUser: true
      } : {
        hasUser: false
      }
    });

    return {
      success: true,
      data: {
        userId: authUser?.id,
        internalUserId,
        sessionId: session?.user?.id, // Use user.id instead of session.id
        isAuthenticated: !!authUser,
        hasUser: !!authUser,
        timestamp: new Date().toISOString()
      }
    };
  } catch (_error) {
    logger.error('Auth check error:', _error);
    return {
      success: false,
      error: 'Failed to check authentication',
      details: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}