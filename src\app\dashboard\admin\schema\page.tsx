'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { CheckCircle, AlertCircle, RefreshCw, Database } from 'lucide-react';

export default function SchemaManagementPage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const refreshSchema = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/refresh-schema', {
        method: 'POST',
      });

      const data = await response.json();
      setResult(data);

      if (!response.ok) {
        setError(data.error || 'Failed to refresh schema');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const fixSchema = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/schema-fix', {
        method: 'POST',
      });

      const data = await response.json();
      setResult(data);

      if (!response.ok) {
        setError(data.error || 'Failed to fix schema');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const clearNextCache = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/clear-cache', {
        method: 'POST',
      });

      const data = await response.json();
      setResult(data);

      if (!response.ok) {
        setError(data.error || 'Failed to clear cache');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container py-10">
      <h1 className="text-3xl font-bold mb-6">Schema Management</h1>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <RefreshCw className="h-5 w-5" />
              Basic Schema Refresh
            </CardTitle>
            <CardDescription>
              Simple schema cache refresh using pg_notify
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm mb-4">
              Use this option to refresh the schema cache using the standard notification mechanism.
              This is the least invasive option and should be tried first.
            </p>
          </CardContent>
          <CardFooter>
            <Button
              onClick={refreshSchema}
              disabled={loading}
              variant="outline"
            >
              {loading ? 'Refreshing...' : 'Refresh Schema Cache'}
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Complete Schema Fix
            </CardTitle>
            <CardDescription>
              Comprehensive schema fix with column type alterations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm mb-4">
              This option alters column types to force a complete schema cache refresh.
              Use this when the basic refresh doesn&apos;t resolve the issue.
            </p>
          </CardContent>
          <CardFooter>
            <Button
              onClick={fixSchema}
              disabled={loading}
            >
              {loading ? 'Fixing...' : 'Fix Schema Cache'}
            </Button>
          </CardFooter>
        </Card>
      </div>

      {error && (
        <Alert variant="destructive" className="mt-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {result && !error && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {result.success ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <AlertCircle className="h-5 w-5 text-amber-500" />
              )}
              {result.success ? 'Operation Successful' : 'Operation Completed with Issues'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-slate-100 p-4 rounded-md overflow-auto text-sm">
              {JSON.stringify(result, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  );
}