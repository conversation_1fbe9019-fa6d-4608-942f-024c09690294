import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors",
  {
    variants: {
      variant: {
        default:
          "bg-[hsl(var(--primary))] text-primary-foreground shadow hover:bg-[hsl(var(--primary-hover))] active:bg-[hsl(var(--primary-active))]",
        destructive:
          "bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90 active:bg-destructive/80",
        outline:
          "border border-input bg-background shadow-sm hover:bg-[hsl(var(--primary-50))] hover:text-[hsl(var(--primary))] hover:border-[hsl(var(--primary))]",
        secondary:
          "bg-[hsl(var(--secondary))] text-secondary-foreground shadow-sm hover:bg-[hsl(var(--secondary-hover))] active:bg-[hsl(var(--secondary-active))]",
        accent:
          "bg-[hsl(var(--accent))] text-accent-foreground shadow-sm hover:bg-[hsl(var(--accent-hover))] active:bg-[hsl(var(--accent-active))]",
        ghost: "hover:bg-[hsl(var(--primary-50))] hover:text-[hsl(var(--primary))]",
        link: "text-[hsl(var(--primary))] underline-offset-4 hover:underline",
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-10 rounded-md px-8",
        icon: "size-9 p-0",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
  VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(
          buttonVariants({ variant, size }),
          "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
          "disabled:pointer-events-none disabled:opacity-50",
          "[&_svg]:size-4 [&_svg]:shrink-0",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { buttonVariants }
