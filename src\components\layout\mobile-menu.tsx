'use client'

import { logger } from '@/lib/logger';
import Link from 'next/link'
import { X } from 'lucide-react'
import { Logo } from './Logo'
import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { signOut } from '@/lib/supabase/auth'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/auth-context'

interface MobileMenuProps {
  isOpen: boolean
  setIsOpen: (isOpen: boolean) => void
}

export function MobileMenu({ isOpen, setIsOpen }: MobileMenuProps) {
  const { isSignedIn, user, loading, refreshSession } = useAuth()
  const [isSigningOut, setIsSigningOut] = useState(false)
  const router = useRouter()

  // Refresh the session when the mobile menu is opened
  useEffect(() => {
    if (isOpen) {
      refreshSession()
    }
  }, [isOpen, refreshSession])

  const handleSignOut = async () => {
    try {
      setIsSigningOut(true)
      setIsOpen(false)
      await signOut()
      // The signOut function will handle the redirect
    } catch (_error) {
      logger.error('Error signing out:', _error)
      // Redirect to home page even if there&apos;s an error
      window.location.href = '/'
    } finally {
      setIsSigningOut(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 md:hidden">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-gray-600 bg-opacity-75 transition-opacity"
        onClick={() => setIsOpen(false)}
      />

      {/* Menu panel */}
      <div className="fixed inset-y-0 right-0 flex flex-col w-full max-w-xs bg-white shadow-xl">
        <div className="flex items-center justify-between h-16 px-4 border-b">
          <Logo />
          <button
            type="button"
            className="inline-flex items-center justify-center p-2 rounded-md text-gray-500 hover:text-gray-900 hover:bg-gray-100"
            onClick={() => setIsOpen(false)}
          >
            <span className="sr-only">Close menu</span>
            <X className="h-6 w-6" />
          </button>
        </div>

        <nav className="flex-1 px-4 py-6 overflow-y-auto">
          <div className="flex flex-col space-y-4">
            {/* Primary navigation links - always visible */}
            <Link
              href="/"
              className="text-base font-medium text-gray-900 hover:text-primary"
              onClick={() => setIsOpen(false)}
            >
              Home
            </Link>
            <Link
              href="/about"
              className="text-base font-medium text-gray-900 hover:text-primary"
              onClick={() => setIsOpen(false)}
            >
              About Us
            </Link>
            <Link
              href="/contact"
              className="text-base font-medium text-gray-900 hover:text-primary"
              onClick={() => setIsOpen(false)}
            >
              Contact Us
            </Link>

            {/* Authenticated user links */}
            {isSignedIn && (
              <>
                <Link
                  href="/dashboard"
                  className="text-base font-medium text-gray-900 hover:text-primary"
                  onClick={() => setIsOpen(false)}
                >
                  Dashboard
                </Link>
                <Link
                  href="/dashboard/profile"
                  className="text-base font-medium text-gray-900 hover:text-primary"
                  onClick={() => setIsOpen(false)}
                >
                  Profile
                </Link>
                <Link
                  href="/dashboard/events"
                  className="text-base font-medium text-gray-900 hover:text-primary"
                  onClick={() => setIsOpen(false)}
                >
                  Events
                </Link>
                <Link
                  href="/events/calendar"
                  className="text-base font-medium text-gray-900 hover:text-primary"
                  onClick={() => setIsOpen(false)}
                >
                  Calendar
                </Link>
                <Link
                  href="/admin"
                  className="text-base font-medium text-gray-900 hover:text-primary"
                  onClick={() => setIsOpen(false)}
                >
                  Admin
                </Link>
              </>
            )}

            {/* Legal pages */}
            <div className="py-2 border-t border-gray-200 mt-2">
              <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">
                Legal
              </h3>
              <div className="flex flex-col space-y-2">
                <Link
                  href="/terms"
                  className="text-sm text-gray-600 hover:text-primary"
                  onClick={() => setIsOpen(false)}
                >
                  Terms of Service
                </Link>
                <Link
                  href="/privacy-policy"
                  className="text-sm text-gray-600 hover:text-primary"
                  onClick={() => setIsOpen(false)}
                >
                  Privacy Policy
                </Link>
                <Link
                  href="/cookies"
                  className="text-sm text-gray-600 hover:text-primary"
                  onClick={() => setIsOpen(false)}
                >
                  Cookie Policy
                </Link>
              </div>
            </div>
          </div>

          {/* Auth buttons or user button */}
          <div className="mt-6 pt-6 border-t border-gray-200">
            {isSignedIn ? (
              <div className="flex flex-col space-y-4">
                <div className="flex items-center mb-2">
                  <span className="text-sm text-gray-500 mr-2">Signed in as:</span>
                  <span className="font-medium">{user?.first_name} {user?.last_name}</span>
                </div>
                <Link
                  href="/dashboard"
                  className="w-full flex items-center justify-center px-4 py-2 text-sm font-medium text-[hsl(var(--foreground))] border border-gray-300 hover:border-[hsl(var(--primary)/0.5)] rounded-md shadow-sm hover:bg-gray-50 transition-all hover:text-[hsl(var(--primary))]"
                  onClick={() => setIsOpen(false)}
                >
                  Dashboard
                </Link>
                <Link
                  href="/dashboard/profile"
                  className="w-full flex items-center justify-center px-4 py-2 text-sm font-medium text-[hsl(var(--foreground))] border border-gray-300 hover:border-[hsl(var(--primary)/0.5)] rounded-md shadow-sm hover:bg-gray-50 transition-all hover:text-[hsl(var(--primary))]"
                  onClick={() => setIsOpen(false)}
                >
                  Profile
                </Link>
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={handleSignOut}
                  disabled={isSigningOut}
                >
                  {isSigningOut ? 'Signing out...' : 'Sign out'}
                </Button>
              </div>
            ) : (
              <div className="flex flex-col space-y-4">
                <Link
                  href="/sign-in"
                  className="w-full flex items-center justify-center px-4 py-2 text-sm font-medium text-[hsl(var(--foreground))] border border-gray-300 hover:border-[hsl(var(--primary)/0.5)] rounded-md shadow-sm hover:bg-gray-50 transition-all hover:text-[hsl(var(--primary))]"
                  onClick={() => setIsOpen(false)}
                >
                  Sign in
                </Link>
                <Link
                  href="/sign-up"
                  className="w-full flex items-center justify-center px-4 py-2 text-sm font-semibold text-white bg-[hsl(var(--primary)/0.9)] hover:bg-[hsl(var(--primary))] border-2 border-[hsl(var(--primary)/0.3)] rounded-md shadow-md transition-all hover:shadow-lg"
                  onClick={() => setIsOpen(false)}
                >
                  Join Now
                </Link>
              </div>
            )}
          </div>
        </nav>
      </div>
    </div>
  )
}