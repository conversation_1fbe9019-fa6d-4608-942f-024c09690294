import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardContent, CardTitle, CardDescription } from '@/components/ui/card';
import React, { useState, useMemo } from 'react';
import { DayPicker } from 'react-day-picker';
import { Event } from '@/repositories/event-repository';
import { useRouter } from 'next/navigation';
import { logger } from '@/lib/logger';

'use client';

type EventCalendarProps = {
  events: Event[];
  isLoading?: boolean;
  title?: string;
  description?: string;
  viewMode?: 'organizer' | 'attendee';
  onDateSelect?: (date: Date) => void;
  onEventClick?: (event: Event) => void;
  className?: string;
};

export function EventCalendar({
  events = [],
  isLoading = false,
  title = 'Event Calendar',
  description,
  viewMode = 'attendee',
  onDateSelect,
  onEventClick,
  className,
}: EventCalendarProps) {
  const router = useRouter();
  const [month, setMonth] = useState<Date>(new Date());

  // Group events by date for efficient lookup
  const eventsByDate = useMemo(() => {
    return events.reduce<Record<string, Event[]>>((acc, event) => {
      // Handle both string and Date types for startDate
      if (!event.startDate) {
        // Skip events with no start date
        return acc;
      }

      let dateStr = '';
      if (typeof event.startDate === 'string') {
        const dateParts = event.startDate.split('T');
        if (dateParts.length > 0 && dateParts[0]) {
          dateStr = dateParts[0]; // Get YYYY-MM-DD part
        }
      } else if (event.startDate instanceof Date) {
        const isoString = event.startDate.toISOString();
        const dateParts = isoString.split('T');
        if (dateParts.length > 0 && dateParts[0]) {
          dateStr = dateParts[0];
        }
      } else {
        // Skip events with invalid dates
        return acc;
      }

      // Skip if we couldn't extract a valid date string
      if (!dateStr) {
        return acc;
      }

      if (!acc[dateStr]) {
        acc[dateStr] = [];
      }

      // Now we can safely push to the array
      const dateArray = acc[dateStr];
      if (dateArray) {
        dateArray.push(event);
      }

      return acc;
    }, {});
  }, [events]);

  // Create an array of dates that have events
  const eventDates = useMemo(() => {
    return Object.keys(eventsByDate).map(dateStr => new Date(dateStr));
  }, [eventsByDate]);

  // Navigation buttons
  const handlePrevMonth = () => {
    const newMonth = new Date(month);
    newMonth.setMonth(newMonth.getMonth() - 1);
    setMonth(newMonth);
  };

  const handleNextMonth = () => {
    const newMonth = new Date(month);
    newMonth.setMonth(newMonth.getMonth() + 1);
    setMonth(newMonth);
  };

  const handleDateSelect = (date?: Date) => {
    if (date && onDateSelect) {
      onDateSelect(date);
    }

    // If no handler provided, show events for this date
    if (date && !onDateSelect) {
      const dateStr = format(date, 'yyyy-MM-dd');
      const dateEvents = eventsByDate[dateStr] || [];

      if (dateEvents.length === 1 && !onEventClick && dateEvents[0]?.id) {
        // If only one event with a valid ID, navigate to it
        router.push(`/events/${dateEvents[0].id}`);
      } else if (dateEvents.length > 0) {
        // Show events in a modal or redirect to a date view
        logger.debug('Events for', dateStr, dateEvents);
        // This would be replaced with proper UI for showing multiple events
      }
    }
  };

  // Function to add a dot indicator for days with events
  const renderDayContents = (day: Date, _modifiers: unknown) => {
    const dateStr = format(day, 'yyyy-MM-dd');
    const events = eventsByDate[dateStr];
    const hasEvents = events && events.length > 0;

    return (
      <div className="relative h-full w-full flex flex-col items-center">
        <div>{format(day, 'd')}</div>
        {hasEvents && (
          <div className="flex mt-1 gap-0.5">
            {Array.from({ length: Math.min(3, events.length) }).map((_, i) => (
              <div
                key={i}
                className="w-1.5 h-1.5 rounded-full bg-primary"
              />
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div>
          <CardTitle>{title}</CardTitle>
          {description && <CardDescription>{description}</CardDescription>}
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="icon"
            onClick={handlePrevMonth}
            className="h-8 w-8"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <div className="text-sm font-medium">{format(month, 'MMMM yyyy')}</div>
          <Button
            variant="outline"
            size="icon"
            onClick={handleNextMonth}
            className="h-8 w-8"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        {isLoading ? (
          <div className="flex items-center justify-center h-96">
            <div className="animate-pulse">Loading events...</div>
          </div>
        ) : (
          <DayPicker
            mode="single"
            month={month}
            onMonthChange={setMonth}
            onSelect={handleDateSelect}
            showOutsideDays
            fixedWeeks
            weekStartsOn={0}
            modifiers={{
              hasEvent: eventDates,
            }}
            modifiersStyles={{
              hasEvent: {
                fontWeight: 'bold'
              }
            }}
            className="w-full"
            classNames={{
              months: "w-full flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
              month: "w-full space-y-4",
              caption: "flex justify-center relative items-center h-10",
              caption_label: "text-sm font-medium hidden", // Hide because we have custom header
              nav: "hidden", // Hide default nav - we use custom one
              table: "w-full border-collapse",
              head_row: "flex w-full",
              head_cell: "text-muted-foreground rounded-md w-full font-normal text-[0.8rem] h-10 flex items-center justify-center",
              row: "flex w-full mt-0",
              cell: "relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md h-14 border border-border",
              day: "h-full w-full p-0 font-normal aria-selected:opacity-100 hover:bg-accent rounded-none",
              day_selected: "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
              day_today: "bg-accent text-accent-foreground",
              day_outside: "text-muted-foreground opacity-50",
              day_disabled: "text-muted-foreground opacity-50",
              day_hidden: "invisible",
            }}
            formatters={{
              formatCaption: () => '', // We have our own header
            }}
          />
        )}
      </CardContent>
    </Card>
  );
}