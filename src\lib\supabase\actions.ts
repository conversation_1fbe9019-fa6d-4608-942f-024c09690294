import { createClient as createBaseClient } from './server';

/**
 * Re-export the createClient function from server.ts for server actions
 * This ensures we use the same cached client implementation everywhere
 */
export const createServerActionClient = createBaseClient;

/**
 * For backward compatibility - redirects to the standard createClient function
 * @deprecated Use createClient from server.ts instead
 */
export const createClient = createBaseClient;