import { logger } from '@/lib/logger';
/**
 * Environment variable validation for Supabase integration
 * This script validates that all required environment variables are present
 * and that sensitive keys are not exposed to the client
 */

interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

export function validateSupabaseEnvironment(): ValidationResult {
  const result: ValidationResult = {
    valid: true,
    errors: [],
    warnings: [],
  };

  // Required variables
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
  ];

  // Check for missing required variables
  for (const varName of requiredVars) {
    if (!process.env[varName]) {
      result.valid = false;
      result.errors.push(`Missing required environment variable: ${varName}`);
    }
  }

  // Check for sensitive variables exposed to client
  if (process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY) {
    result.valid = false;
    result.errors.push(
      'Security risk: NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY should not be exposed to the client. ' +
      'Use SUPABASE_SERVICE_ROLE_KEY instead.'
    );
  }

  // Validate URL format
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  if (supabaseUrl) {
    try {
      new URL(supabaseUrl);
    } catch (error) {
      result.valid = false;
      result.errors.push(`Invalid Supabase URL format: ${supabaseUrl}`);
    }
  }

  // Check for token format
  const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  // Simple JWT format check (not comprehensive but catches obvious issues)
  const jwtPattern = /^eyJ[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+$/;

  if (anonKey && !jwtPattern.test(anonKey)) {
    result.valid = false;
    result.errors.push('Invalid format for NEXT_PUBLIC_SUPABASE_ANON_KEY');
  }

  if (serviceKey && !jwtPattern.test(serviceKey)) {
    result.valid = false;
    result.errors.push('Invalid format for SUPABASE_SERVICE_ROLE_KEY');
  }

  // Check for inconsistent projects
  if (anonKey && serviceKey) {
    try {
      const anonParts = anonKey.split('.');
      const serviceParts = serviceKey.split('.');

      if (anonParts.length > 1 && serviceParts.length > 1) {
        // Ensure we have valid parts before trying to decode
        const anonPart = anonParts[1];
        const servicePart = serviceParts[1];

        if (anonPart && servicePart) {
          const anonPayload = JSON.parse(Buffer.from(anonPart, 'base64').toString());
          const servicePayload = JSON.parse(Buffer.from(servicePart, 'base64').toString());

          if (anonPayload.ref !== servicePayload.ref) {
            result.valid = false;
            result.errors.push(
              'Inconsistent project references in API keys. ' +
              'The anon key and service key appear to be for different Supabase projects.'
            );
          }
        }
      } else {
        result.warnings.push('API keys do not have the expected JWT format');
      }
    } catch (error) {
      result.warnings.push('Could not validate project consistency between API keys');
    }
  }

  return result;
}

// Function to run validation in server context and log results
export function validateEnvironment(): void {
  // Only run in development or on server startup
  if (process.env.NODE_ENV !== 'production' || process.env.VALIDATE_ENV === 'true') {
    const result = validateSupabaseEnvironment();

    if (!result.valid) {
      logger.error('❌ Environment validation failed:');
      result.errors.forEach(error => logger.error(`  - ${error}`));
      result.warnings.forEach(warning => logger.warn(`  - ${warning}`));

      // In development, we might want to throw to prevent startup with invalid config
      if (process.env.NODE_ENV === 'development') {
        throw new Error('Environment validation failed. See logs for details.');
      }
    } else {
      logger.info('✅ Environment validation passed');
      if (result.warnings.length > 0) {
        logger.warn('⚠️ Environment validation warnings:');
        result.warnings.forEach(warning => logger.warn(`  - ${warning}`));
      }
    }
  }
}