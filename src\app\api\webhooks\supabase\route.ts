import { createAdminClient } from '@/lib/supabase/admin-client'
import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import * as crypto from 'crypto'
import { extractAvatarFromOAuth } from '@/utils/imageHandling'
import { SupabaseClient } from '@supabase/supabase-js'
import { logger } from '@/lib/logger'

// Type definitions for webhook data
interface WebhookUserData {
  id: string;
  email: string;
  email_confirmed_at: string | null;
  user_metadata?: {
    first_name?: string;
    given_name?: string;
    last_name?: string;
    family_name?: string;
    avatar_url?: string;
    picture?: string;
    [key: string]: unknown;
  };
  [key: string]: unknown;
}

export const dynamic = 'force-dynamic'

/**
 * Handle Supabase Auth webhooks
 * This route is called by Supabase Auth when certain events occur
 */
export async function POST(request: NextRequest) {
  // Use admin client for webhook processing
  const supabase = await createAdminClient()

  // Get the request body as text for signature verification
  const rawBody = await request.text()
  const payload = JSON.parse(rawBody)
  const headersList = await headers()

  // Get the webhook signature
  const webhookSecret = process.env.SUPABASE_WEBHOOK_SECRET
  const signature = headersList.get('x-supabase-signature')

  // If there's no webhook secret, return an error
  if (!webhookSecret) {
    console.error('Missing SUPABASE_WEBHOOK_SECRET')
    return NextResponse.json(
      { error: 'Server misconfigured' },
      { status: 500 }
    )
  }

  // If there's no signature, return an error
  if (!signature) {
    console.error('Missing X-Supabase-Signature header')
    return NextResponse.json(
      { error: 'Missing webhook signature' },
      { status: 400 }
    )
  }

  // Verify the webhook signature
  // Supabase uses HMAC SHA256 for webhook signatures
  try {
    const hmac = crypto.createHmac('sha256', webhookSecret)
    const digest = hmac.update(rawBody).digest('hex')

    if (digest !== signature) {
      console.error('Invalid webhook signature')
      return NextResponse.json(
        { error: 'Invalid webhook signature' },
        { status: 400 }
      )
    }
  } catch (err) {
    console.error('Error verifying webhook:', err)
    return NextResponse.json(
      { error: 'Error verifying webhook signature' },
      { status: 400 }
    )
  }

  // Get the event type
  const eventType = payload.type

  // Log the event for debugging
  logger.info(`Webhook received: ${eventType}`)

  try {
    // Handle different event types
    switch (eventType) {
      case 'user.created':
        // A new user was created
        await handleUserCreated(payload.data, supabase)
        break

      case 'user.updated':
        // A user was updated
        await handleUserUpdated(payload.data, supabase)
        break

      case 'user.deleted':
        // A user was deleted
        await handleUserDeleted(payload.data, supabase)
        break
    }

    // Log the webhook to the database
    // Note: webhook_logs table may not exist in all environments
    try {
      await (supabase as SupabaseClient<unknown>).from('webhook_logs').insert({
        event_type: eventType,
        payload: payload.data,
      });
    } catch (logError) {
      // Ignore logging errors to prevent webhook failures
      console.warn('Failed to log webhook:', logError);
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error handling webhook:', error)
    return NextResponse.json(
      { error: 'Error handling webhook' },
      { status: 500 }
    )
  }
}

/**
 * Handle user.created event
 */
async function handleUserCreated(data: WebhookUserData, supabase: SupabaseClient) {
  // Check if user already exists in the users table
  const { data: existingUser } = await supabase
    .from('users')
    .select('id')
    .eq('auth_user_id', data.id)
    .single()

  if (existingUser) {
    // User already exists, no need to create a new one
    return
  }

  // Extract user metadata including avatar URL
  const userMetadata = data.user_metadata || {}
  const firstName = userMetadata.first_name || userMetadata.given_name || ''
  const lastName = userMetadata.last_name || userMetadata.family_name || ''
  const avatarUrl = extractAvatarFromOAuth(userMetadata)

  // Create a new user in the users table
  await supabase.from('users').insert({
    auth_user_id: data.id,
    email: data.email,
    first_name: firstName,
    last_name: lastName,
    avatar: avatarUrl,
    role: 'user', // Default role
    email_verified: data.email_confirmed_at !== null,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  })
}

/**
 * Handle user.updated event
 */
async function handleUserUpdated(data: WebhookUserData, supabase: SupabaseClient) {
  // Extract user metadata including avatar URL
  const userMetadata = data.user_metadata || {}
  const firstName = userMetadata.first_name || userMetadata.given_name || ''
  const lastName = userMetadata.last_name || userMetadata.family_name || ''
  const avatarUrl = extractAvatarFromOAuth(userMetadata)

  // Update the user in the users table
  await supabase
    .from('users')
    .update({
      email: data.email,
      first_name: firstName || undefined,
      last_name: lastName || undefined,
      avatar: avatarUrl || undefined,
      email_verified: data.email_confirmed_at !== null,
      updated_at: new Date().toISOString(),
    })
    .eq('auth_user_id', data.id)
}

/**
 * Handle user.deleted event
 */
async function handleUserDeleted(data: WebhookUserData, supabase: SupabaseClient) {
  // Mark the user as deleted in the users table
  await supabase
    .from('users')
    .update({
      is_deleted: true,
      updated_at: new Date().toISOString(),
    })
    .eq('auth_user_id', data.id)
}
