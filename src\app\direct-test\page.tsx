'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { createClient } from '@/lib/supabase/client'
import { UserAvatarMenu } from '@/components/layout/user-avatar-menu'

export default function DirectTestPage() {
  const [user, setUser] = useState<unknown>(null)
  const [loading, setLoading] = useState(true)
  const [isSignedIn, setIsSignedIn] = useState(false)
  const [refreshCount, setRefreshCount] = useState(0)
  
  // Check auth status directly
  useEffect(() => {
    const checkAuth = async () => {
      setLoading(true)
      try {
        console.log('Checking auth status directly')
        const supabase = createClient()
        
        // Get the current user from Supabase Auth
        const { data: { user: authUser }, error } = await supabase.auth.getUser()
        
        // Also get the session to double-check authentication state
        const { data: { session } } = await supabase.auth.getSession()
        
        // Update the signed-in state - require both user and session
        const isAuthenticated = !!authUser && !!session && !error
        setIsSignedIn(isAuthenticated)
        
        console.log('Direct auth check:', {
          hasUser: !!authUser,
          hasSession: !!session,
          hasError: !!error,
          isAuthenticated
        })
        
        if (authUser && session) {
          console.log('User authenticated, fetching user data')
          
          // Get user data from database
          const { data: userData, error: userError } = await supabase
            .from('users')
            .select('*')
            .eq('auth_user_id', authUser.id)
            .single()
          
          if (userData) {
            console.log('User data found:', userData)
            setUser(userData)
          } else {
            console.log('No user data found, using auth user data')
            setUser({
              id: authUser.id,
              auth_user_id: authUser.id,
              first_name: authUser.user_metadata?.first_name || authUser.user_metadata?.given_name || '',
              last_name: authUser.user_metadata?.last_name || authUser.user_metadata?.family_name || '',
              email: authUser.email,
              avatar: authUser.user_metadata?.avatar_url || authUser.user_metadata?.picture || null
            })
          }
        } else {
          console.log('User not authenticated, clearing user data')
          setUser(null)
        }
      } catch (error) {
        console.error('Error checking auth:', error)
        setUser(null)
        setIsSignedIn(false)
      } finally {
        setLoading(false)
      }
    }
    
    checkAuth()
  }, [refreshCount])
  
  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">Direct Auth Test Page</h1>
      
      <div className="bg-gray-100 p-6 rounded-lg mb-6">
        <h2 className="text-xl font-semibold mb-4">Direct Auth State</h2>
        <div className="space-y-2">
          <p><strong>Is Signed In:</strong> {isSignedIn ? 'Yes' : 'No'}</p>
          <p><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</p>
          <p><strong>Has User:</strong> {user ? 'Yes' : 'No'}</p>
          
          {user && (
            <div className="mt-4">
              <h3 className="text-lg font-medium mb-2">User Data</h3>
              <pre className="bg-gray-200 p-4 rounded overflow-auto text-xs">
                {JSON.stringify(user, null, 2)}
              </pre>
            </div>
          )}
        </div>
        
        <Button 
          onClick={() => {
            console.log('Manual refresh triggered')
            setRefreshCount(prev => prev + 1)
          }}
          className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Refresh Auth
        </Button>
      </div>

      <div className="bg-gray-100 p-6 rounded-lg mb-6">
        <h2 className="text-xl font-semibold mb-4">Direct UserAvatarMenu Test</h2>
        <div className="flex items-center space-x-4">
          {isSignedIn && user ? (
            <>
              <p>User Avatar Menu:</p>
              <UserAvatarMenu user={user} loading={loading} />
            </>
          ) : (
            <p>Not signed in or no user data available</p>
          )}
        </div>
      </div>

      <div className="bg-gray-100 p-6 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Manual User Object Test</h2>
        <div className="flex items-center space-x-4">
          <Button 
            onClick={() => {
              console.log('Creating test user object')
              const testUser = {
                id: 'test-id',
                auth_user_id: 'test-auth-id',
                first_name: 'Test',
                last_name: 'User',
                email: '<EMAIL>',
                avatar: null
              }
              setUser(testUser)
              setIsSignedIn(true)
              console.log('Test user set:', testUser)
            }}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Create Test User
          </Button>
        </div>
      </div>
    </div>
  )
}
