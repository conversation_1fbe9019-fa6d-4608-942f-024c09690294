import { logger } from '@/lib/logger';
import { createBrowserClient } from '@supabase/ssr'
import { type Database } from './types'

/**
 * Supabase admin client for privileged operations
 * This file provides a client that uses the service role key for admin operations
 * SECURITY WARNING: Never expose this client to the browser or client-side code
 */


/**
 * Create a Supabase admin client with service role key for privileged operations
 * This should only be used on the server side and for operations that require admin privileges
 * SECURITY WARNING: Never expose this client to the browser or client-side code
 */
export async function adminSupabase() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseUrl || !supabaseServiceKey) {
    const missingVars = []
    if (!supabaseUrl) missingVars.push('NEXT_PUBLIC_SUPABASE_URL')
    if (!supabaseServiceKey) missingVars.push('SUPABASE_SERVICE_ROLE_KEY')

    const errorMessage = `Missing Supabase admin credentials: ${missingVars.join(', ')}. Please check your environment variables.`
    logger.error(errorMessage)
    throw new Error(errorMessage)
  }

  return createBrowserClient<Database>(supabaseUrl, supabaseServiceKey)
}

// For backward compatibility with existing code
export const createAdminClient = adminSupabase
