/* eslint-disable @typescript-eslint/no-unused-vars */
'use client';

import { useState, createContext, useContext, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { EventTypeStep } from '@/components/events/event-wizard/steps/event-type-step';
import { BasicDetailsStep } from '@/components/events/event-wizard/steps/basic-details-step';
import { CategoriesStep } from '@/components/events/event-wizard/steps/categories-step';
import { FieldsStep } from '@/components/events/event-wizard/steps/fields-step';
import { TshirtOptionsStep } from '@/components/events/event-wizard/steps/tshirt-options-step';
import { ImageUploadStep } from '@/components/events/event-wizard/steps/image-upload-step';
import { PreviewStep } from '@/components/events/event-wizard/steps/preview-step';
import { EventType } from '@/types/event-types';
import { createEvent, saveEventDraft } from '@/app/actions/events';
import { toast } from '@/components/ui/use-toast';
import { z } from 'zod';
import { checkAuthentication } from '@/app/dashboard/events/debug';
import { Event } from '@/repositories/event-repository';
import { toISOString, toInputDateTime } from '@/lib/utils/date-utils';
import { createEventSchema, EventFormData, basicDetailsSchema } from '@/lib/validations/event-schema';
import { CompletionRateIndicator } from './completion-rate';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { supabase } from '@/lib/supabase/pages-client';

// Define wizard steps
export enum WizardStep {
  EVENT_TYPE = 0,
  BASIC_DETAILS = 1,
  CATEGORIES = 2,
  FIELDS = 3,
  TSHIRT_OPTIONS = 4,
  IMAGES = 5,
  PREVIEW = 6,
}

// Create wizard context for state management
interface WizardContextProps {
  currentStep: WizardStep;
  setCurrentStep: (step: WizardStep) => void;
  formData: unknown;
  updateFormData: (data: any) => void;
  nextStep: () => void;
  prevStep: () => void;
  isSubmitting: boolean;
  submitForm: () => Promise<void>;
  saveDraft: () => Promise<void>;
}

const WizardContext = createContext<WizardContextProps | undefined>(undefined);

export function useWizard() {
  const context = useContext(WizardContext);
  if (!context) {
    throw new Error('useWizard must be used within a WizardProvider');
  }
  return context;
}

// Default form data
interface FormDataType extends EventFormData {
  eventTypeId: string;
  galleryImages?: ImageObject[];
}

interface ImageObject {
  url: string;
  path: string;
}

const defaultFormData: FormDataType = {
  title: '',
  description: '',
  eventTypeId: '',
  eventType: '',
  location: '',
  country: '',
  state: '',
  startDate: '',
  endDate: '',
  timezone: Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC',
  categories: [],
  customFields: [],
  emergencyContactSettings: {
    required: false,
    fields: ['name', 'phone', 'relationship'],
    allowSameForMultipleRegistrations: true,
  },
  city: '',
  status: 'draft',
  // New fields
  totalCapacity: undefined,
  registrationCloseDate: '',
  allowCategorySpecificClosingDates: false,
  tshirtOptions: {
    enabled: false, // Disabled by default
    sizes: ["XS", "S", "M", "L", "XL", "XXL", "XXXL"],
    description: '',
    sizeChartImage: null
  },
  // Initialize image fields as undefined
  posterImage: undefined,
  coverImage: undefined,
  galleryImages: [],
};

interface EventWizardProps {
  eventTypes: EventType[];
  existingEvent?: Event | null;
  pageTitle: string;
}

export function EventWizard({ eventTypes, existingEvent, pageTitle }: EventWizardProps) {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState<WizardStep>(WizardStep.EVENT_TYPE);

  // Log existingEvent prop for debugging
  console.log('[DEBUG] EventWizard received existingEvent:', existingEvent ? {
    id: existingEvent.id,
    title: existingEvent.title,
    hasCategories: Array.isArray(existingEvent.categories) && existingEvent.categories.length > 0,
    categoriesCount: Array.isArray(existingEvent.categories) ? existingEvent.categories.length : 0
  } : 'null');

  const [formData, setFormData] = useState<FormDataType | { id: string; title: string; description: string; eventTypeId: string; eventType: string; location: string; country: string; state: string; startDate: string; endDate: string; timezone: string; categories: unknown[]; customFields: unknown[]; emergencyContactSettings: any; city: string; status: string; totalCapacity: number | undefined; registrationCloseDate: string; allowCategorySpecificClosingDates: boolean; tshirtOptions: any; posterImage: any; coverImage: any; galleryImages: ImageObject[]; }>(() => {
    if (existingEvent) {
      console.log('[DEBUG] Initializing formData with existingEvent data');
      // Map existing event data to form data structure
      return {
        id: existingEvent.id,
        title: existingEvent.title,
        description: existingEvent.description || '',
        eventTypeId: existingEvent.eventTypeId,
        eventType: existingEvent.eventTypeId, // For compatibility with the schema
        location: existingEvent.location || '',
        country: existingEvent.country || '',
        state: existingEvent.state || '',
        startDate: existingEvent.startDate ? toInputDateTime(existingEvent.startDate) : '',
        endDate: existingEvent.endDate ? toInputDateTime(existingEvent.endDate) : '',
        timezone: existingEvent.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone,
        categories: existingEvent.categories || [],
        customFields: existingEvent.customFields || [],
        emergencyContactSettings: existingEvent.emergencyContactSettings || {
          required: false,
          fields: ['name', 'phone', 'relationship'],
          allowSameForMultipleRegistrations: true,
        },
        city: existingEvent.city || '',
        status: existingEvent.status || 'draft',
        // New fields
        totalCapacity: existingEvent.totalCapacity,
        registrationCloseDate: existingEvent.registrationCloseDate ? toInputDateTime(existingEvent.registrationCloseDate) : '',
        allowCategorySpecificClosingDates: existingEvent.allowCategorySpecificClosingDates || false,
        tshirtOptions: existingEvent.tshirtOptions || {
          enabled: false,
          sizes: ["XS", "S", "M", "L", "XL", "XXL", "XXXL"],
          description: '',
          sizeChartImage: null
        },
        // Add image fields if they exist
        posterImage: existingEvent.posterImage,
        coverImage: existingEvent.coverImage,
        galleryImages: existingEvent.galleryImages || [],
      };
    }
    return defaultFormData;
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Function to determine the first incomplete step
  const determineFirstIncompleteStep = (eventData: unknown): WizardStep => {
    // If no event type is selected, start at the beginning
    if (!eventData.eventTypeId) {
      return WizardStep.EVENT_TYPE;
    }

    // Check basic details
    const hasBasicDetails = eventData.title &&
      eventData.description &&
      eventData.location &&
      eventData.country &&
      eventData.state &&
      eventData.city;
    if (!hasBasicDetails) {
      return WizardStep.BASIC_DETAILS;
    }

    // Check categories
    const hasCategories = Array.isArray(eventData.categories) && eventData.categories.length > 0;
    if (!hasCategories) {
      return WizardStep.CATEGORIES;
    }

    // Check custom fields
    const hasCustomFields = Array.isArray(eventData.customFields) && eventData.customFields.length > 0;
    if (!hasCustomFields) {
      return WizardStep.FIELDS;
    }

    // Check T-shirt options - always consider this step complete
    // T-shirt options are optional and enabled by default
    const hasTshirtOptions = true;

    // Check images
    const hasImages = eventData.posterImage || eventData.coverImage;
    if (!hasImages) {
      return WizardStep.IMAGES;
    }

    // If everything is complete, go to preview
    return WizardStep.PREVIEW;
  };

  // Log formData changes
  useEffect(() => {
    console.log('[DEBUG] formData state updated:', {
      id: formData.id,
      title: formData.title,
      hasEventType: !!formData.eventTypeId,
      hasCategories: Array.isArray(formData.categories) && formData.categories.length > 0,
      categoriesCount: Array.isArray(formData.categories) ? formData.categories.length : 0
    });
  }, [formData]);

  // If we have an existing event, navigate to the first incomplete step
  useEffect(() => {
    if (existingEvent && currentStep === WizardStep.EVENT_TYPE) {
      const firstIncompleteStep = determineFirstIncompleteStep(formData);
      console.log('[DEBUG] First incomplete step:', firstIncompleteStep);
      setCurrentStep(firstIncompleteStep);
    }
  }, [existingEvent, currentStep, formData]);

  // Update form data with new values
  const updateFormData = (newData: any) => {
    setFormData((prev) => ({ ...prev, ...newData }));
  };

  // Validation functions for each step
  const validateEventTypeStep = (): boolean => {
    if (!formData.eventTypeId) {
      toast({
        title: "Validation Error",
        description: "Please select an event type before proceeding",
        variant: "destructive",
      });
      return false;
    }
    return true;
  };

  const validateBasicDetailsStep = (): boolean => {
    try {
      basicDetailsSchema.parse(formData);
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors: Record<string, string> = {};
        error.errors.forEach(err => {
          if (err.path.length > 0) {
            if (err.path && err.path.length > 0 && err.path[0]) {
              errors[err.path[0]] = err.message;
            }
          }
        });
        setValidationErrors(errors);

        toast({
          title: "Validation Error",
          description: "Please fix the errors in the basic details form",
          variant: "destructive",
        });
      }
      return false;
    }
  };

  const validateCategoriesStep = (): boolean => {
    // For running events, categories are required
    const eventType = eventTypes.find(type => type.id === formData.eventTypeId);
    const isRunEvent = eventType?.slug === 'runs';

    if (isRunEvent && (!formData.categories || formData.categories.length === 0)) {
      toast({
        title: "Validation Error",
        description: "Running events require at least one category",
        variant: "destructive",
      });
      return false;
    }
    return true;
  };

  // Other steps don't have strict validation requirements
  const validateFieldsStep = (): boolean => true;
  const validateTshirtOptionsStep = (): boolean => true;
  const validateImagesStep = (): boolean => true;
  const validatePreviewStep = (): boolean => true;

  // Validate current step before navigation
  const validateCurrentStep = (): boolean => {
    switch (currentStep) {
      case WizardStep.EVENT_TYPE:
        return validateEventTypeStep();
      case WizardStep.BASIC_DETAILS:
        return validateBasicDetailsStep();
      case WizardStep.CATEGORIES:
        return validateCategoriesStep();
      case WizardStep.FIELDS:
        return validateFieldsStep();
      case WizardStep.TSHIRT_OPTIONS:
        return validateTshirtOptionsStep();
      case WizardStep.IMAGES:
        return validateImagesStep();
      case WizardStep.PREVIEW:
        return validatePreviewStep();
      default:
        return true;
    }
  };

  // Navigation functions
  const nextStep = async () => {
    if (currentStep < WizardStep.PREVIEW) {
      // Validate current step before proceeding
      if (!validateCurrentStep()) {
        return;
      }

      // Auto-save the current state before moving to the next step
      try {
        // Only auto-save if we have at least a title or event type selected
        if (formData.title || formData.eventTypeId) {
          console.log('[DEBUG] Auto-saving before moving to next step');
          await autoSaveDraft();
        }
      } catch (error) {
        console.error('[DEBUG] Error auto-saving before step change:', error);
        // Continue even if auto-save fails
      }

      // If moving to the preview step, make sure we're not automatically submitting
      const nextStepValue = currentStep + 1;
      setCurrentStep(nextStepValue);

      // Scroll to the top of the page smoothly after step change
      setTimeout(() => {
        const pageHeading = document.querySelector('h1') as HTMLElement;
        if (pageHeading) {
          pageHeading.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
      }, 100);
    }
  };

  // Auto-save draft without UI feedback
  const autoSaveDraft = async () => {
    try {
      // Check if supabase client is available
      if (!supabase) {
        console.error('[DEBUG] Supabase client is not available');
        return;
      }

      // Check authentication directly using Supabase client
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.error('[DEBUG] Authentication check failed during auto-save - No user found');
        return;
      }

      // Format data to match event schema - include ALL fields
      const preparedData = {
        // Basic details
        title: formData.title || "Untitled Draft",
        description: formData.description || "",
        eventType: formData.eventTypeId,
        location: formData.location || "",
        country: formData.country || "",
        state: formData.state || "",
        city: formData.city || "",
        startDate: formData.startDate || undefined,
        endDate: formData.endDate || undefined,
        timezone: formData.timezone || "UTC",
        status: "draft" as const,

        // Categories and fields
        categories: Array.isArray(formData.categories) ? formData.categories : [],
        customFields: Array.isArray(formData.customFields) ? formData.customFields : [],

        // Capacity and registration settings
        totalCapacity: formData.totalCapacity,
        registrationCloseDate: formData.registrationCloseDate,
        allowCategorySpecificClosingDates: formData.allowCategorySpecificClosingDates,

        // T-shirt options
        tshirtOptions: formData.tshirtOptions,

        // Emergency contact settings
        emergencyContactSettings: formData.emergencyContactSettings,

        // Images
        posterImage: formData.posterImage,
        coverImage: formData.coverImage,
        galleryImages: formData.galleryImages || [],

        // Include ID if we're updating an existing event
        ...(formData.id ? { id: formData.id } : {})
      };

      // Use the dedicated saveEventDraft function
      const result = await saveEventDraft(preparedData);

      if (result.success && result.data && !formData.id && result.data && 'id' in result.data) {
        // Update form data with the saved event ID if new
        updateFormData({ id: result.data.id });
        console.log('[DEBUG] Auto-save updated form data with new ID:', result.data.id);
      }
    } catch (error) {
      console.error('[DEBUG] Auto-save draft error:', error);
    }
  };

  const prevStep = async () => {
    if (currentStep > WizardStep.EVENT_TYPE) {
      // Auto-save the current state before moving to the previous step
      try {
        // Only auto-save if we have at least a title or event type selected
        if (formData.title || formData.eventTypeId) {
          console.log('[DEBUG] Auto-saving before moving to previous step');
          await autoSaveDraft();
        }
      } catch (error) {
        console.error('[DEBUG] Error auto-saving before step change:', error);
        // Continue even if auto-save fails
      }

      setCurrentStep(currentStep - 1);
    }
  };

  // Handle step click for direct navigation
  const handleStepClick = async (stepNum: number) => {
    // Don't do anything if clicking the current step
    if (stepNum === currentStep) {
      return;
    }

    // If trying to go forward, validate current step first
    if (stepNum > currentStep) {
      if (!validateCurrentStep()) {
        return;
      }
    }

    // Auto-save before changing steps
    try {
      if (formData.title || formData.eventTypeId) {
        console.log('[DEBUG] Auto-saving before step navigation');
        await autoSaveDraft();
      }
    } catch (error) {
      console.error('[DEBUG] Error auto-saving before step navigation:', error);
      // Continue even if auto-save fails
    }

    // Navigate to the selected step
    setCurrentStep(stepNum);

    // Scroll to the top of the page smoothly after step change
    setTimeout(() => {
      const pageHeading = document.querySelector('h1') as HTMLElement;
      if (pageHeading) {
        pageHeading.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }, 100);
  };

  // Save as draft function
  const saveDraft = async () => {
    try {
      setIsSubmitting(true);

      // Check if supabase client is available
      if (!supabase) {
        console.error('[DEBUG] Supabase client is not available');
        toast({
          title: 'Error',
          description: 'Authentication service is not available. Please refresh the page and try again.',
          variant: 'destructive',
        });
        return;
      }

      // Check authentication directly using Supabase client
      const { data: { user } } = await supabase.auth.getUser();
      console.log('[DEBUG] Auth status before saving draft:', user ? 'Authenticated' : 'Not authenticated');

      if (!user) {
        toast({
          title: 'Authentication Error',
          description: 'You need to be signed in to save a draft. Please refresh the page and try again.',
          variant: 'destructive',
        });
        return;
      }

      // Format data to match event schema - include ALL fields
      const preparedData = {
        // Basic details
        title: formData.title || "Untitled Draft",
        description: formData.description || "",
        eventType: formData.eventTypeId,
        location: formData.location || "",
        country: formData.country || "",
        state: formData.state || "",
        city: formData.city || "",
        startDate: formData.startDate || undefined,
        endDate: formData.endDate || undefined,
        timezone: formData.timezone || "UTC",
        status: "draft" as const,

        // Categories and fields
        categories: Array.isArray(formData.categories) ? formData.categories : [],
        customFields: Array.isArray(formData.customFields) ? formData.customFields : [],

        // Capacity and registration settings
        totalCapacity: formData.totalCapacity,
        registrationCloseDate: formData.registrationCloseDate,
        allowCategorySpecificClosingDates: formData.allowCategorySpecificClosingDates,

        // T-shirt options
        tshirtOptions: formData.tshirtOptions,

        // Emergency contact settings
        emergencyContactSettings: formData.emergencyContactSettings,

        // Images
        posterImage: formData.posterImage,
        coverImage: formData.coverImage,
        galleryImages: formData.galleryImages || [],

        // Include ID if we're updating an existing event
        ...(formData.id ? { id: formData.id } : {})
      };

      // Log for debugging
      console.log('[DEBUG] Saving draft with data:', preparedData);
      console.log('[DEBUG] Debug fields - city:', formData.city, 'startDate:', formData.startDate, 'endDate:', formData.endDate);

      try {
        // Use the dedicated saveEventDraft function
        const result = await saveEventDraft(preparedData);
        console.log('[DEBUG] Save draft result:', result);

        if (!result.success) {
          toast({
            title: 'Could not save draft',
            description: result.error || "Unknown error saving draft",
            variant: 'destructive',
          });
          return;
        }

        // Update form data with the saved event ID if new
        if (result.data && !formData.id && result.data && 'id' in result.data) {
          updateFormData({ id: result.data.id });
          console.log('[DEBUG] Updated form data with new ID:', result.data.id);
        }

        toast({
          title: 'Draft Saved',
          description: 'Your event has been saved as draft.',
        });

        // Force a refresh of the client-side router data
        router.refresh();

        // Ask user if they want to go to events page
        const goToEvents = window.confirm('Draft saved. Would you like to go to your events page?');
        if (goToEvents) {
          router.push('/dashboard/events');
        }
      } catch (err) {
        // Handle network errors or other exceptions
        console.error('[DEBUG] Network or server error:', err);
        toast({
          title: 'Connection Error',
          description: 'Could not connect to the server. Please check your internet connection.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('[DEBUG] Draft save error:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to save draft',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Submit form function
  const submitForm = async () => {
    // Confirm with the user before creating the event
    const userConfirmed = window.confirm("Are you ready to create this event? Click OK to proceed or Cancel to continue editing.");

    if (!userConfirmed) {
      return; // User cancelled, don't proceed with submission
    }

    try {
      // Format data to match event schema - include ALL fields
      const formattedData = {
        // Basic details
        title: formData.title,
        description: formData.description || "",
        eventType: formData.eventTypeId,
        location: formData.location || "",
        country: formData.country || "",
        state: formData.state || "",
        city: formData.city || "",
        startDate: formData.startDate || undefined,
        endDate: formData.endDate || undefined,
        timezone: formData.timezone || "UTC",
        status: "published" as const,

        // Categories and fields
        categories: Array.isArray(formData.categories) ? formData.categories : [],
        customFields: Array.isArray(formData.customFields) ? formData.customFields : [],

        // Capacity and registration settings
        totalCapacity: formData.totalCapacity,
        registrationCloseDate: formData.registrationCloseDate,
        allowCategorySpecificClosingDates: formData.allowCategorySpecificClosingDates,

        // T-shirt options
        tshirtOptions: formData.tshirtOptions,

        // Emergency contact settings
        emergencyContactSettings: formData.emergencyContactSettings,

        // Images
        posterImage: formData.posterImage,
        coverImage: formData.coverImage,
        galleryImages: formData.galleryImages || [],

        // Include ID if we're updating an existing event
        ...(formData.id ? { id: formData.id } : {})
      };

      console.log('Submitting event with data:', formattedData);

      setIsSubmitting(true);

      let result;
      try {
        result = await createEvent(formattedData);
      } catch (err) {
        // Handle network errors or other exceptions
        console.error('Network or server error:', err);
        toast({
          title: 'Connection Error',
          description: 'Could not connect to the server. Please check your internet connection.',
          variant: 'destructive',
        });
        return;
      }

      if (!result.success) {
        throw new Error(result.error || 'Failed to create event');
      }

      toast({
        title: 'Event Created',
        description: 'Your event has been successfully created!',
      });

      // Redirect to the event page
      if (result.data && 'id' in result.data) {
        router.push(`/dashboard/events/${result.data.id}`);
      } else {
        router.push('/dashboard/events');
      }
    } catch (error) {
      console.error('Event submission error:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create event',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Step components rendering
  const renderStep = () => {
    switch (currentStep) {
      case WizardStep.EVENT_TYPE:
        return <EventTypeStep eventTypes={eventTypes} />;
      case WizardStep.BASIC_DETAILS:
        return <BasicDetailsStep />;
      case WizardStep.CATEGORIES:
        return <CategoriesStep />;
      case WizardStep.FIELDS:
        return <FieldsStep />;
      case WizardStep.TSHIRT_OPTIONS:
        return <TshirtOptionsStep />;
      case WizardStep.IMAGES:
        return <ImageUploadStep />;
      case WizardStep.PREVIEW:
        return <PreviewStep />;
      default:
        return <EventTypeStep eventTypes={eventTypes} />;
    }
  };

  return (
    <WizardContext.Provider value={{
      currentStep,
      setCurrentStep,
      formData,
      updateFormData,
      nextStep,
      prevStep,
      isSubmitting,
      submitForm,
      saveDraft,
    }}>
      <div className="max-w-5xl mx-auto">
        {/* Completion Rate Indicator */}
        <CompletionRateIndicator />

        {/* Event Title Display */}
        {formData.title && (
          <div className="mb-4 mt-2 text-center">
            <h2 className="text-xl font-semibold text-primary">
              {formData.title}
            </h2>
          </div>
        )}

        {/* Progress Indicator */}
        <div className="mb-8">
          <div className="flex justify-between">
            {Object.values(WizardStep)
              .filter((v) => !isNaN(Number(v)))
              .map((step) => {
                const stepNum = Number(step);
                const isActive = currentStep === stepNum;
                const isCompleted = currentStep > stepNum;
                const isClickable = stepNum <= currentStep || isCompleted;

                return (
                  <TooltipProvider key={stepNum}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div
                          className={`flex flex-col items-center w-full ${isClickable ? 'cursor-pointer' : 'cursor-not-allowed'}`}
                          onClick={() => isClickable && handleStepClick(stepNum)}
                          role="button"
                          tabIndex={isClickable ? 0 : -1}
                          aria-label={`Go to ${stepNum === WizardStep.EVENT_TYPE ? 'Event Type' :
                            stepNum === WizardStep.BASIC_DETAILS ? 'Basic Details' :
                              stepNum === WizardStep.CATEGORIES ? 'Categories' :
                                stepNum === WizardStep.FIELDS ? 'Fields' :
                                  stepNum === WizardStep.TSHIRT_OPTIONS ? 'T-Shirts' :
                                    stepNum === WizardStep.IMAGES ? 'Images' :
                                      'Preview'
                            } step`}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' || e.key === ' ') {
                              e.preventDefault();
                              isClickable && handleStepClick(stepNum);
                            }
                          }}
                        >
                          <div className={`
                            w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold mb-2
                            ${isActive ? 'bg-primary text-white' : isCompleted ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-500'}
                            ${isClickable ? 'hover:ring-2 hover:ring-primary hover:ring-offset-2' : ''}
                            transition-all
                          `}>
                            {isCompleted ? '✓' : stepNum + 1}
                          </div>
                          <div className="text-xs text-center">
                            {stepNum === WizardStep.EVENT_TYPE && 'Event Type'}
                            {stepNum === WizardStep.BASIC_DETAILS && 'Basic Details'}
                            {stepNum === WizardStep.CATEGORIES && 'Categories'}
                            {stepNum === WizardStep.FIELDS && 'Fields'}
                            {stepNum === WizardStep.TSHIRT_OPTIONS && 'T-Shirts'}
                            {stepNum === WizardStep.IMAGES && 'Images'}
                            {stepNum === WizardStep.PREVIEW && 'Preview'}
                          </div>
                          {stepNum < 6 && (
                            <div className={`h-0.5 w-full mt-4 ${isCompleted ? 'bg-green-500' : 'bg-gray-200'}`} />
                          )}
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        {isClickable ?
                          `Click to go to ${stepNum === WizardStep.EVENT_TYPE ? 'Event Type' :
                            stepNum === WizardStep.BASIC_DETAILS ? 'Basic Details' :
                              stepNum === WizardStep.CATEGORIES ? 'Categories' :
                                stepNum === WizardStep.FIELDS ? 'Fields' :
                                  stepNum === WizardStep.TSHIRT_OPTIONS ? 'T-Shirts' :
                                    stepNum === WizardStep.IMAGES ? 'Images' :
                                      'Preview'} step` :
                          'Complete previous steps first'
                        }
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                );
              })}
          </div>
        </div>

        {/* Step Content */}
        <Card className="event-template-review">
          <CardContent className="pt-6">
            {renderStep()}
          </CardContent>
        </Card>

        {/* Navigation Buttons */}
        <div className="flex justify-between mt-6">
          <Button
            variant="outline"
            onClick={currentStep === WizardStep.EVENT_TYPE ? () => router.push('/dashboard/events') : prevStep}
            disabled={isSubmitting}
          >
            {currentStep === WizardStep.EVENT_TYPE ? 'Cancel' : 'Previous'}
          </Button>

          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={saveDraft}
              disabled={isSubmitting}
            >
              Save as Draft
            </Button>

            {currentStep < WizardStep.PREVIEW ? (
              <Button
                id="wizard-next-button"
                onClick={nextStep}
                disabled={isSubmitting}
              >
                Next
              </Button>
            ) : (
              <Button
                id="wizard-submit-button"
                onClick={submitForm}
                disabled={isSubmitting}
                className="bg-green-600 hover:bg-green-700"
              >
                {isSubmitting ? 'Creating...' : 'Create Event'}
              </Button>
            )}
          </div>
        </div>
      </div>
    </WizardContext.Provider>
  );
}