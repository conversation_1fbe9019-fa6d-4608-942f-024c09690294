import { logger } from '@/lib/logger';
import { NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/pages-client'
import { z } from 'zod'

const updateProfileSchema = z.object({
  nationality: z.string().min(1),
  country: z.string().min(1),
  state_province: z.string().optional(),
})

export async function PATCH(req: Request) {
  try {
    const supabase = await createClient();

    // Get the current session
    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
      return new NextResponse('Unauthorized', { status: 401 });
    }

    // Get the internal user ID from the users table
    const { data: userData, error: userIdError } = await supabase
      .from('users')
      .select('id')
      .eq('auth_user_id', session.user.id)
      .single();

    if (userIdError || !userData) {
      logger.error("Error fetching user ID:", userIdError);
      return new NextResponse("User not found", { status: 404 });
    }

    const userId = userData.id;

    const body = await req.json()
    const _data = updateProfileSchema.parse(body)

    // Update the user profile
    // Use a type assertion to avoid TypeScript errors
    const updateData: Record<string, unknown> = {
      nationality: data.nationality,
      country: data.country,
      updated_at: new Date().toISOString()
    };

    // Only add state if it's defined
    if (data.state_province) {
      updateData.state = data.state_province;
    }

    const { error: _error } = await supabase
      .from('users')
      .update(updateData)
      .eq('id', userId)

    if (_error) {
      return new NextResponse(`Database Error: ${error.message}`, { status: 500 })
    }

    return NextResponse.json({ message: 'Profile updated successfully' })
  } catch (_error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ errors: error.errors }, { status: 400 })
    }

    return new NextResponse('Internal Server Error', { status: 500 })
  }
}