'use client'

import { useState, useEffect, useRef } from 'react'
import { motion, useMotionValue, useSpring, useTransform } from 'framer-motion'
import Link from 'next/link'
import Image from 'next/image'
import { Calendar, MapPin, Loader2 } from 'lucide-react'
import { TypeAnimation } from 'react-type-animation'
import ImageCarousel from '../components/ImageCarousel'
import EventSearch from '../components/EventSearch'
import { formatDate } from '../utils/formatDate'
import { Card, CardContent } from '../components/ui/card'
import { Badge } from '../components/ui/badge'
import { Button } from '../components/ui/button'
import { usePublicEvents } from '@/hooks/use-public-events'

// Helper function to convert API events to display format
const convertEventForDisplay = (event: any) => {
  return {
    id: event.id,
    title: event.title || 'Untitled Event',
    href: `/events/${event.slug || event.id}`,
    image: event.coverImage?.url || event.posterImage?.url || '/assets/event-placeholder.jpg',
    description: event.description || 'No description available',
    price: 'Free', // Price information might be in categories
    date: event.startDate ? formatDate(event.startDate) : 'TBA',
    location: event.location || 'TBA',
    slots: 'Available' // Capacity information might be elsewhere
  };
};

const testimonials = [
  {
    id: 1,
    name: "Sarah Johnson",
    comment: "Fuiyoo helped me find the perfect weekend activities for my family. We've discovered so many fun events!",
    image: "https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
  },
  {
    id: 2,
    name: "David Lee",
    comment: "The app is super easy to use and I've met some great people at the events. Highly recommend!",
    image: "https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
  },
  {
    id: 3,
    name: "Emily Wong",
    comment: "As a fitness enthusiast, I love how I can find different sports activities in my area. Great platform!",
    image: "https://images.pexels.com/photos/1036623/pexels-photo-1036623.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1"
  }
]

// Animation variants with faster animations
const fadeInUp = {
  hidden: { opacity: 0, y: 15 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      ease: "easeOut"
    }
  }
}

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.07 // Faster stagger
    }
  }
}

const heroTextVariant = {
  hidden: { opacity: 0, y: 10 },
  visible: (custom: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.4,
      delay: custom * 0.15, // Faster delay between items
      ease: "easeOut"
    }
  })
}

export default function Home() {
  const [scrollY, setScrollY] = useState(0)
  const mouseX = useMotionValue(0)
  const mouseXSmooth = useSpring(mouseX, { damping: 50, stiffness: 300 })
  const [isMouseIdle, setIsMouseIdle] = useState(true)
  const idleTimerRef = useRef<NodeJS.Timeout | null>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // Auto-moving position for when mouse is idle
  const autoPosition = useMotionValue(0)
  const direction = useRef(1) // 1 for right, -1 for left

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY)
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  useEffect(() => {
    // Mouse movement handler
    const handleMouseMove = (e: MouseEvent) => {
      if (!containerRef.current) return

      const { left, width } = containerRef.current.getBoundingClientRect()
      const relativeX = e.clientX - left
      const normalizedX = relativeX / width

      mouseX.set(normalizedX)

      // Reset idle timer on mouse movement
      setIsMouseIdle(false)
      if (idleTimerRef.current) {
        clearTimeout(idleTimerRef.current)
      }
      idleTimerRef.current = setTimeout(() => {
        setIsMouseIdle(true)
      }, 3000) // 3 seconds without movement to consider mouse idle
    }

    window.addEventListener('mousemove', handleMouseMove)

    // Auto movement for idle state
    const autoMoveInterval = setInterval(() => {
      if (isMouseIdle) {
        let newPosition = autoPosition.get() + 0.005 * direction.current

        // Reverse direction when reaching edges
        if (newPosition >= 0.9) {
          direction.current = -1
        } else if (newPosition <= 0.1) {
          direction.current = 1
        }

        autoPosition.set(newPosition)
        mouseX.set(newPosition)
      }
    }, 30) // Update frequency

    return () => {
      window.removeEventListener('mousemove', handleMouseMove)
      clearInterval(autoMoveInterval)
      if (idleTimerRef.current) {
        clearTimeout(idleTimerRef.current)
      }
    }
  }, [mouseX, isMouseIdle])

  // Initialize idle state
  useEffect(() => {
    setIsMouseIdle(true)
    autoPosition.set(0.5) // Start in the middle
  }, [])

  // Transform mouse X position to beam position
  const beamX = useTransform(mouseXSmooth, [0, 1], ['-30%', '130%'])

  // Get events from public API
  const { events: apiEvents, loading, error } = usePublicEvents();

  // Convert API events to display format
  const [displayEvents, setDisplayEvents] = useState<any[]>([]);

  useEffect(() => {
    if (apiEvents && apiEvents.length > 0) {
      const formattedEvents = apiEvents.map(convertEventForDisplay);
      setDisplayEvents(formattedEvents);
    }
  }, [apiEvents]);

  return (
    <main className="min-h-screen overflow-x-hidden pt-0">
      {/* Modern Hero Section with 3D parallax effect */}
      <section className="relative overflow-hidden min-h-[100vh]">
        {/* Background layers for parallax effect */}
        <div className="absolute inset-0 w-full h-full">
          {/* Background gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-b from-black/50 via-black/30 to-black/70 z-10"></div>

          {/* Additional mask for text readability */}
          <div className="absolute inset-0 z-20 mask-linear-from-black mask-linear-to-transparent mask-linear-to-60% mask-linear-at-left"></div>

          {/* Animated particles overlay */}
          <div className="absolute inset-0 z-[5] opacity-40 pointer-events-none">
            <motion.div
              className="absolute inset-0"
              animate={{
                backgroundPosition: ['0% 0%', '100% 100%'],
              }}
              transition={{
                duration: 20,
                ease: "linear",
                repeat: Infinity,
                repeatType: "reverse"
              }}
              style={{
                backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'100\' height=\'100\' viewBox=\'0 0 100 100\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cpath d=\'M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z\' fill=\'%23ffffff\' fill-opacity=\'0.1\' fill-rule=\'evenodd\'/%3E%3C/svg%3E")',
                backgroundSize: '180px 180px'
              }}
            />
          </div>

          {/* Main background carousel */}
          <div className="absolute inset-0 w-full h-full">
            <ImageCarousel overlay={false} />
          </div>
        </div>

        {/* Content container with parallax effect */}
        <div className="relative z-20 min-h-[100vh] flex flex-col">
          {/* Main content area */}
          <div className="flex-grow flex items-center">
            <div className="container mx-auto px-4 md:px-6 lg:px-8 py-12">
              <div className="grid grid-cols-1 lg:grid-cols-12 gap-8 items-center">
                {/* Left side: Hero text content with mask for better readability */}
                <motion.div
                  className="lg:col-span-6 text-center lg:text-left relative"
                  initial={{ opacity: 0, x: -50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.8 }}
                >
                  {/* Mask layer for text readability */}
                  <div className="absolute inset-0 -m-8 mask-radial-from-black mask-radial-to-transparent mask-radial-to-80% mask-radial-at-center"></div>

                  {/* Badge */}
                  <motion.div
                    className="inline-flex items-center px-3 py-1 rounded-full bg-white/10 backdrop-blur-md border border-white/20 mb-6 text-white relative z-10"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2, duration: 0.5 }}
                  >
                    <motion.div
                      className="w-2 h-2 rounded-full bg-primary mr-2"
                      animate={{ scale: [1, 1.3, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    />
                    <span className="text-sm font-medium tracking-wider">DISCOVER · CONNECT · EXPERIENCE</span>
                  </motion.div>

                  {/* Main heading with modern gradient text effect */}
                  <motion.h1
                    className="text-5xl sm:text-6xl md:text-7xl font-bold mb-6 text-white relative z-10"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3, duration: 0.7 }}
                  >
                    <span className="block md:inline">Find Your Next</span>{" "}
                    <span className="relative inline-block">
                      <span className="relative z-10 text-transparent bg-clip-text bg-gradient-to-r from-primary via-indigo-400 to-purple-500 animate-gradient-x">
                        Adventure
                      </span>
                    </span>
                  </motion.h1>

                  {/* Description with modern glass morphism effect */}
                  <motion.p
                    className="text-xl md:text-2xl mb-8 max-w-2xl lg:max-w-3xl relative z-10"
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5, duration: 0.7 }}
                  >
                    <span className="inline-block backdrop-blur-md rounded-xl p-5 bg-gradient-to-br from-black/50 to-black/30 border border-white/10 shadow-xl text-white">
                      Discover and book the best events across Malaysia — from
                      <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary to-purple-500 animate-gradient-x font-semibold"> sports competitions</span> to
                      <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 to-primary animate-gradient-x font-semibold"> wellness retreats</span> and
                      <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-indigo-400 animate-gradient-x font-semibold"> entertainment shows</span>.
                    </span>
                  </motion.p>

                  {/* CTA buttons with modern gradient styling */}
                  <motion.div
                    className="flex flex-wrap gap-4 justify-center lg:justify-start relative z-10"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.7, duration: 0.5 }}
                  >
                    <Button
                      className="relative overflow-hidden group px-8 py-6 h-auto text-lg rounded-xl shadow-lg transition-all duration-300 hover:shadow-xl hover:translate-y-[-2px]"
                    >
                      <span className="absolute inset-0 bg-gradient-to-r from-primary via-indigo-500 to-purple-600 animate-gradient-x"></span>
                      <span className="absolute inset-0 opacity-0 group-hover:opacity-20 bg-white transition-opacity duration-300"></span>
                      <span className="relative z-10 text-white font-medium">Explore Events</span>
                    </Button>

                    <Button
                      variant="outline"
                      className="relative overflow-hidden group px-8 py-6 h-auto text-lg rounded-xl border-white/20 transition-all duration-300 hover:translate-y-[-2px]"
                    >
                      <span className="absolute inset-0 bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-md"></span>
                      <span className="absolute inset-0 opacity-0 group-hover:opacity-10 bg-white transition-opacity duration-300"></span>
                      <span className="relative z-10 text-white font-medium">Create Event</span>
                    </Button>
                  </motion.div>

                  {/* Stats counter with modern gradient styling */}
                  <motion.div
                    className="mt-12 grid grid-cols-3 gap-4 max-w-lg mx-auto lg:mx-0 relative z-10"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.9, duration: 0.5 }}
                  >
                    {[
                      {
                        value: '500+',
                        label: 'Events',
                        gradient: 'from-primary to-indigo-500'
                      },
                      {
                        value: '50K+',
                        label: 'Users',
                        gradient: 'from-indigo-500 to-purple-600'
                      },
                      {
                        value: '15+',
                        label: 'Categories',
                        gradient: 'from-purple-600 to-primary'
                      }
                    ].map((stat, index) => (
                      <div
                        key={index}
                        className="relative text-center p-4 rounded-xl overflow-hidden group"
                      >
                        {/* Background with gradient border effect */}
                        <div className="absolute inset-0 p-[1px] rounded-xl overflow-hidden">
                          <div className={`absolute inset-0 bg-gradient-to-r ${stat.gradient} animate-gradient-x opacity-50`}></div>
                        </div>
                        <div className="absolute inset-[1px] bg-black/30 backdrop-blur-md rounded-[10px]"></div>

                        {/* Content */}
                        <div className="relative z-10">
                          <motion.div
                            className={`text-2xl md:text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r ${stat.gradient} animate-gradient-x`}
                            initial={{ opacity: 0, scale: 0.5 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ delay: 1 + (index * 0.1), duration: 0.5 }}
                          >
                            {stat.value}
                          </motion.div>
                          <div className="text-sm text-white/80 mt-1 font-medium">{stat.label}</div>
                        </div>
                      </div>
                    ))}
                  </motion.div>
                </motion.div>

                {/* Right side: 3D-like floating cards with mask effects */}
                <motion.div
                  className="lg:col-span-6 relative h-[300px] md:h-[400px] lg:h-[500px] hidden md:block"
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.5, duration: 1 }}
                >
                  {/* Mask layer for visual interest */}
                  <div className="absolute inset-0 mask-radial-from-transparent mask-radial-to-black mask-radial-to-70% mask-radial-at-left"></div>

                  {/* Floating event cards with parallax effect */}
                  {[
                    {
                      title: "Basketball Tournament",
                      image: "/assets/basket.jpg",
                      date: "May 15, 2023",
                      x: -20, y: -10, delay: 0, scale: 1.1
                    },
                    {
                      title: "Yoga Workshop",
                      image: "/assets/yoga.jpg",
                      date: "June 10, 2023",
                      x: 30, y: 40, delay: 0.2, scale: 0.9
                    },
                    {
                      title: "Financial Seminar",
                      image: "/assets/finseminar.jpg",
                      date: "July 5, 2023",
                      x: -10, y: 80, delay: 0.4, scale: 0.8
                    }
                  ].map((card, index) => (
                    <motion.div
                      key={index}
                      className="absolute rounded-2xl overflow-hidden shadow-2xl w-64 h-80 bg-white"
                      style={{
                        left: `calc(50% + ${card.x}px)`,
                        top: `calc(50% + ${card.y}px)`,
                        zIndex: 10 - index,
                        transformStyle: "preserve-3d",
                        perspective: "1000px"
                      }}
                      initial={{
                        opacity: 0,
                        x: card.x - 50,
                        y: card.y - 50,
                        rotateX: 10,
                        rotateY: -10,
                        scale: card.scale
                      }}
                      animate={{
                        opacity: 1,
                        x: card.x,
                        y: card.y,
                        rotateX: [10, 5, 10],
                        rotateY: [-10, -5, -10],
                        scale: card.scale
                      }}
                      transition={{
                        delay: 0.8 + card.delay,
                        duration: 1,
                        rotateX: {
                          repeat: Infinity,
                          duration: 5 + index,
                          ease: "easeInOut"
                        },
                        rotateY: {
                          repeat: Infinity,
                          duration: 6 + index,
                          ease: "easeInOut"
                        }
                      }}
                    >
                      <div className="relative h-48 w-full">
                        <Image
                          src={card.image}
                          alt={card.title}
                          fill
                          className="object-cover"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                        <div className="absolute bottom-3 left-3 right-3">
                          <div className="text-white font-bold">{card.title}</div>
                          <div className="text-white/80 text-sm flex items-center">
                            <Calendar className="w-3 h-3 mr-1" />
                            {card.date}
                          </div>
                        </div>
                      </div>
                      <div className="p-3 bg-white">
                        <div className="w-full h-2 bg-gray-100 rounded-full mb-2"></div>
                        <div className="w-2/3 h-2 bg-gray-100 rounded-full"></div>
                      </div>
                    </motion.div>
                  ))}

                  {/* Decorative elements */}
                  <motion.div
                    className="absolute -bottom-10 -right-10 w-40 h-40 rounded-full bg-primary/30 blur-3xl"
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.3, 0.5, 0.3]
                    }}
                    transition={{
                      duration: 8,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                  <motion.div
                    className="absolute -top-10 -left-10 w-40 h-40 rounded-full bg-purple-500/20 blur-3xl"
                    animate={{
                      scale: [1, 1.3, 1],
                      opacity: [0.2, 0.4, 0.2]
                    }}
                    transition={{
                      duration: 10,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                </motion.div>
              </div>
            </div>
          </div>

          {/* Search bar with floating effect */}
          <div className="relative pb-16 md:pb-24 px-4">
            <motion.div
              className="mx-auto max-w-5xl relative z-30"
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1, duration: 0.8 }}
            >
              <motion.div
                className="bg-[hsl(var(--background))]/95 backdrop-blur-xl rounded-2xl shadow-2xl p-5 border border-[hsl(var(--border))]/20 overflow-hidden"
                whileHover={{ boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.3)" }}
                transition={{ duration: 0.3 }}
              >
                {/* Decorative gradient orbs */}
                <div className="absolute -top-20 -right-20 w-40 h-40 rounded-full bg-[hsl(var(--primary))]/10 blur-3xl"></div>
                <div className="absolute -bottom-20 -left-20 w-40 h-40 rounded-full bg-[hsl(var(--accent))]/10 blur-3xl"></div>

                <div className="relative z-10">
                  <EventSearch />
                </div>
              </motion.div>
            </motion.div>
          </div>
        </div>

        {/* Animated scroll indicator */}
        <motion.div
          className="absolute bottom-6 left-1/2 transform -translate-x-1/2 z-30 hidden md:flex flex-col items-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.5, duration: 0.5 }}
        >
          <span className="text-white text-sm font-medium mb-2 px-4 py-1 rounded-full bg-black/30 backdrop-blur-sm">Scroll to explore</span>
          <motion.div
            animate={{ y: [0, 8, 0] }}
            transition={{ repeat: Infinity, duration: 1.5 }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M12 5v14M19 12l-7 7-7-7" />
            </svg>
          </motion.div>
        </motion.div>
      </section>

      {/* Featured Events - Better spacing and responsive grid */}
      <section className="py-20">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <motion.div
            className="flex flex-col sm:flex-row justify-between items-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-3xl font-bold mb-4 sm:mb-0">Featured Events</h2>
            <Link href="/events" passHref>
              <Button variant="outline" className="border-[hsl(var(--primary))] text-[hsl(var(--primary))] hover:bg-[hsl(var(--primary-50))]">
                View All Events
              </Button>
            </Link>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
          >
            {loading ? (
              <div className="col-span-full flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-primary mr-2" />
                <span>Loading events...</span>
              </div>
            ) : error ? (
              <div className="col-span-full text-center py-12">
                <p className="text-red-500">Error loading events</p>
                <p className="text-sm text-muted-foreground">{error.message}</p>
              </div>
            ) : displayEvents.length === 0 ? (
              <div className="col-span-full text-center py-12">
                <p>No events found</p>
              </div>
            ) : displayEvents.slice(0, 4).map((event, index) => (
              <motion.div
                key={event.id}
                className="bg-[hsl(var(--card))] text-[hsl(var(--card-foreground))] rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-shadow cursor-pointer"
                variants={fadeInUp}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                whileHover={{ y: -5 }}
                onClick={() => window.location.href = event.href}
              >
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src={event.image}
                    alt={event.title}
                    fill
                    unoptimized
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                    className="object-cover transition-transform duration-500 hover:scale-110"
                  />
                  <div className="absolute top-3 right-3 bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))] text-xs font-bold px-2 py-1 rounded">
                    {event.price}
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="font-bold text-lg mb-2">{event.title}</h3>
                  <p className="text-[hsl(var(--muted-foreground))] text-sm mb-4 line-clamp-2">{event.description}</p>
                  <div className="flex items-center gap-3 text-sm text-[hsl(var(--muted-foreground))] mb-3">
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      <span>{event.date}</span>
                    </div>
                    <div className="flex items-center">
                      <MapPin className="w-4 h-4 mr-1" />
                      <span>{event.location}</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-[hsl(var(--muted-foreground))]">Slots: {event.slots}</span>
                    <Button variant="outline" size="sm" className="text-[hsl(var(--primary))] border-[hsl(var(--primary))] hover:bg-[hsl(var(--primary-50))]">
                      Book Now
                    </Button>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Categories Section - Improved grid and spacing */}
      <section className="py-20 bg-[hsl(var(--muted))]">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <motion.h2
            className="text-3xl font-bold mb-12 text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            Browse by Category
          </motion.h2>

          <motion.div
            className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 md:gap-6"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
          >
            {[
              {
                name: 'Sports', icon: (
                  <svg className="w-8 h-8 mx-auto" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12.75 4.75a.75.75 0 0 0-1.5 0v2.5h-2.5a.75.75 0 0 0 0 1.5h2.5v2.5a.75.75 0 0 0 1.5 0v-2.5h2.5a.75.75 0 0 0 0-1.5h-2.5v-2.5Z" />
                    <path fillRule="evenodd" d="M9.464 3.229C10.176 3.086 10.915 3 11.686 3h.628c.77 0 1.51.086 2.221.229a10.5 10.5, 0, 0 1 5.457 2.979c.67.721, 1.257 1.527, 1.75, 2.403.493.876.871, 1.821, 1.126, 2.819.255.997.387, 2.042.387, 3.116v.91c0 1.073-.132, 2.118-.387, 3.115-.255.998-.633, 1.943-1.126, 2.819-.493.876-1.08, 1.682-1.75, 2.402A10.5 10.5, 0, 0 1 14.536 20.771c-.712.143-1.451.229-2.221.229h-.628c-.77, 0-1.51-.086-2.221-.229a10.5 10.5, 0, 0 1-5.457-2.979c-.67-.72-1.257-1.526-1.75-2.402-.493-.876-.871-1.821-1.126-2.819C.877, 11.574.745, 10.529.745, 9.456v-.91c0-1.074.132-2.119.387-3.116.255-.998.633-1.943 1.126-2.819.493-.876, 1.08-1.682, 1.75-2.403A10.5 10.5, 0, 0 1 9.465 3.23Zm-5.08, 8.488a8.89, 8.89, 0, 0 1 2.322-5.684A8.96, 8.96, 0, 0 1 12,3.5a8.96, 8.96, 0, 0 1 5.294, 2.533A8.89, 8.89, 0, 0 1 19.615, 11.717a8.89, 8.89, 0, 0 1-2.321, 5.684A8.96, 8.96, 0, 0 1 12, 19.934a8.96, 8.96, 0, 0 1-5.294-2.533, 8.89, 8.89, 0, 0 1-2.322-5.684Z" clipRule="evenodd" />
                  </svg>
                )
              },
              {
                name: 'Wellness', icon: (
                  <svg className="w-8 h-8 mx-auto" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9.548 2.803C9.386 2.245 8.98 1.994 8.4 2c-.58.006-.98.267-1.132.826A.55.55 0 0 0 7.02 3.5c0 2.018-.082 3.672-.49 5.08l.002-.008c-.41 1.408-1.090 2.55-1.994 3.602A37.78 37.78 0 0 0 2.502 14.2c-.1.121-.197.241-.296.362-.191.236-.38.475-.55.702-.4.073-.78.147-.115.22-.154.31-.304.632-.37.942-.175.806-.075 1.517.334 2.096.41.579 1.09.973 2.114.978a3.53 3.53 0 0 0 .48-.032c.393-.053.808-.162 1.226-.287l.042-.013c.42-.125.813-.266 1.219-.41.405-.142.785-.287 1.116-.419.332-.13.616-.248.872-.33a1.23 1.23 0 0, 1 .364-.066c.16.003.307.**************.122.216.282.308.45l.012.022c.092.169.173.347.265.515.9.168.185.328.295.448.***************.365.***************-.059.335-.173.191-.207.37-.5.549-.81.09-.155.18-.313.274-.471l.018-.03c.094-.158.193-.317.308-.444.121-.134.279-.25.49-.253.181-.003.373.069.588.183.216.115.447.272.689.435.485.329.994.678 1.479.827.463.145.836.096 1.178-.075.341-.172.635-.463.865-.865.266-.467.415-1.096.415-1.814 0-.718-.207-1.607-.512-2.448-.304-.842-.701-1.64-1.05-2.194a10.93 10.93 0 0 0-1.157-1.497 22.11 22.11 0 0 0-.482-.495c-1.033-1.035-1.552-2.184-1.81-3.567-.257-1.383-.234-3.005-.032-4.821a.54.54 0 0 0-.241-.615c-.181-.112-.392-.099-.579-.061-.186.038-.378.098-.566.159-.375.12-.733.24-1.09.295-.363.055-.79.068-1.2-.153a2.567 2.567 0 0 1-1.28-1.983Z" />
                  </svg>
                )
              },
              {
                name: 'Finance', icon: (
                  <svg className="w-8 h-8 mx-auto" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2c.25 0 .46.18.49.42l.38 2.65c.61.25 1.17.58 1.69.98l2.49-1c.23-.09.5 0 .61.22l2 3.46c.13.22.07.49-.12.64l-2.11 1.65c.04.32.07.64.07.98 0 .34-.03.66-.07.98l2.11 1.65c.19.15.24.42.12.64l-2 3.46c-.12.22-.39.3-.61.22l-2.49-1c-.52.4-1.08.73-1.69.98l-.38 2.65c.03.24-.24.42-.49.42h-4c-.25 0-.46-.18-.49-.42l-.38-2.65c-.61-.25-1.17-.58-1.69-.98l-2.49 1c-.23.09-.5 0-.61-.22l-2-3.46c-.13-.22-.07-.49.12-.64l2.11-1.65c-.04-.32-.07-.64-.07-.98 0-.34.03-.66.07-.98L4.24 9.67c-.19-.15-.24-.42-.12-.64l2-3.46c.12-.22.39-.31.61-.22l2.49 1c.52-.4 1.08-.73 1.69-.98l.38-2.65c.03-.24.24-.42.49-.42h4zm-.71 7.71c-.66.66-.66 1.74 0 2.4.66.66 1.74.66 2.4 0 .66-.66.66-1.74 0-2.4-.66-.66-1.74-.66-2.4 0z" />
                  </svg>
                )
              },
              {
                name: 'Education', icon: (
                  <svg className="w-8 h-8 mx-auto" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 3L1 9l11 6 9-4.91V17c0 .55.45 1 1 1s1-.45 1-1v-6.5L12 3zm6 9.5l-1-.55V17c0 1.1-.9 2-2 2H7c-1.1 0-2-.9-2-2v-4.38l-2-1.09v5.47c0 2.2 1.8 4 4 4h12c2.2 0 4-1.8 4-4v-5.47l-5 2.73z" />
                  </svg>
                )
              },
              {
                name: 'Entertainment', icon: (
                  <svg className="w-8 h-8 mx-auto" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10zm0-7c-1.66 0-3-1.34-3-3s1.34-3 3-3 3 1.34 3 3-1.34 3-3 3zm-7-3c0-1.66 1.34-3 3-3s3 1.34 3 3-1.34 3-3 3-3-1.34-3-3zm11 0c0 1.66-1.34 3-3 3s-3-1.34-3-3 1.34-3 3-3 3 1.34 3 3z" />
                  </svg>
                )
              },
              {
                name: 'Charity', icon: (
                  <svg className="w-8 h-8 mx-auto" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" />
                  </svg>
                )
              },
              {
                name: 'Technology', icon: (
                  <svg className="w-8 h-8 mx-auto" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 18c1.1 0 1.99-.9 1.99-2L22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2H0v2h24v-2h-4zM4 6h16v10H4V6z" />
                  </svg>
                )
              },
              {
                name: 'Business', icon: (
                  <svg className="w-8 h-8 mx-auto" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 6h-4V4c0-1.11-.89-2-2-2h-4c-1.11 0-2 .89-2 2v2H4c-1.11 0-1.99.89-1.99 2L2 19c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2zm-6 0h-4V4h4v2z" />
                  </svg>
                )
              },
              {
                name: 'Arts', icon: (
                  <svg className="w-8 h-8 mx-auto" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 22C6.49 22 2 17.51 2 12S6.49 2 12 2s10 4.04 10 9c0 3.31-2.69 6-6 6h-1.77c-.28 0-.5.22-.5.5 0 .12.05.23.13.33.41.47.64 1.06.64 1.67 0 1.38-1.12 2.5-2.5 2.5zm0-18c-4.41 0-8 3.59-8 8s3.59 8 8 8c.28 0 .5-.22.5-.5 0-.16-.08-.28-.14-.35-.41-.46-.63-1.05-.63-1.65 0-1.38 1.12-2.5 2.5-2.5H16c2.21 0 4-1.79 4-4 0-3.86-3.59-7-8-7z" />
                    <circle cx="6.5" cy="11.5" r="1.5" />
                    <circle cx="9.5" cy="7.5" r="1.5" />
                    <circle cx="14.5" cy="7.5" r="1.5" />
                    <circle cx="17.5" cy="11.5" r="1.5" />
                  </svg>
                )
              },
              {
                name: 'Other', icon: (
                  <svg className="w-8 h-8 mx-auto" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z" />
                    <path d="M12 9c.83 0 1.5.67 1.5 1.5S12.83 12 12 12s-1.5-.67-1.5-1.5S11.17 9 12 9zm0 6c2.08 0 4.15 1.01 4.15 3H7.85c0-1.99 2.08-3 4.15-3z" />
                  </svg>
                )
              }
            ].map(({ name, icon }, index) => (
              <motion.div
                key={name}
                className="bg-[hsl(var(--card))] text-[hsl(var(--card-foreground))] rounded-lg p-4 text-center shadow-md hover:shadow-lg transition-shadow cursor-pointer"
                variants={fadeInUp}
                transition={{ duration: 0.3, delay: index * 0.05 }}
                whileHover={{ y: -5, scale: 1.02 }}
                onClick={() => window.location.href = `/events?category=${name}`}
              >
                <div className="text-[hsl(var(--primary))] mb-2">
                  {icon}
                </div>
                <h3 className="font-medium">{name}</h3>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Testimonials - Improved spacing */}
      <section className="py-20">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <motion.h2
            className="text-3xl font-bold mb-12 text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            Client Testimonials
          </motion.h2>
          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
          >
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={testimonial.id}
                className="bg-[hsl(var(--card))] text-[hsl(var(--card-foreground))] p-6 rounded-lg shadow-md"
                variants={fadeInUp}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                whileHover={{ scale: 1.03 }}
              >
                <div className="flex items-center mb-4">
                  <div className="relative w-12 h-12 mr-4">
                    <Image
                      src={testimonial.image}
                      alt={testimonial.name}
                      fill
                      unoptimized
                      sizes="48px"
                      className="rounded-full object-cover"
                    />
                  </div>
                  <div>
                    <h4 className="font-bold">{testimonial.name}</h4>
                    <div className="flex text-yellow-400">
                      {[...Array(5)].map((_, i) => (
                        <svg key={i} className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      ))}
                    </div>
                  </div>
                </div>
                <p className="text-[hsl(var(--muted-foreground))]">{testimonial.comment}</p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* More Activities Section - Improved layout */}
      <section className="py-20 bg-[hsl(var(--muted))]">
        <div className="container mx-auto px-4 md:px-6 lg:px-8">
          <motion.h2
            className="text-3xl font-bold mb-12 text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            More Activities
          </motion.h2>
          <motion.div
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-100px" }}
            variants={staggerContainer}
          >
            {loading ? (
              <div className="col-span-full flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-primary mr-2" />
                <span>Loading events...</span>
              </div>
            ) : error ? (
              <div className="col-span-full text-center py-12">
                <p className="text-red-500">Error loading events</p>
                <p className="text-sm text-muted-foreground">{error.message}</p>
              </div>
            ) : displayEvents.length <= 4 ? (
              <div className="col-span-full text-center py-12">
                <p>No additional events found</p>
              </div>
            ) : displayEvents.slice(4).map((event, index) => (
              <motion.div
                key={event.id}
                className="bg-[hsl(var(--card))] text-[hsl(var(--card-foreground))] rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-shadow cursor-pointer"
                variants={fadeInUp}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                whileHover={{ y: -5 }}
                onClick={() => window.location.href = event.href}
              >
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src={event.image}
                    alt={event.title}
                    fill
                    unoptimized
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                    className="object-cover transition-transform duration-500 hover:scale-110"
                  />
                  <div className="absolute top-3 right-3 bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))] text-xs font-bold px-2 py-1 rounded">
                    {event.price}
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="font-bold text-lg mb-2">{event.title}</h3>
                  <p className="text-[hsl(var(--muted-foreground))] text-sm mb-4 line-clamp-2">{event.description}</p>
                  <div className="flex items-center gap-3 text-sm text-[hsl(var(--muted-foreground))] mb-3">
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      <span>{event.date}</span>
                    </div>
                    <div className="flex items-center">
                      <MapPin className="w-4 h-4 mr-1" />
                      <span>{event.location}</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-[hsl(var(--muted-foreground))]">Slots: {event.slots}</span>
                    <Button variant="outline" size="sm" className="text-[hsl(var(--primary))] border-[hsl(var(--primary))] hover:bg-[hsl(var(--primary-50))]">
                      Book Now
                    </Button>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
          <motion.div
            className="mt-10 text-center"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button variant="outline" className="border-[hsl(var(--primary))] text-[hsl(var(--primary))] hover:bg-[hsl(var(--primary-50))]">
                Load More
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* App Download Section - Enhanced with Pexels images */}
      <section className="py-20 bg-gradient-to-r from-primary to-primary/90 text-white overflow-hidden relative">
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-10">
          <Image
            src="https://images.pexels.com/photos/7130560/pexels-photo-7130560.jpeg"
            alt="Background pattern"
            fill
            unoptimized
            className="object-cover"
          />
        </div>

        <div className="container mx-auto px-4 md:px-6 lg:px-8 relative z-10">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <motion.div
              className="md:w-1/2 mb-10 md:mb-0 text-center md:text-left"
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-3xl font-bold mb-4">Download our new app now!</h2>
              <p className="mb-8 text-lg">Get exclusive access to events and special discounts</p>
              <div className="flex gap-4 justify-center md:justify-start">
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button className="bg-black hover:bg-gray-800">
                    <svg className="w-5 h-5 mr-2" viewBox="0 0 384 512" fill="currentColor">
                      <path d="M318.7 268.7c-.2-36.7 16.4-64.4 50-84.8-18.8-26.9-47.2-41.7-84.7-44.6-35.5-2.8-74.3 20.7-88.5 20.7-15 0-49.4-19.7-76.4-19.7C63.3 141.2 4 184.8 4 273.5q0 39.3 14.4 81.2c12.8 36.7 59 126.7 107.2 125.2 25.2-.6 43-17.9 75.8-17.9 31.8 0 48.3 17.9 76.4 17.9 48.6-.7 90.4-82.5 102.6-119.3-65.2-30.7-61.7-90-61.7-91.9zm-56.6-164.2c27.3-32.4 24.8-61.9 24-72.5-24.1 1.4-52 16.4-67.9 34.9-17.5 19.8-27.8 44.3-25.6 71.9 26.1 2 49.9-11.4 69.5-34.3z" />
                    </svg>
                    App Store
                  </Button>
                </motion.div>
                <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                  <Button className="bg-black hover:bg-gray-800">
                    <svg className="w-5 h-5 mr-2" viewBox="0 0 512 512" fill="currentColor">
                      <path d="M325.3 234.3L104.6 13l280.8 161.2-60.1 60.1zM47 0C34 6.8 25.3 19.2 25.3 35.3v441.3c0 16.1 8.7 28.5 21.7 35.3l256.6-256L47 0zm425.2 225.6l-58.9-34.1-65.7 64.5 65.7 64.5 60.1-34.1c18-14.3 18-46.5-1.2-60.8zM104.6 499l280.8-161.2-60.1-60.1L104.6 499z" />
                    </svg>
                    Play Store
                  </Button>
                </motion.div>
              </div>

              {/* Added features list */}
              <motion.div
                className="mt-8 grid grid-cols-2 gap-4"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.3 }}
              >
                {[
                  "Easy event booking",
                  "Real-time notifications",
                  "Exclusive discounts",
                  "Event reminders"
                ].map((feature, index) => (
                  <div key={index} className="flex items-center">
                    <svg className="w-5 h-5 mr-2 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span>{feature}</span>
                  </div>
                ))}
              </motion.div>
            </motion.div>

            <motion.div
              className="md:w-1/2 relative flex justify-center md:justify-end"
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <div className="relative">
                {/* Main app mockup */}
                <Image
                  src="https://images.pexels.com/photos/6925378/pexels-photo-6925378.jpeg"
                  alt="Mobile app showing event details"
                  width={300}
                  height={600}
                  unoptimized
                  className="rounded-2xl shadow-xl z-10 relative border-4 border-white/20"
                />

                {/* Secondary app mockup */}
                <div className="absolute -left-20 top-20 hidden lg:block">
                  <Image
                    src="https://images.pexels.com/photos/5053740/pexels-photo-5053740.jpeg"
                    alt="Mobile app showing event list"
                    width={200}
                    height={400}
                    unoptimized
                    className="rounded-2xl shadow-xl border-4 border-white/20 rotate-[-8deg]"
                  />
                </div>

                {/* Glow effect */}
                <motion.div
                  className="absolute -inset-1 bg-white/20 rounded-2xl blur-xl -z-0"
                  animate={{
                    boxShadow: ['0 0 20px 5px rgba(255,255,255,0.3)', '0 0 30px 10px rgba(255,255,255,0.5)', '0 0 20px 5px rgba(255,255,255,0.3)']
                  }}
                  transition={{ duration: 2, repeat: Infinity }}
                ></motion.div>
              </div>
            </motion.div>
          </div>

          {/* Attribution */}
          <div className="text-center mt-10 text-xs text-white/60">
            Images from Pexels - The best free stock photos, royalty free images shared by creators
          </div>
        </div>
      </section>

      {/* Back to top button */}
      <motion.div
        className="fixed bottom-6 right-6 z-50"
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: scrollY > 300 ? 1 : 0, scale: scrollY > 300 ? 1 : 0 }}
        transition={{ duration: 0.3 }}
      >
        <motion.button
          className="bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))] p-3 rounded-full shadow-lg"
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M18 15l-6-6-6 6" />
          </svg>
        </motion.button>
      </motion.div>
    </main>
  );
}