import { createClient } from '@/lib/supabase/pages-client';
import { NextResponse } from "next/server";
import fs from 'fs';
import path from 'path';

export async function POST(request: Request) {
  try {
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    // Only allow authenticated users
    if (!session || !session.user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const userId = session.user.id;

    // Check if user has admin role
    const { data: user } = await supabase
      .from('users')
      .select('role')
      .eq('id', userId)
      .single();

    if (!user || !user.role || !['ADMIN', 'SUPER_ADMIN'].includes(user.role)) {
      return new NextResponse("Forbidden", { status: 403 });
    }

    // Read the migration SQL file
    const migrationPath = path.join(process.cwd(), 'src', 'db', 'migrations', '0020_create_registration_fields_table.sql');
    const migrationSql = fs.readFileSync(migrationPath, 'utf8');

    // Apply the migration
    // Use a type assertion to access the query method
    const supabaseAny = supabase as unknown;
    const { error } = await supabaseAny.query(migrationSql);

    if (error) {
      console.error("Error applying migration:", error);
      return new NextResponse("Error applying migration", { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: "Registration fields table created successfully"
    });
  } catch (error) {
    console.error("Error in migration route:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
