import { getBaseUrl } from '@/utils/url-utilities';
import { logger } from '@/lib/logger';

/**
 * Check if a URL is an event registration URL
 * @param url The URL to check
 * @returns True if the URL is an event registration URL
 */
export function isEventRegistrationUrl(url: string): boolean {
  if (!url) return false;

  try {
    // Create a URL object from the string
    const urlObj = new URL(
      url.startsWith('http')
        ? url
        : `${getBaseUrl()}${url.startsWith('/') ? url : `/${url}`}`
    );

    // Check if the pathname matches the event registration pattern
    return urlObj.pathname.match(/^\/events\/[^/]+\/register(\/.*)?$/) !== null;
  } catch (error) {
    logger.error('Error checking if URL is an event registration URL:', error);
    return false;
  }
}

/**
 * Get the event ID from an event URL
 * @param url The URL to extract the event ID from
 * @returns The event ID or null if not found
 */
export function getEventIdFromUrl(url: string): string | null {
  if (!url) return null;

  try {
    // Create a URL object from the string
    const urlObj = new URL(
      url.startsWith('http')
        ? url
        : `${getBaseUrl()}${url.startsWith('/') ? url : `/${url}`}`
    );

    // Extract the event ID from the pathname
    const match = urlObj.pathname.match(/^\/events\/([^/]+)(\/.*)?$/);
    return match && match[1] ? match[1] : null;
  } catch (error) {
    logger.error('Error extracting event ID from URL:', error);
    return null;
  }
}
