import { NextResponse } from "next/server";
import { createClient } from '@/lib/supabase/pages-client';
import { EventRepository } from "@/repositories/event-repository";

/**
 * Public API endpoint for fetching events
 * This endpoint does not require authentication
 */
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const slug = searchParams.get('slug');
    const id = searchParams.get('id');
    const category = searchParams.get('category');
    const state = searchParams.get('state');

    const eventRepository = new EventRepository();

    // Get a single event by slug
    if (slug) {
      const event = await eventRepository.getEventBySlug(slug);
      if (!event) {
        return new NextResponse("Event not found", { status: 404 });
      }

      // Get the event images
      const supabase = await createClient();
      const { data: images, error: imagesError } = await supabase
        .from('event_images')
        .select('*')
        .eq('event_id', event.id)
        .order('created_at', { ascending: false });

      if (imagesError) {
        console.error("Error fetching event images:", imagesError);
        // Continue without images
      }

      // Process images if available
      const poster = images?.find(img => img.type === 'poster');
      const gallery = images?.filter(img => img.type === 'gallery') || [];

      return NextResponse.json({
        ...event,
        posterImage: poster ? {
          url: poster.url || '', // Ensure it's never undefined
          path: poster.path || `events/${event.id}/poster` // Use path from DB or create a default
        } : null,
        galleryImages: gallery.map(img => ({
          url: img.url || '', // Ensure it's never undefined
          path: img.path || `events/${event.id}/gallery/${img.id}` // Use path from DB or create a default
        }))
      });
    }

    // Get a single event by ID
    if (id) {
      const event = await eventRepository.getEventById(id);
      if (!event) {
        return new NextResponse("Event not found", { status: 404 });
      }

      // Get the event images
      const supabase = await createClient();
      const { data: images, error: imagesError } = await supabase
        .from('event_images')
        .select('*')
        .eq('event_id', id)
        .order('created_at', { ascending: false });

      if (imagesError) {
        console.error("Error fetching event images:", imagesError);
        // Continue without images
      }

      // Process images if available
      const poster = images?.find(img => img.type === 'poster');
      const gallery = images?.filter(img => img.type === 'gallery') || [];

      return NextResponse.json({
        ...event,
        posterImage: poster ? {
          url: poster.url || '', // Ensure it's never undefined
          path: poster.path || `events/${id}/poster` // Use path from DB or create a default
        } : null,
        galleryImages: gallery.map(img => ({
          url: img.url || '', // Ensure it's never undefined
          path: img.path || `events/${id}/gallery/${img.id}` // Use path from DB or create a default
        }))
      });
    }

    // Get all published events
    let events = await eventRepository.getAllEvents();

    // Filter by status - only return published events
    events = events.filter(event => event.status === 'published');

    // Filter by category if provided
    if (category) {
      events = events.filter(event =>
        event.eventTypeId?.toLowerCase() === category.toLowerCase()
      );
    }

    // Filter by state if provided
    if (state) {
      events = events.filter(event =>
        event.state?.toLowerCase() === state.toLowerCase()
      );
    }

    // Fetch poster images for each event
    if (events.length > 0) {
      const supabase = await createClient();
      const eventIds = events.map(event => event.id);

      const { data: eventImages } = await supabase
        .from('event_images')
        .select('*')
        .in('event_id', eventIds)
        .eq('type', 'poster');

      // Add poster URLs to events
      if (eventImages) {
        const posterMap = eventImages.reduce((acc, img) => {
          // Use type assertion to handle potential type errors
          const typedImg = img as unknown;
          if (typedImg && typedImg.event_id) {
            acc[typedImg.event_id] = typedImg.url || '';
          }
          return acc;
        }, {} as Record<string, string>);

        // Create a new array with the updated events
        const eventsWithPosters = events.map(event => {
          // Create a new object for each event
          const updatedEvent = { ...event };

          // Add the poster image if available
          if (posterMap[event.id]) {
            updatedEvent.posterImage = {
              url: posterMap[event.id] || '', // Ensure it's never undefined
              path: `events/${event.id}/poster`
            };
          } else {
            updatedEvent.posterImage = null;
          }

          return updatedEvent;
        });

        // Replace the events array
        events = eventsWithPosters;
      }
    }

    return NextResponse.json(events);
  } catch (error) {
    console.error("Error fetching public events:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
