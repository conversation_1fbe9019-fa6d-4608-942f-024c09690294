'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { updatePassword } from '@/lib/supabase/auth'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { PasswordStrengthIndicator } from '@/components/ui/password-strength'

/**
 * Update Password Form Component
 *
 * This component allows users to set a new password after
 * clicking a password reset link from their email.
 */
export default function UpdatePasswordForm() {
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const [success, setSuccess] = useState(false)
  const [passwordStrength, setPasswordStrength] = useState(0)
  const router = useRouter()
  const supabase = createClient()

  // Check if we have a valid session
  useEffect(() => {
    const checkSession = async () => {
      const { data: { session } } = await supabase.auth.getSession()

      if (!session) {
        // No session, redirect to sign-in
        router.push('/sign-in?error=Invalid+or+expired+password+reset+link')
      }
    }

    checkSession()
  }, [router, supabase])

  // Check password strength
  useEffect(() => {
    if (!password) {
      setPasswordStrength(0)
      return
    }

    let strength = 0

    // Length check
    if (password.length >= 8) strength += 1

    // Contains uppercase
    if (/[A-Z]/.test(password)) strength += 1

    // Contains lowercase
    if (/[a-z]/.test(password)) strength += 1

    // Contains number
    if (/[0-9]/.test(password)) strength += 1

    // Contains special character
    if (/[^A-Za-z0-9]/.test(password)) strength += 1

    setPasswordStrength(strength)
  }, [password])

  /**
   * Handle form submission
   * @param e Form event
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    // Validate passwords match
    if (password !== confirmPassword) {
      setError('Passwords do not match')
      setLoading(false)
      return
    }

    // Validate password strength
    if (password.length < 8) {
      setError('Password must be at least 8 characters long')
      setLoading(false)
      return
    }

    // Require minimum password strength
    if (passwordStrength < 3) {
      setError('Please use a stronger password with a mix of uppercase, lowercase, numbers, or special characters')
      setLoading(false)
      return
    }

    try {
      // Use the auth utility function to update the password
      await updatePassword(password)

      setSuccess(true)

      // Redirect to dashboard after 3 seconds
      setTimeout(() => {
        router.push('/dashboard')
      }, 3000)
    } catch (err: unknown) {
      setError(err.message || 'An error occurred while updating your password')
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return (
      <div className="w-full max-w-md mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold">Password Updated</h1>
          <p className="text-muted-foreground mt-2">
            Your password has been successfully updated. You will be redirected to the dashboard.
          </p>
        </div>

        <Alert className="mt-4">
          <AlertDescription>
            Your password has been updated successfully. You can now use your new password to sign in.
          </AlertDescription>
        </Alert>

        <Button
          className="w-full mt-4"
          onClick={() => router.push('/dashboard')}
        >
          Go to Dashboard
        </Button>
      </div>
    )
  }

  return (
    <div className="w-full max-w-md mx-auto space-y-6">
      <div className="text-center">
        <h1 className="text-2xl font-bold">Update Password</h1>
        <p className="text-muted-foreground mt-2">
          Enter your new password below
        </p>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="password" className="text-foreground">New Password</Label>
          <Input
            id="password"
            type="password"
            placeholder="••••••••"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            disabled={loading}
            aria-describedby="password-description"
            className="bg-background"
          />
          <PasswordStrengthIndicator strength={passwordStrength} />
          <p id="password-description" className="text-xs text-muted-foreground">
            Password must be at least 8 characters and include a mix of uppercase, lowercase, numbers, or special characters
          </p>
        </div>

        <div className="space-y-2">
          <Label htmlFor="confirmPassword" className="text-foreground">Confirm New Password</Label>
          <Input
            id="confirmPassword"
            type="password"
            placeholder="••••••••"
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            required
            disabled={loading}
            className="bg-background"
          />
        </div>

        <Button
          type="submit"
          className="w-full"
          disabled={loading}
          aria-label={loading ? "Updating password..." : "Update password"}
        >
          {loading ? 'Updating password...' : 'Update Password'}
        </Button>
      </form>
    </div>
  )
}
