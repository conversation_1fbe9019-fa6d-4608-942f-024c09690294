'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from '@/components/ui/use-toast';
import {
  Loader2,
  Download,
  FileJson,
  FileText,
  Database,
  CalendarClock,
  Shield,
  AlertTriangle,
  CheckCircle2,
  Clock,
  XCircle,
  Mail
} from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { requestDataExport, getUserDataExports } from '@/app/actions/data-export';
import { cn } from '@/lib/utils';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { type DataExport } from '@/lib/schemas/data-export-db';
import { logger } from '@/lib/logger';

type ExportFormat = 'json' | 'csv';
type ExportType = 'all' | 'profile' | 'activity' | 'consents';

interface ExportOption {
  id: ExportType;
  title: string;
  description: string;
  icon: React.ReactNode;
}

interface ExportHistoryItem {
  id: string;
  status: string;
  exportFormat: ExportFormat;
  exportType: ExportType;
  requestedAt: string;
  completedAt?: string;
  expiresAt?: string;
  downloadCount: string;
}

interface DataExportProps {
  userId?: string;
}

export function DataExport({ userId }: DataExportProps) {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [selectedFormat, setSelectedFormat] = useState<ExportFormat>('json');
  const [selectedType, setSelectedType] = useState<ExportType>('all');
  const [confirmationOpen, setConfirmationOpen] = useState(false);
  const [emailConfirmation, setEmailConfirmation] = useState('');
  const [emailError, setEmailError] = useState<string | null>(null);
  const [lastRequestTime, setLastRequestTime] = useState<Date | null>(null);
  const [activeTab, setActiveTab] = useState('request');
  const [exportHistory, setExportHistory] = useState<ExportHistoryItem[]>([]);
  const [loadingHistory, setLoadingHistory] = useState(false);
  const [downloadingId, setDownloadingId] = useState<string | null>(null);

  // Fetch export history when the component mounts or tab changes
  useEffect(() => {
    if (activeTab === 'history') {
      fetchExportHistory();
    }
  }, [activeTab]);

  const fetchExportHistory = async () => {
    setLoadingHistory(true);
    try {
      const { exports, error } = await getUserDataExports();

      if (error) {
        logger.error('Error fetching export history:', error);
        toast({
          title: "Error",
          description: "Failed to load export history. Please try again.",
          variant: "destructive",
        });
        return;
      }

      if (exports && exports.length > 0) {
        // Transform Supabase DataExport objects to ExportHistoryItem format
        const transformedExports: ExportHistoryItem[] = exports.map((export_) => ({
          id: export_.id,
          status: export_.status,
          exportFormat: export_.export_format,
          exportType: export_.export_type,
          requestedAt: export_.requested_at,
          completedAt: export_.completed_at,
          expiresAt: export_.expires_at,
          downloadCount: export_.download_count,
        }));
        setExportHistory(transformedExports);
      } else {
        setExportHistory([]);
      }
    } catch (error) {
      logger.error('Error fetching exports:', error);
      toast({
        title: "Error",
        description: "Failed to load export history",
        variant: "destructive",
      });
    } finally {
      setLoadingHistory(false);
    }
  };

  const exportOptions: ExportOption[] = [
    {
      id: 'all',
      title: 'Complete Data',
      description: 'Export all your personal data, including profile, activity history, contacts, and privacy consents.',
      icon: <Database className="h-8 w-8 text-primary" />,
    },
    {
      id: 'profile',
      title: 'Profile Data',
      description: 'Export only your profile information and saved contacts.',
      icon: <FileText className="h-8 w-8 text-blue-500" />,
    },
    {
      id: 'activity',
      title: 'Activity History',
      description: 'Export your activity logs and emergency contact access history.',
      icon: <CalendarClock className="h-8 w-8 text-amber-500" />,
    },
    {
      id: 'consents',
      title: 'Privacy Consents',
      description: 'Export your privacy consent history and current settings.',
      icon: <Shield className="h-8 w-8 text-green-500" />,
    },
  ];

  const handleExportRequest = async () => {
    // Reset any previous error
    setEmailError(null);

    // Validate that the email matches the user&apos;s registered email
    if (!user?.email) {
      setEmailError("Unable to verify your account email. Please try again later.");
      return;
    }

    if (emailConfirmation.trim().toLowerCase() !== user.email.trim().toLowerCase()) {
      setEmailError("The email you entered doesn't match your registered email address.");
      return;
    }

    setLoading(true);
    try {
      const { success, message, error, exportId } = await requestDataExport({
        format: selectedFormat,
        type: selectedType,
        email: user.email // Always use the verified user email from the session
      });

      if (error || !success) {
        throw new Error(error || 'Failed to request data export');
      }

      setLastRequestTime(new Date());
      setConfirmationOpen(false);

      toast({
        title: "Export Request Submitted",
        description: message || "Your data export has been requested. You'll receive an email when it&apos;s ready to download.",
      });

      // Switch to history tab to show the new request
      setActiveTab('history');
      fetchExportHistory();
    } catch (error) {
      logger.error("Error requesting data export:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to request data export",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleStartExport = () => {
    // Always start with a blank email field for security purposes
    setEmailConfirmation('');

    // Reset any previous error
    setEmailError(null);

    setConfirmationOpen(true);
  };

  const handleDownload = async (exportId: string, format: string) => {
    setDownloadingId(exportId);

    try {
      // Navigate to the API endpoint to download the file
      window.location.href = `/api/export/${exportId}`;

      // Since we&apos;re navigating away, no need to setDownloadingId(null)
      // But we'll do it anyway after a delay to ensure UI updates before navigation
      setTimeout(() => {
        setDownloadingId(null);
      }, 2000);
    } catch (error) {
      logger.error('Error downloading export:', error);
      toast({
        title: "Download Failed",
        description: "Failed to download data export. Please try again.",
        variant: "destructive",
      });
      setDownloadingId(null);
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";

    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'requested':
        return <Badge variant="outline" className="bg-blue-50 text-blue-600 border-blue-200">Requested</Badge>;
      case 'processing':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-600 border-yellow-200">Processing</Badge>;
      case 'completed':
        return <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">Completed</Badge>;
      case 'failed':
        return <Badge variant="outline" className="bg-red-50 text-red-600 border-red-200">Failed</Badge>;
      case 'expired':
        return <Badge variant="outline" className="bg-gray-50 text-gray-600 border-gray-200">Expired</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'requested':
        return <Clock className="h-4 w-4 text-blue-500" />;
      case 'processing':
        return <Loader2 className="h-4 w-4 text-yellow-500 animate-spin" />;
      case 'completed':
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'expired':
        return <AlertTriangle className="h-4 w-4 text-gray-500" />;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="request">Request Export</TabsTrigger>
          <TabsTrigger value="history">Export History</TabsTrigger>
        </TabsList>

        <TabsContent value="request" className="space-y-4 mt-6">
          <div className="flex flex-col gap-y-2">
            <h2 className="text-xl font-semibold tracking-tight">Export Your Data</h2>
            <p className="text-muted-foreground text-sm">
              Download a copy of your personal data in compliance with data protection regulations.
            </p>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-2">
            {exportOptions.map((option) => (
              <Card
                key={option.id}
                className={cn(
                  "cursor-pointer transition-all hover:shadow-md",
                  selectedType === option.id ? "border-primary ring-1 ring-primary" : ""
                )}
                onClick={() => setSelectedType(option.id)}
              >
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    {option.icon}
                    {selectedType === option.id && (
                      <div className="w-4 h-4 rounded-full bg-primary"></div>
                    )}
                  </div>
                  <CardTitle className="text-lg">{option.title}</CardTitle>
                  <CardDescription>{option.description}</CardDescription>
                </CardHeader>
              </Card>
            ))}
          </div>

          <div className="flex flex-col gap-4 mt-6">
            <div>
              <h3 className="font-medium mb-2">Export Format</h3>
              <div className="flex gap-2">
                <div className="relative">
                  <input
                    type="radio"
                    id="format-json"
                    name="exportFormat"
                    value="json"
                    checked={selectedFormat === 'json'}
                    onChange={() => setSelectedFormat('json')}
                    className="sr-only" // Visually hidden but accessible
                  />
                  <label
                    htmlFor="format-json"
                    className={cn(
                      "flex items-center justify-center gap-2 h-10 px-4 py-2 w-24 rounded-md text-sm font-medium transition-all cursor-pointer hover:shadow-sm",
                      selectedFormat === 'json'
                        ? "border-primary ring-1 ring-primary"
                        : "border border-input"
                    )}
                  >
                    <FileJson className="h-4 w-4" />
                    JSON
                    {selectedFormat === 'json' && (
                      <div className="w-2 h-2 rounded-full bg-primary ml-1"></div>
                    )}
                  </label>
                </div>

                <div className="relative">
                  <input
                    type="radio"
                    id="format-csv"
                    name="exportFormat"
                    value="csv"
                    checked={selectedFormat === 'csv'}
                    onChange={() => setSelectedFormat('csv')}
                    className="sr-only" // Visually hidden but accessible
                  />
                  <label
                    htmlFor="format-csv"
                    className={cn(
                      "flex items-center justify-center gap-2 h-10 px-4 py-2 w-24 rounded-md text-sm font-medium transition-all cursor-pointer hover:shadow-sm",
                      selectedFormat === 'csv'
                        ? "border-primary ring-1 ring-primary"
                        : "border border-input"
                    )}
                  >
                    <FileText className="h-4 w-4" />
                    CSV
                    {selectedFormat === 'csv' && (
                      <div className="w-2 h-2 rounded-full bg-primary ml-1"></div>
                    )}
                  </label>
                </div>
              </div>
            </div>

            <Button
              className="mt-4 w-full md:w-auto"
              onClick={handleStartExport}
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Download className="mr-2 h-4 w-4" />
                  Request Data Export
                </>
              )}
            </Button>

            {lastRequestTime && (
              <div className="bg-muted p-3 rounded-md text-sm mt-4">
                <div className="flex gap-2 items-center text-muted-foreground">
                  <CalendarClock className="h-4 w-4" />
                  <span>
                    Last export requested: {lastRequestTime.toLocaleString()}
                  </span>
                </div>
              </div>
            )}

            <div className="mt-4 bg-yellow-50 border border-yellow-200 rounded-md p-4">
              <div className="flex gap-2">
                <AlertTriangle className="h-5 w-5 text-yellow-600 flex-shrink-0 mt-0.5" />
                <div className="text-sm text-yellow-800">
                  <p className="font-medium mb-1">Important Privacy Information</p>
                  <p>
                    Your data export will be prepared and sent to your email address.
                    The process may take up to 24 hours to complete. The export link will be valid for 7 days.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="history" className="space-y-4 mt-6">
          <div className="flex flex-col gap-y-2">
            <h2 className="text-xl font-semibold tracking-tight">Export History</h2>
            <p className="text-muted-foreground text-sm">
              View and download your previously requested data exports
            </p>
          </div>

          {loadingHistory ? (
            <div className="flex justify-center p-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : exportHistory.length === 0 ? (
            <Card className="border-dashed">
              <CardContent className="flex flex-col items-center justify-center py-8 text-center">
                <Database className="h-12 w-12 text-muted-foreground mb-3 opacity-50" />
                <h3 className="text-lg font-medium mb-1">No export history found</h3>
                <p className="text-sm text-muted-foreground max-w-md mb-4">
                  You haven&apos;t requested any data exports yet. Use the Request Export tab to create your first data export.
                </p>
                <Button variant="outline" onClick={() => setActiveTab('request')}>
                  Request New Export
                </Button>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="p-0 overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[100px]">Status</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Format</TableHead>
                      <TableHead className="hidden md:table-cell">Requested</TableHead>
                      <TableHead className="hidden md:table-cell">Expires</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {exportHistory.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-1.5">
                            {getStatusIcon(item.status)}
                            <span className="sr-only">{item.status}</span>
                            {getStatusBadge(item.status)}
                          </div>
                        </TableCell>
                        <TableCell className="capitalize">
                          {item.exportType}
                        </TableCell>
                        <TableCell className="uppercase">
                          {item.exportFormat}
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          {formatDate(item.requestedAt)}
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          {formatDate(item.expiresAt)}
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            size="sm"
                            variant={item.status === 'completed' ? 'default' : 'outline'}
                            disabled={item.status !== 'completed' || downloadingId === item.id}
                            onClick={() => handleDownload(item.id, item.exportFormat)}
                          >
                            {downloadingId === item.id ? (
                              <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                            ) : (
                              <Download className="mr-1 h-3 w-3" />
                            )}
                            Download
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      <Dialog open={confirmationOpen} onOpenChange={setConfirmationOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Data Export</DialogTitle>
            <DialogDescription>
              Please enter your email address to confirm this data export request.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <div>
                <Label htmlFor="email">Email Address</Label>
              </div>
              <Input
                id="email"
                type="email"
                placeholder="Enter your email"
                value={emailConfirmation}
                onChange={(e) => {
                  setEmailConfirmation(e.target.value);
                  // Clear error when user types
                  if (emailError) setEmailError(null);
                }}
                className={emailError ? "border-red-500" : ""}
                autoComplete="off"
                autoFocus
              />
              {emailError && (
                <div className="text-sm text-red-500 flex items-center gap-1 mt-1">
                  <XCircle className="h-4 w-4" />
                  {emailError}
                </div>
              )}
            </div>

            <div className="flex gap-2 items-start bg-muted p-3 rounded-md">
              <Mail className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium mb-1">Email Verification Required</p>
                <p>
                  For security reasons, please enter your email address to confirm this data export request.
                  A download link will be sent to this email when your export is ready.
                </p>
              </div>
            </div>
          </div>

          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button
              onClick={handleExportRequest}
              disabled={loading || !emailConfirmation}
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : "Confirm Export"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
