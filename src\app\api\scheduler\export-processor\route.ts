import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase/pages-client';
import { logger } from '@/lib/logger';

/**
 * Scheduler API endpoint to process pending export requests
 * This would typically be called by a scheduler/cron job
 * GET /api/scheduler/export-processor
 */
export async function GET(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  const secretKey = process.env.SCHEDULER_API_KEY;
  
  // Check if the request is authorized with the correct API key
  if (!secretKey || authHeader !== `Bearer ${secretKey}`) {
    return NextResponse.json(
      { error: 'Unauthorized' },
      { status: 401 }
    );
  }
  
  try {
    // Initialize Supabase client
    const supabase = await createAdminClient();
    
    // Get exports with requested status
    const { data: pendingExports, error: fetchError } = await supabase
      .from('data_exports')
      .select('*')
      .eq('status', 'requested')
      .eq('is_deleted', false)
      .limit(20); // Process in batches
    
    if (fetchError) {
      logger.error('Error fetching pending exports:', fetchError);
      return NextResponse.json(
        { error: `Failed to fetch pending exports: ${fetchError.message}` },
        { status: 500 }
      );
    }
    
    if (!pendingExports || pendingExports.length === 0) {
      return NextResponse.json({ message: 'No pending exports to process' });
    }
    
    const results = {
      total: pendingExports.length,
      processed: 0,
      completed: 0,
      failed: 0,
      details: [] as any[]
    };
    
    // Process each export
    for (const exportRequest of pendingExports) {
      try {
        // Update to processing status
        const { error: updateError } = await supabase
          .from('data_exports')
          .update({ 
            status: 'processing',
            processed_at: new Date().toISOString()
          })
          .eq('id', exportRequest.id);
          
        if (updateError) {
          logger.error(`Error updating export ${exportRequest.id} to processing:`, updateError);
          continue; // Skip this export
        }
        
        results.processed++;
        
        // Generate the export data based on type and format
        // In a real implementation, this would process the data and store it
        // in a secure location (like S3) for the user to download
        
        // For demo purposes, we'll just simulate a random success/failure
        const isSuccess = Math.random() > 0.1; // 90% success rate
        
        if (isSuccess) {
          // Generate a mock download URL
          // In production, this would be a signed URL to a secure storage location
          const downloadUrl = `/api/export/${exportRequest.id}`;
          
          // Set expiry date (7 days from now)
          const expiryDate = new Date();
          expiryDate.setDate(expiryDate.getDate() + 7);
          
          // Update the export request with download URL and completed status
          const { error: completeError } = await supabase
            .from('data_exports')
            .update({ 
              status: 'completed',
              completed_at: new Date().toISOString(),
              download_url: downloadUrl,
              expires_at: expiryDate.toISOString()
            })
            .eq('id', exportRequest.id);
          
          if (completeError) {
            logger.error(`Error updating export ${exportRequest.id} to completed:`, completeError);
            continue;
          }
          
          results.completed++;
          results.details.push({
            id: exportRequest.id,
            status: 'completed',
            type: exportRequest.export_type,
            format: exportRequest.export_format
          });
          
          // In a real implementation, you would send an email to the user with the download link
          logger.info(`Export ${exportRequest.id} processed successfully. Email would be sent to ${exportRequest.email}`);
        } else {
          // Simulate a failure for testing purposes
          const { error: failError } = await supabase
            .from('data_exports')
            .update({ 
              status: 'failed',
              error_message: 'Failed to process export. Random failure for testing.',
              completed_at: new Date().toISOString()
            })
            .eq('id', exportRequest.id);
          
          if (failError) {
            logger.error(`Error updating export ${exportRequest.id} to failed:`, failError);
            continue;
          }
          
          results.failed++;
          results.details.push({
            id: exportRequest.id,
            status: 'failed',
            type: exportRequest.export_type,
            format: exportRequest.export_format,
            error: 'Random failure for testing'
          });
          
          logger.info(`Export ${exportRequest.id} failed. Email would be sent to ${exportRequest.email}`);
        }
      } catch (_error) {
        logger.error(`Error processing export ${exportRequest.id}:`, _error);
        
        // Update the export request with failure status
        const { error: failError } = await supabase
          .from('data_exports')
          .update({ 
            status: 'failed',
            error_message: `Failed to process export: ${error instanceof Error ? error.message : 'Unknown error'}`,
            completed_at: new Date().toISOString()
          })
          .eq('id', exportRequest.id);
        
        if (failError) {
          logger.error(`Error updating export ${exportRequest.id} to failed status:`, failError);
        }
        
        results.failed++;
        results.details.push({
          id: exportRequest.id,
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
    
    return NextResponse.json({ results });
  } catch (_error) {
    logger.error('Error processing export requests:', _error);
    return NextResponse.json(
      { 
        error: 'Failed to process export requests',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
} 