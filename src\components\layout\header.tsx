import { logger } from '@/lib/logger';
"use client"

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Menu } from 'lucide-react'
import { cn } from '@/lib/utils'
import { MobileMenu } from './mobile-menu'
import { Logo } from './Logo'
import { AuthButtons } from './auth-buttons'
import { useAuth } from '@/contexts/auth-context'
import { Button } from '@/components/ui/button'
import { UserAvatarMenu } from './user-avatar-menu'

// Create a simple sidebar state store
const SIDEBAR_STATE_KEY = 'fuiyoo-sidebar-open'

export function Header() {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const { isSignedIn, user, loading, refreshSession } = useAuth()
  const [isMounted, setIsMounted] = useState(false)

  // Set mounted state on client side
  useEffect(() => {
    setIsMounted(true)
    // Force a session refresh when the component mounts
    refreshSession()
  }, [refreshSession])

  // Debug logging
  useEffect(() => {
    logger.info('Header auth state:', {
      isSignedIn,
      hasUser: !!user,
      loading,
      isMounted
    })
  }, [isSignedIn, user, loading, isMounted])

  // Toggle sidebar function - for dashboard compatibility
  const toggleSidebar = () => {
    const newState = !isSidebarOpen;
    setIsSidebarOpen(newState);
    // Store in localStorage to share state with dashboard component
    localStorage.setItem(SIDEBAR_STATE_KEY, JSON.stringify(newState));
    // Dispatch a custom event that the dashboard can listen for
    window.dispatchEvent(new CustomEvent('sidebar-toggle', { detail: { isOpen: newState } }));
  };

  // Toggle mobile menu for main navigation
  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <header className="sticky top-0 w-full bg-[hsl(var(--background))] border-b border-[hsl(var(--border))] shadow-sm z-40">
      <div className="container mx-auto px-4 md:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Logo />

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/" className="text-sm font-medium text-[hsl(var(--foreground))] hover:text-[hsl(var(--primary))]">
              Home
            </Link>
            <Link href="/about" className="text-sm font-medium text-[hsl(var(--foreground))] hover:text-[hsl(var(--primary))]">
              About Us
            </Link>
            <Link href="/contact" className="text-sm font-medium text-[hsl(var(--foreground))] hover:text-[hsl(var(--primary))]">
              Contact Us
            </Link>

            {/* Show dashboard link only when signed in */}
            {isSignedIn && (
              <Link href="/dashboard" className="text-sm font-medium text-[hsl(var(--foreground))] hover:text-[hsl(var(--primary))]">
                Dashboard
              </Link>
            )}
          </nav>

          {/* Auth buttons and mobile menu toggle */}
          <div className="flex items-center space-x-4">
            {/* Auth buttons - use the AuthButtons component for better state handling */}
            <div className="hidden md:flex items-center space-x-2">
              <AuthButtons />
            </div>

            {/* Mobile menu toggle */}
            <button
              type="button"
              className="md:hidden p-2 rounded-md text-[hsl(var(--muted-foreground))] hover:text-[hsl(var(--foreground))] hover:bg-[hsl(var(--primary-50))]"
              onClick={() => setIsMobileMenuOpen(true)}
            >
              <span className="sr-only">Open menu</span>
              <Menu className="h-6 w-6" />
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <MobileMenu
        isOpen={isMobileMenuOpen}
        setIsOpen={setIsMobileMenuOpen}
      />
    </header>
  )
}