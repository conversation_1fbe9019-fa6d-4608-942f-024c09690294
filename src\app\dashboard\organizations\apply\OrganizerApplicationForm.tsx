"use client";

import { useState, useEffect } from "react";
import { z } from "zod";
import { use<PERSON><PERSON>, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { createOrganizerApplication, saveApplicationDraft } from "./actions";
import { debounce } from "lodash";
import { OrganizationApplicationData } from "../../../../types/roles";

// Form validation schema
const organizationSchema = z.object({
  // Step 1: Organization Information
  organizationName: z.string().min(2, "Organization name is required"),
  organizationType: z.string(), // Changed from enum to match OrganizationApplicationData
  description: z.string().min(20, "Please provide a more detailed description"),
  website: z.string().url().optional().or(z.literal("")),

  // Step 2: Contact Details
  contactName: z.string().min(2, "Contact name is required"),
  contactEmail: z.string().email("Please enter a valid email"),
  contactPhone: z.string().min(10, "Please enter a valid phone number"),
  address: z.object({
    street: z.string().min(2, "Street address is required"),
    city: z.string().min(2, "City is required"),
    state: z.string().min(2, "State/Province is required"),
    postalCode: z.string().min(2, "Postal code is required"),
    country: z.string().min(2, "Country is required"),
  }),

  // Step 3: Document Uploads
  documents: z.object({
    businessRegistration: z.string().optional(),
    taxDocuments: z.string().optional(),
    identificationDocument: z.string().optional(),
    otherDocuments: z.array(z.string()).optional(),
  }).optional(),

  // Step 4: Event Experience
  experienceDescription: z.string().min(20, "Please provide more details about your experience").optional(),

  // Track completed steps
  completedSteps: z.array(z.string()).optional(),
});

type ApplicationFormData = z.infer<typeof organizationSchema>;

interface OrganizerApplicationFormProps {
  userId: string;
  savedData: OrganizationApplicationData | null;
  currentStep: number;
}

export default function OrganizerApplicationForm({
  userId,
  savedData,
  currentStep: initialStep = 1
}: OrganizerApplicationFormProps) {
  const [currentStep, setCurrentStep] = useState(initialStep);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);

  const totalSteps = 4; // Updated to include document upload step

  const { register, handleSubmit, formState: { errors }, getValues, trigger, watch } = useForm<ApplicationFormData>({
    resolver: zodResolver(organizationSchema),
    defaultValues: savedData ? {
      ...savedData,
      // Ensure saved data matches the form's expected structure
      organizationType: savedData.organizationType || "business",
      completedSteps: savedData.completedSteps || [],
      // Ensure address is defined
      address: savedData.address || {
        street: "",
        city: "",
        state: "",
        postalCode: "",
        country: "",
      }
    } : {
      organizationName: "",
      organizationType: "business",
      description: "",
      website: "",
      contactName: "",
      contactEmail: "",
      contactPhone: "",
      address: {
        street: "",
        city: "",
        state: "",
        postalCode: "",
        country: "",
      },
      documents: {
        businessRegistration: "",
        taxDocuments: "",
        identificationDocument: "",
        otherDocuments: [],
      },
      experienceDescription: "",
      completedSteps: [],
    }
  });

  // Auto-save on field changes
  useEffect(() => {
    const subscription = watch((value, { name, type }) => {
      // Only save if it's a change event (not mount)
      if (type === 'change') {
        const debouncedSave = debounce(async () => {
          const formData = getValues();
          // Convert form data to OrganizationApplicationData
          const appData: OrganizationApplicationData = {
            organizationName: formData.organizationName,
            organizationType: formData.organizationType,
            description: formData.description,
            website: formData.website || undefined,
            contactName: formData.contactName,
            contactEmail: formData.contactEmail,
            contactPhone: formData.contactPhone,
            address: formData.address,
            experienceDescription: formData.experienceDescription,
            documents: formData.documents,
            completedSteps: formData.completedSteps || [],
          };
          const result = await saveApplicationDraft(userId, appData, currentStep);
          if (!result.success) {
            console.error('Failed to auto-save:', result.message);
          }
        }, 1000);

        debouncedSave();
      }
    });

    return () => subscription.unsubscribe();
  }, [watch, userId, currentStep]);

  // Move to next step
  const goToNextStep = async () => {
    // Validate current step fields
    let isValid = false;

    if (currentStep === 1) {
      isValid = await trigger(['organizationName', 'organizationType', 'description', 'website']);
    } else if (currentStep === 2) {
      isValid = await trigger(['contactName', 'contactEmail', 'contactPhone', 'address.street', 'address.city', 'address.state', 'address.postalCode', 'address.country']);
    } else if (currentStep === 3) {
      // Document uploads are optional
      isValid = true;
    }

    if (isValid) {
      // Save draft
      const formData = getValues();
      // Convert form data to OrganizationApplicationData
      const appData: OrganizationApplicationData = {
        organizationName: formData.organizationName,
        organizationType: formData.organizationType,
        description: formData.description,
        website: formData.website || undefined,
        contactName: formData.contactName,
        contactEmail: formData.contactEmail,
        contactPhone: formData.contactPhone,
        address: formData.address,
        experienceDescription: formData.experienceDescription,
        documents: formData.documents,
        completedSteps: formData.completedSteps || [],
      };
      const result = await saveApplicationDraft(userId, appData, currentStep);

      if (result.success) {
        setCurrentStep(prev => Math.min(prev + 1, totalSteps));
        setMessage({ type: 'success', text: 'Progress saved' });

        // Clear message after 3 seconds
        setTimeout(() => setMessage(null), 3000);
      } else {
        setMessage({ type: 'error', text: result.message });
      }
    }
  };

  // Go to previous step
  const goToPreviousStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  // Handle file upload
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>, fieldName: string) => {
    const file = e.target.files?.[0];
    if (file) {
      // In a real implementation, you would upload the file to storage
      // and save the URL or file reference
      console.log(`File ${file.name} selected for ${fieldName}`);
      // For now, we'll just store the file name as a placeholder
      const formData = getValues();
      if (fieldName === 'otherDocuments') {
        if (!formData.documents) formData.documents = { otherDocuments: [] };
        if (!formData.documents.otherDocuments) formData.documents.otherDocuments = [];
        formData.documents.otherDocuments.push(file.name);
      } else if (formData.documents) {
        (formData.documents as unknown)[fieldName] = file.name;
      }
    }
  };

  // Submit the form
  const onSubmit: SubmitHandler<ApplicationFormData> = async (data) => {
    setIsSubmitting(true);

    try {
      // Convert form data to OrganizationApplicationData
      const appData: OrganizationApplicationData = {
        organizationName: data.organizationName,
        organizationType: data.organizationType,
        description: data.description,
        website: data.website || undefined,
        contactName: data.contactName,
        contactEmail: data.contactEmail,
        contactPhone: data.contactPhone,
        address: data.address,
        experienceDescription: data.experienceDescription,
        documents: data.documents,
        completedSteps: data.completedSteps || [],
      };
      const result = await createOrganizerApplication(userId, appData);

      if (result.success) {
        setMessage({ type: 'success', text: 'Application submitted successfully! We will review your application and get back to you soon.' });
      } else {
        setMessage({ type: 'error', text: result.message });
      }
    } catch (error) {
      console.error("Error submitting application:", error);
      setMessage({ type: 'error', text: 'An unexpected error occurred. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 mb-24"> {/* Added bottom margin to prevent footer overlap */}
      {/* Progress bar */}
      <div className="mb-8">
        <div className="flex justify-between mb-2">
          {Array.from({ length: totalSteps }).map((_, idx) => (
            <div key={idx} className="flex flex-col items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${idx + 1 < currentStep
                ? 'bg-green-500 text-white'
                : idx + 1 === currentStep
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700'
                }`}>
                {idx + 1 < currentStep ? '✓' : idx + 1}
              </div>
              <span className="text-xs mt-1">
                {idx === 0 ? 'Organization' : idx === 1 ? 'Contact' : idx === 2 ? 'Documents' : 'Experience'}
              </span>
            </div>
          ))}
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2.5">
          <div
            className="bg-blue-600 h-2.5 rounded-full"
            style={{ width: `${(currentStep / totalSteps) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* Error/Success message */}
      {message && (
        <div className={`p-4 mb-6 rounded-md ${message.type === 'success' ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'
          }`}>
          {message.text}
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)}>
        {/* Step 1: Organization Information */}
        {currentStep === 1 && (
          <div className="space-y-4">
            <h2 className="text-xl font-semibold mb-4">Organization Information</h2>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Organization Name *
              </label>
              <input
                {...register("organizationName")}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
              {errors.organizationName && (
                <p className="mt-1 text-sm text-red-600">{errors.organizationName.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Organization Type *
              </label>
              <select
                {...register("organizationType")}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="business">Business</option>
                <option value="community">Community</option>
                <option value="nonprofit">Non-profit</option>
                <option value="education">Education</option>
                <option value="government">Government</option>
                <option value="other">Other</option>
              </select>
              {errors.organizationType && (
                <p className="mt-1 text-sm text-red-600">{errors.organizationType.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description *
              </label>
              <textarea
                {...register("description")}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="Describe your organization and the types of events you plan to host"
              ></textarea>
              {errors.description && (
                <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Website (optional)
              </label>
              <input
                type="url"
                {...register("website")}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="https://www.example.com"
              />
              {errors.website && (
                <p className="mt-1 text-sm text-red-600">{errors.website.message}</p>
              )}
            </div>
          </div>
        )}

        {/* Step 2: Contact Details */}
        {currentStep === 2 && (
          <div className="space-y-4">
            <h2 className="text-xl font-semibold mb-4">Contact Details</h2>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Contact Name *
              </label>
              <input
                {...register("contactName")}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
              {errors.contactName && (
                <p className="mt-1 text-sm text-red-600">{errors.contactName.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Contact Email *
              </label>
              <input
                type="email"
                {...register("contactEmail")}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
              {errors.contactEmail && (
                <p className="mt-1 text-sm text-red-600">{errors.contactEmail.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Contact Phone *
              </label>
              <input
                type="tel"
                {...register("contactPhone")}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
              />
              {errors.contactPhone && (
                <p className="mt-1 text-sm text-red-600">{errors.contactPhone.message}</p>
              )}
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Address</h3>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Street *
                </label>
                <input
                  {...register("address.street")}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                />
                {errors.address?.street && (
                  <p className="mt-1 text-sm text-red-600">{errors.address.street.message}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    City *
                  </label>
                  <input
                    {...register("address.city")}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                  {errors.address?.city && (
                    <p className="mt-1 text-sm text-red-600">{errors.address.city.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    State/Province *
                  </label>
                  <input
                    {...register("address.state")}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                  {errors.address?.state && (
                    <p className="mt-1 text-sm text-red-600">{errors.address.state.message}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Postal Code *
                  </label>
                  <input
                    {...register("address.postalCode")}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                  {errors.address?.postalCode && (
                    <p className="mt-1 text-sm text-red-600">{errors.address.postalCode.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Country *
                  </label>
                  <input
                    {...register("address.country")}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  />
                  {errors.address?.country && (
                    <p className="mt-1 text-sm text-red-600">{errors.address.country.message}</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Step 3: Document Uploads */}
        {currentStep === 3 && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold mb-4">Document Uploads</h2>
            <p className="text-gray-600 mb-6">
              Please upload the required documents to verify your organization.
              Supported formats: PDF, JPG, PNG (Max size: 5MB per file)
            </p>

            <div className="space-y-6">
              <div className="border border-gray-200 rounded-md p-4 bg-gray-50">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Business Registration / SSM Certificate *
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="file"
                    id="businessRegistration"
                    accept=".pdf,.jpg,.jpeg,.png"
                    onChange={(e) => handleFileUpload(e, 'businessRegistration')}
                    className="block w-full text-sm text-gray-500
                      file:mr-4 file:py-2 file:px-4
                      file:rounded-md file:border-0
                      file:text-sm file:font-semibold
                      file:bg-blue-50 file:text-blue-700
                      hover:file:bg-blue-100"
                  />
                </div>
                <p className="mt-1 text-xs text-gray-500">Upload your company registration document (SSM)</p>
              </div>

              <div className="border border-gray-200 rounded-md p-4 bg-gray-50">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tax Documents (optional)
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="file"
                    id="taxDocuments"
                    accept=".pdf,.jpg,.jpeg,.png"
                    onChange={(e) => handleFileUpload(e, 'taxDocuments')}
                    className="block w-full text-sm text-gray-500
                      file:mr-4 file:py-2 file:px-4
                      file:rounded-md file:border-0
                      file:text-sm file:font-semibold
                      file:bg-blue-50 file:text-blue-700
                      hover:file:bg-blue-100"
                  />
                </div>
                <p className="mt-1 text-xs text-gray-500">Upload your tax registration document if applicable</p>
              </div>

              <div className="border border-gray-200 rounded-md p-4 bg-gray-50">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Identification Document (optional)
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="file"
                    id="identificationDocument"
                    accept=".pdf,.jpg,.jpeg,.png"
                    onChange={(e) => handleFileUpload(e, 'identificationDocument')}
                    className="block w-full text-sm text-gray-500
                      file:mr-4 file:py-2 file:px-4
                      file:rounded-md file:border-0
                      file:text-sm file:font-semibold
                      file:bg-blue-50 file:text-blue-700
                      hover:file:bg-blue-100"
                  />
                </div>
                <p className="mt-1 text-xs text-gray-500">Upload ID of the organization representative</p>
              </div>

              <div className="border border-gray-200 rounded-md p-4 bg-gray-50">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Other Supporting Documents (optional)
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="file"
                    id="otherDocuments"
                    accept=".pdf,.jpg,.jpeg,.png"
                    multiple
                    onChange={(e) => handleFileUpload(e, 'otherDocuments')}
                    className="block w-full text-sm text-gray-500
                      file:mr-4 file:py-2 file:px-4
                      file:rounded-md file:border-0
                      file:text-sm file:font-semibold
                      file:bg-blue-50 file:text-blue-700
                      hover:file:bg-blue-100"
                  />
                </div>
                <p className="mt-1 text-xs text-gray-500">Any additional documents you want to provide</p>
              </div>
            </div>
          </div>
        )}

        {/* Step 4: Event Experience (was Step 3 before) */}
        {currentStep === 4 && (
          <div className="space-y-4">
            <h2 className="text-xl font-semibold mb-4">Event Experience</h2>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Describe Your Event Experience *
              </label>
              <textarea
                {...register("experienceDescription")}
                rows={6}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="Tell us about your experience organizing events. Include details about types of events, audience sizes, venues, etc."
              ></textarea>
              {errors.experienceDescription && (
                <p className="mt-1 text-sm text-red-600">{errors.experienceDescription.message}</p>
              )}
            </div>

            <div className="mt-8 bg-blue-50 p-4 rounded-md">
              <h3 className="text-lg font-medium text-blue-800 mb-2">Terms and Conditions</h3>
              <p className="text-blue-700 mb-4">
                By submitting this application, you agree to:
              </p>
              <ul className="list-disc ml-5 text-blue-700 mb-4">
                <li>Provide accurate information about your organization</li>
                <li>Comply with our platform&apos;s terms of service and event guidelines</li>
                <li>Undergo verification processes as required</li>
                <li>Maintain proper conduct when hosting events on our platform</li>
              </ul>
            </div>
          </div>
        )}

        {/* Navigation buttons */}
        <div className="mt-8 flex justify-between">
          {currentStep > 1 ? (
            <button
              type="button"
              onClick={goToPreviousStep}
              className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Previous
            </button>
          ) : (
            <div>{/* Empty div for layout */}</div>
          )}

          {currentStep < totalSteps ? (
            <button
              type="button"
              onClick={goToNextStep}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Next
            </button>
          ) : (
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
            >
              {isSubmitting ? "Submitting..." : "Submit Application"}
            </button>
          )}
        </div>
      </form>
    </div>
  );
}