import { logger } from '@/lib/logger';
import { createClient } from '@/lib/supabase/pages-client'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

export const dynamic = 'force-dynamic'

/**
 * API endpoint to check the current session state
 * This is useful for debugging authentication issues
 */
export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = await createClient()

    // Get the current session
    const { data: { session }, error } = await supabase.auth.getSession()

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Get all cookies for debugging
    const allCookies = Array.from(cookieStore.getAll()).map(c => {
      // Use type assertion to handle RequestCookie properties
      const cookieAny = c as unknown;
      return {
        name: c.name,
        // Don't show the actual value for security reasons
        hasValue: !!c.value,
        // Only include these properties if they exist
        ...(cookieAny.expires !== undefined && { expires: cookieAny.expires }),
        ...(cookieAny.path !== undefined && { path: cookieAny.path }),
        ...(cookieAny.domain !== undefined && { domain: cookieAny.domain }),
        ...(cookieAny.secure !== undefined && { secure: cookieAny.secure }),
        ...(cookieAny.httpOnly !== undefined && { httpOnly: cookieAny.httpOnly }),
      };
    })

    // Return session info
    return NextResponse.json({
      authenticated: !!session,
      sessionExists: !!session,
      user: session?.user ? {
        id: session.user.id,
        email: session.user.email,
        // Don't include sensitive data
      } : null,
      cookies: {
        count: allCookies.length,
        names: allCookies.map(c => c.name),
        details: allCookies
      },
      timestamp: new Date().toISOString(),
      // Include request info for debugging
      request: {
        url: request.url,
        method: request.method,
        headers: Object.fromEntries(
          Array.from(request.headers.entries())
            .filter(([key]) => !['cookie', 'authorization'].includes(key.toLowerCase()))
        ),
      }
    })
  } catch (err) {
    logger.error('Error in session-check route:', err)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
