import { PostgrestSingleResponse } from '@supabase/supabase-js';
import { logger } from '@/lib/logger';

/**
 * Base repository for database operations
 */
export class BaseRepository<T extends Record<string, unknown>> {
  protected tableName: string;

  /**
   * Create a new repository
   * @param tableName Database table name
   */
  constructor(tableName: string) {
    this.tableName = tableName;
  }

  /**
   * Get the Supabase client
   */
  protected async getClient() {
    return await createClient();
  }

  /**
   * Find a record by ID
   */
  async findById(id: string): Promise<T | null> {
    try {
      const supabase = await this.getClient();

      // Use type assertion to handle the dynamic table name
      const { data, error: _error } = await supabase
        .from(this.tableName as unknown)
        .select('*')
        .eq('id', id)
        .single();

      if (_error) {
        logger.error(`Error fetching ${this.tableName} by ID:`, _error);
        return null;
      }

      return data as unknown as T;
    } catch (_error) {
      logger.error(`Error in findById for ${this.tableName}:`, _error);
      return null;
    }
  }

  /**
   * Find records by a query
   */
  async find(query: Record<string, unknown> = {}): Promise<T[]> {
    try {
      const supabase = await this.getClient();

      // Use type assertion to handle the dynamic table name
      let queryBuilder = supabase.from(this.tableName as unknown).select('*');

      // Apply filters
      Object.entries(query).forEach(([key, value]) => {
        queryBuilder = queryBuilder.eq(key, value);
      });

      const { data, error: _error } = await queryBuilder;

      if (_error) {
        logger.error(`Error fetching ${this.tableName}:`, _error);
        return [];
      }

      return data as unknown as T[];
    } catch (_error) {
      logger.error(`Error in find for ${this.tableName}:`, _error);
      return [];
    }
  }

  /**
   * Create a new record
   */
  async create(data: Partial<T>): Promise<T | null> {
    try {
      const supabase = await this.getClient();

      // Use type assertion to handle the dynamic table name and data
      const { data: result, error: _error } = await supabase
        .from(this.tableName as unknown)
        .insert(data as unknown)
        .select()
        .single();

      if (_error) {
        logger.error(`Error creating ${this.tableName}:`, _error);
        return null;
      }

      return result as unknown as T;
    } catch (_error) {
      logger.error(`Error in create for ${this.tableName}:`, _error);
      return null;
    }
  }

  /**
   * Update a record
   */
  async update(id: string, data: Partial<T>): Promise<T | null> {
    try {
      const supabase = await this.getClient();

      // Use type assertion to handle the dynamic table name and data
      const { data: result, error: _error } = await supabase
        .from(this.tableName as unknown)
        .update(data as unknown)
        .eq('id', id)
        .select()
        .single();

      if (_error) {
        logger.error(`Error updating ${this.tableName}:`, _error);
        return null;
      }

      return result as unknown as T;
    } catch (_error) {
      logger.error(`Error in update for ${this.tableName}:`, _error);
      return null;
    }
  }

  /**
   * Delete a record
   */
  async delete(id: string): Promise<boolean> {
    try {
      const supabase = await this.getClient();

      // Use type assertion to handle the dynamic table name
      const { error: _error } = await supabase
        .from(this.tableName as unknown)
        .delete()
        .eq('id', id);

      if (_error) {
        logger.error(`Error deleting ${this.tableName}:`, _error);
        return false;
      }

      return true;
    } catch (_error) {
      logger.error(`Error in delete for ${this.tableName}:`, _error);
      return false;
    }
  }
}
