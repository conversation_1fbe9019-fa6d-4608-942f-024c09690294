const fs = require('fs');
const path = require('path');

console.log('🔧 Starting any types fix...');

// Common any type replacements
const replacements = [
  // Function parameters and variables
  { from: /: any\[\]/g, to: ': unknown[]' },
  { from: /: any\s*=/g, to: ': unknown =' },
  { from: /<any>/g, to: '<unknown>' },
  { from: /as any/g, to: 'as unknown' },
  
  // Common parameter names
  { from: /error: any/g, to: 'error: unknown' },
  { from: /data: any/g, to: 'data: unknown' },
  { from: /result: any/g, to: 'result: unknown' },
  { from: /response: any/g, to: 'response: unknown' },
  { from: /payload: any/g, to: 'payload: unknown' },
  { from: /params: any/g, to: 'params: unknown' },
  { from: /props: any/g, to: 'props: unknown' },
  { from: /event: any/g, to: 'event: unknown' },
  { from: /item: any/g, to: 'item: unknown' },
  { from: /value: any/g, to: 'value: unknown' },
  
  // Function signatures
  { from: /\(error: any\)/g, to: '(error: unknown)' },
  { from: /\(data: any\)/g, to: '(data: unknown)' },
  { from: /\(result: any\)/g, to: '(result: unknown)' },
  { from: /\(response: any\)/g, to: '(response: unknown)' },
  { from: /\(payload: any\)/g, to: '(payload: unknown)' },
  { from: /\(params: any\)/g, to: '(params: unknown)' },
  { from: /\(props: any\)/g, to: '(props: unknown)' },
  { from: /\(event: any\)/g, to: '(event: unknown)' },
  { from: /\(item: any\)/g, to: '(item: unknown)' },
  { from: /\(value: any\)/g, to: '(value: unknown)' },
  
  // Catch blocks
  { from: /catch \(error: any\)/g, to: 'catch (error: unknown)' },
  { from: /catch \(err: any\)/g, to: 'catch (err: unknown)' },
  { from: /catch \(e: any\)/g, to: 'catch (e: unknown)' },
  
  // Array and object types
  { from: /Array<any>/g, to: 'Array<unknown>' },
  { from: /Record<string, any>/g, to: 'Record<string, unknown>' },
  { from: /\[key: string\]: any/g, to: '[key: string]: unknown' },
];

// Files to process (TypeScript files)
const filesToProcess = [
  'src/app/actions/activity.ts',
  'src/app/actions/data-export.ts',
  'src/app/api/auth/session-check/route.ts',
  'src/app/auth/reset/page.tsx',
  'src/app/reset-password/reset-password-form.tsx',
  'src/app/update-password/update-password-form.tsx',
  'src/components/profile/activity-history.tsx',
  'src/components/profile/contacts-list.tsx',
  'src/components/profile/privacy-settings.tsx',
  'src/lib/roles.ts',
  'src/utils/imageHandling.ts'
];

let totalFiles = 0;
let totalReplacements = 0;

filesToProcess.forEach(filePath => {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  File not found: ${filePath}`);
    return;
  }

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    let fileReplacements = 0;

    replacements.forEach(({ from, to }) => {
      const matches = content.match(from);
      if (matches) {
        content = content.replace(from, to);
        modified = true;
        fileReplacements += matches.length;
      }
    });

    if (modified) {
      fs.writeFileSync(filePath, content);
      totalFiles++;
      totalReplacements += fileReplacements;
      console.log(`✅ Fixed ${fileReplacements} any types in: ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
});

console.log(`\n📊 Summary:`);
console.log(`   Files processed: ${filesToProcess.length}`);
console.log(`   Files modified: ${totalFiles}`);
console.log(`   Total replacements: ${totalReplacements}`);
console.log('✨ Any types fix completed!\n');
