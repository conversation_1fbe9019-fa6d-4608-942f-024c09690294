#!/bin/bash

# Fix import paths for UI components in profile components
# Just fix the src/components/profile directory now

echo "Fixing imports in src/components/profile..."

find ./src/components/profile -name "*.tsx" -type f -exec sed -i '' 's|@/src/components/ui|@/components/ui|g' {} \;
echo "- Fixed UI component imports"

find ./src/components/profile -name "*.tsx" -type f -exec sed -i '' 's|@/src/lib/schemas|@/lib/schemas|g' {} \;
echo "- Fixed schema imports"

find ./src/components/profile -name "*.tsx" -type f -exec sed -i '' 's|@/src/app/actions|@/app/actions|g' {} \;
echo "- Fixed app actions imports"

find ./src/components/profile -name "*.tsx" -type f -exec sed -i '' 's|@/src/data|@/data|g' {} \;
echo "- Fixed data imports"

# Fix React import syntax and formatting issues
echo "Fixing React import syntax..."
for file in $(find ./src/components/profile -name "*.tsx" -type f); do
  # Get file contents
  content=$(cat "$file")
  
  # Check if we need to add a React import
  if ! grep -q "import React" "$file"; then
    echo "import React from 'react';" > temp_file
    cat "$file" >> temp_file
    mv temp_file "$file"
    echo "- Added React import to $file"
  fi
  
  # Replace redundant React imports in destructuring patterns
  sed -i '' 's/import React, { /import { /g' "$file"
  
  echo "- Fixed React imports in $file"
done

echo "All imports fixed!" 