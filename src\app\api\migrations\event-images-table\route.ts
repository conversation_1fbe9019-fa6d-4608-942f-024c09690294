import { NextResponse } from 'next/server';
import { logger } from '@/lib/logger';

// One-time setup route to create event_images table
// Should be called by an admin user
export async function POST(request: Request) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session || !session.user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const userId = session.user.id;

    // Check if user is an admin (this should be replaced with your actual admin check)
    // In production, you would use a more secure admin check
    const adminCheck = await fetch('/api/admin/check', {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!adminCheck.ok) {
      return new NextResponse("Unauthorized - Admin access required", { status: 403 });
    }

    // Execute the SQL directly using the Supabase REST API
    const res = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/rest/v1/rpc/run_sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY!,
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
      },
      body: JSON.stringify({
        sql: `
          CREATE TABLE IF NOT EXISTS public.event_images (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            event_id UUID NOT NULL REFERENCES public.events(id) ON DELETE CASCADE,
            type TEXT NOT NULL CHECK (type IN ('poster', 'gallery')),
            url TEXT NOT NULL,
            path TEXT NOT NULL,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ,
            UNIQUE(event_id, type) WHERE type = 'poster'
          );

          CREATE INDEX IF NOT EXISTS event_images_event_id_idx ON public.event_images(event_id);
          CREATE INDEX IF NOT EXISTS event_images_type_idx ON public.event_images(type);
        `
      })
    });

    if (!res.ok) {
      logger.error("Error executing SQL:", await res.text());
      return new NextResponse("Error creating event_images table", { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: "Event images table created successfully"
    });
  } catch (_error) {
    logger.error("Error in migration:", _error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}