import { NextResponse } from 'next/server';
import { logger } from '@/lib/logger';

// Get all images for an event
export async function GET(request: Request) {
  try {
    // Get event ID from URL query string
    const url = new URL(request.url);
    const eventId = url.searchParams.get('eventId');

    if (!eventId) {
      return new NextResponse("Event ID is required", { status: 400 });
    }

    // Initialize Supabase client
    const supabase = await createClient();

    // Query the event images from the database
    const { data: eventData, error: eventError } = await supabase
      .from('events')
      .select('organizer_id')
      .eq('id', eventId)
      .single();

    if (eventError) {
      logger.error("Error fetching event:", eventError);
      return new NextResponse("Event not found", { status: 404 });
    }

    // Get poster and gallery images
    const { data: imageData, error: imageError } = await supabase
      .from('event_images')
      .select('*')
      .eq('event_id', eventId)
      .order('created_at', { ascending: false });

    if (imageError) {
      logger.error("Error fetching event images:", imageError);
      return new NextResponse("Error fetching event images", { status: 500 });
    }

    // Separate poster and gallery images
    const poster = imageData.find(img => img.type === 'poster');
    const gallery = imageData.filter(img => img.type === 'gallery');

    return NextResponse.json({
      poster,
      gallery
    });
  } catch (_error) {
    logger.error("Error getting event images:", _error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

// Add a new image to an event
export async function POST(request: Request) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session || !session.user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const userId = session.user.id;

    // Parse request body
    const body = await request.json();
    const { eventId, url, path, type } = body;

    if (!eventId || !url || !path || !type) {
      return new NextResponse("Missing required fields", { status: 400 });
    }

    if (type !== 'poster' && type !== 'gallery') {
      return new NextResponse("Invalid image type", { status: 400 });
    }

    // Supabase client already initialized above

    // Ensure user is the event organizer
    const { data: eventData, error: eventError } = await supabase
      .from('events')
      .select('organizer_id')
      .eq('id', eventId)
      .single();

    if (eventError) {
      logger.error("Error fetching event:", eventError);
      return new NextResponse("Event not found", { status: 404 });
    }

    if (eventData.organizer_id !== userId) {
      return new NextResponse("Unauthorized - Not the event organizer", { status: 403 });
    }

    // For poster type, first check if there's an existing poster
    if (type === 'poster') {
      const { data: existingPoster } = await supabase
        .from('event_images')
        .select('id')
        .eq('event_id', eventId)
        .eq('type', 'poster')
        .single();

      if (existingPoster) {
        // Update existing poster
        const { error: _error } = await supabase
          .from('event_images')
          .update({
            url,
            path,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingPoster.id);

        if (_error) {
          logger.error("Error updating poster:", _error);
          return new NextResponse("Error updating poster", { status: 500 });
        }

        return NextResponse.json({
          success: true,
          message: "Poster updated successfully",
          id: existingPoster.id
        });
      }
    }

    // Insert new image
    // Use a type assertion to avoid TypeScript errors
    const insertData = {
      url,
      path,
      type,
      event_id: eventId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data, error: _error } = await supabase
      .from('event_images')
      .insert([insertData])
      .select();

    if (_error) {
      logger.error("Error adding event image:", _error);
      return new NextResponse("Error adding event image", { status: 500 });
    }

    // Check if data exists and has at least one element
    if (!data || data.length === 0) {
      return NextResponse.json({
        success: true,
        message: type === 'poster' ? "Poster added successfully" : "Gallery image added successfully",
        id: null // No ID available
      });
    }

    return NextResponse.json({
      success: true,
      message: type === 'poster' ? "Poster added successfully" : "Gallery image added successfully",
      id: data[0]?.id || null
    });
  } catch (_error) {
    logger.error("Error adding event image:", _error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

// Delete an event image
export async function DELETE(request: Request) {
  try {
    // Check authentication
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session || !session.user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const userId = session.user.id;

    // Get image ID from URL query string
    const url = new URL(request.url);
    const imageId = url.searchParams.get('id');

    if (!imageId) {
      return new NextResponse("Image ID is required", { status: 400 });
    }

    // Supabase client already initialized above

    // Get the image and related event
    const { data: imageData, error: imageError } = await supabase
      .from('event_images')
      .select('*, events!inner(organizer_id)')
      .eq('id', imageId)
      .single();

    if (imageError) {
      logger.error("Error fetching image:", imageError);
      return new NextResponse("Image not found", { status: 404 });
    }

    // Check if user is the event organizer
    // Use type assertion to handle potential type errors
    const typedImageData = imageData as unknown;
    const organizerId = typedImageData.events?.organizer_id;

    if (organizerId && organizerId !== userId) {
      return new NextResponse("Unauthorized - Not the event organizer", { status: 403 });
    }

    // Delete the image from storage
    // Use the same typedImageData from above
    const imagePath = typedImageData.path;

    if (imagePath) {
      const { error: storageError } = await supabase
        .storage
        .from('public')
        .remove([imagePath]);

      if (storageError) {
        logger.error("Error deleting image from storage:", storageError);
        // Continue with database deletion even if storage deletion fails
      }
    }



    // Delete the image record
    const { error: deleteError } = await supabase
      .from('event_images')
      .delete()
      .eq('id', imageId);

    if (deleteError) {
      logger.error("Error deleting image record:", deleteError);
      return new NextResponse("Error deleting image", { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: "Image deleted successfully"
    });
  } catch (_error) {
    logger.error("Error deleting event image:", _error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}