"use client"

import * as React from "react"
import { createContext, useContext, useCallback, useState, useEffect, useRef } from "react"

/**
 * Toast notification types and interfaces
 */
export interface ToastProps {
  id?: string;
  title?: string;
  description?: string;
  variant?: "default" | "destructive" | "success" | "warning";
  duration?: number;
  action?: React.ReactNode;
  onDismiss?: () => void;
}

interface ToastContextType {
  toast: (props: ToastProps) => { id: string; dismiss: () => void };
  dismiss: (id: string) => void;
  dismissAll: () => void;
}

// Create context with a default value that throws an error if used outside provider
const ToastContext = createContext<ToastContextType>({
  toast: () => {
    throw new Error("useToast must be used within a ToastProvider");
  },
  dismiss: () => {
    throw new Error("useToast must be used within a ToastProvider");
  },
  dismissAll: () => {
    throw new Error("useToast must be used within a ToastProvider");
  },
});

/**
 * Toast provider component
 */
export function ToastProvider({ children }: { children: React.ReactNode }) {
  const [toasts, setToasts] = useState<(ToastProps & { id: string })[]>([]);
  const toastTimeoutsRef = useRef<Map<string, NodeJS.Timeout>>(new Map());

  // Clean up timeouts on unmount
  useEffect(() => {
    return () => {
      toastTimeoutsRef.current.forEach((timeout) => clearTimeout(timeout));
      toastTimeoutsRef.current.clear();
    };
  }, []);

  // Add a new toast
  const toast = useCallback((props: ToastProps) => {
    const id = props.id || `toast-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    const duration = props.duration || 5000;

    setToasts((prev) => [...prev, { ...props, id }]);

    // Set auto-dismiss timeout
    if (duration > 0) {
      const timeout = setTimeout(() => {
        dismiss(id);
      }, duration);

      toastTimeoutsRef.current.set(id, timeout);
    }

    return {
      id,
      dismiss: () => dismiss(id),
    };
  }, []);

  // Dismiss a specific toast
  const dismiss = useCallback((id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id));

    // Clear the timeout
    if (toastTimeoutsRef.current.has(id)) {
      clearTimeout(toastTimeoutsRef.current.get(id)!);
      toastTimeoutsRef.current.delete(id);
    }
  }, []);

  // Dismiss all toasts
  const dismissAll = useCallback(() => {
    setToasts([]);

    // Clear all timeouts
    toastTimeoutsRef.current.forEach((timeout) => clearTimeout(timeout));
    toastTimeoutsRef.current.clear();
  }, []);

  return (
    <ToastContext.Provider value={{ toast, dismiss, dismissAll }}>
      {children}
      <div className="fixed bottom-4 right-4 z-50 flex flex-col gap-2 max-w-md">
        {toasts.map((toast) => (
          <div
            key={toast.id}
            className={`rounded-md border p-4 shadow-md transition-all animate-in fade-in slide-in-from-right-5 ${toast.variant === "destructive"
                ? "bg-destructive text-destructive-foreground border-destructive/20"
                : toast.variant === "success"
                  ? "bg-green-50 border-green-200 text-green-900"
                  : toast.variant === "warning"
                    ? "bg-amber-50 border-amber-200 text-amber-900"
                    : "bg-background border-border"
              }`}
            role="alert"
          >
            <div className="flex justify-between items-start">
              <div className="flex-1">
                {toast.title && (
                  <div className="font-medium mb-1">{toast.title}</div>
                )}
                {toast.description && (
                  <div className="text-sm opacity-90">{toast.description}</div>
                )}
              </div>
              <button
                onClick={() => dismiss(toast.id)}
                className="ml-4 text-gray-400 hover:text-gray-600"
                aria-label="Close toast"
              >
                ×
              </button>
            </div>
            {toast.action && (
              <div className="mt-2">
                {toast.action}
              </div>
            )}
          </div>
        ))}
      </div>
    </ToastContext.Provider>
  );
}

/**
 * Hook to use toast functionality
 */
export function useToast() {
  const context = useContext(ToastContext);

  if (!context) {
    throw new Error("useToast must be used within a ToastProvider");
  }

  return context;
}

/**
 * Global toast function for use outside of React components
 * Falls back to alert() if used outside of the provider
 */
export const toast = (props: ToastProps) => {
  // If used outside provider or in server context, show a fallback
  if (typeof window === "undefined") {
    return { id: "server-side", dismiss: () => { } };
  }

  try {
    // Try to access the context
    const context = useContext(ToastContext);
    if (context) {
      return context.toast(props);
    }
  } catch (e) {
    // If context is not available, use fallback
    console.warn("Toast used outside provider, using fallback");
    alert(`${props.title || ""}${props.title && props.description ? ": " : ""}${props.description || ""}`);
    return { id: "fallback", dismiss: () => { } };
  }

  return { id: "fallback", dismiss: () => { } };
}