import { NextResponse } from 'next/server';
import { logger } from '@/lib/logger';

export async function GET() {
  try {
    // Check authentication to protect the migration route
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session || !session.user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const userId = session.user.id;

    // Supabase client already initialized above

    // Check if the table already exists by trying to query it
    try {
      await supabase.from('users').select('id').limit(1);
      // If we get here, the table exists
      return NextResponse.json({
        success: true,
        message: "Users table already exists",
        created: false
      });
    } catch (_error) {
      // Table doesn't exist, continue with creation
      logger.info("Users table doesn't exist, creating it now...");
    }

    // Need to execute raw SQL through server-side code
    // This endpoint just confirms the need for the migration

    return NextResponse.json({
      success: true,
      message: "Migration needed: Please run the SQL script to create the users table",
      sql: `
CREATE TABLE IF NOT EXISTS public.users (
  id UUID PRIMARY KEY,
  external_id TEXT NOT NULL UNIQUE,
  email TEXT,
  first_name TEXT,
  last_name TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_users_external_id ON public.users (external_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON public.users (email);
      `
    });
  } catch (_error) {
    logger.error("Error checking users table:", _error);
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : "Unknown error",
    }, { status: 500 });
  }
}