import { getEventTypes } from '@/app/actions/events';
import { seedEventTypes } from '@/lib/db/seed-data';
import { EventWizardWrapper } from '@/components/events/event-wizard/EventWizardWrapper';
import { logger } from '@/lib/logger';

export default async function CreateEventPage() {
  // We'll handle the ID in the client component instead
  // Get the Supabase client
  const supabase = await createClient();

  // Get the authenticated user - middleware already ensures we have a valid user
  // so we don&apos;t need to redirect here
  const { data: { user: authUser } } = await supabase.auth.getUser();

  // Log authentication state for debugging
  logger.info('[DEBUG] CreateEventPage - Auth check:', {
    hasUser: !!authUser,
    userId: authUser?.id,
    timestamp: new Date().toISOString()
  });

  // Get the internal user ID from the users table if we have an auth user
  // But don&apos;t redirect if there&apos;s an issue - let the client component handle it
  let userId = null;
  if (authUser) {
    const { data: userData, error: userIdError } = await supabase
      .from('users')
      .select('id')
      .eq('auth_user_id', authUser.id)
      .single();

    if (userIdError || !userData) {
      logger.error("[DEBUG] Error fetching user ID in CreateEventPage:", userIdError);
    } else {
      userId = userData.id;
    }
  }

  // Fetch event types to display in the wizard
  logger.info('[SERVER] CreateEventPage - Fetching event types');
  const eventTypesResponse = await getEventTypes();
  let eventTypes = eventTypesResponse.success ? eventTypesResponse.data || [] : [];

  logger.info('[SERVER] CreateEventPage - Event types fetched:', {
    success: eventTypesResponse.success,
    count: eventTypes.length,
    error: eventTypesResponse.error
  });

  // If no event types exist, seed the database with default types
  if (eventTypes.length === 0) {
    logger.info('[SERVER] CreateEventPage - No event types found, seeding database');
    const seedResult = await seedEventTypes();
    logger.info('[SERVER] CreateEventPage - Seed result:', {
      success: seedResult.success,
      error: seedResult.error
    });

    if (seedResult.success) {
      // Fetch event types again after seeding
      logger.info('[SERVER] CreateEventPage - Fetching event types after seeding');
      const refreshedTypesResponse = await getEventTypes();
      eventTypes = refreshedTypesResponse.success ? refreshedTypesResponse.data || [] : [];

      logger.info('[SERVER] CreateEventPage - Refreshed event types:', {
        success: refreshedTypesResponse.success,
        count: eventTypes.length,
        error: refreshedTypesResponse.error
      });
    }
  }

  // We'll handle loading the event in the client component
  const pageTitle = 'Create or Edit Event';

  logger.info('[SERVER] CreateEventPage - Rendering page with:', {
    eventTypesCount: eventTypes.length,
    pageTitle
  });

  return (
    <div className="container mx-auto px-4 py-8 mb-20">
      <h1 className="text-3xl font-bold mb-2">{pageTitle}</h1>
      <p className="text-[hsl(var(--muted-foreground))] mb-6">
        Follow the steps to create your event or continue editing your draft
      </p>

      {/* Add a key to force remounting if needed */}
      <EventWizardWrapper
        eventTypes={eventTypes}
        pageTitle={pageTitle}
        key={`event-wizard-${Date.now()}`}
      />
    </div>
  );
}