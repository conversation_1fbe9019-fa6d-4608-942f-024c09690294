import { NextResponse } from 'next/server';
import { logger } from '@/lib/logger';

// File upload handler for event images
export async function POST(request: Request) {
  try {
    // Initialize Supabase client
    const supabase = await createClient();

    // Check authentication with Supabase
    const { data: { session } } = await supabase.auth.getSession();

    let userId = 'anonymous';

    if (session?.user?.id) {
      userId = session.user.id;
      logger.info('Upload API: Authenticated user found', userId);
    } else {
      logger.warn('Upload API: No authenticated user found, proceeding with anonymous upload for debugging');
      // For production, you would uncomment the following lines:
      // return NextResponse.json({
      //   success: false,
      //   error: "Unauthorized",
      //   message: "You must be logged in to upload images"
      // }, { status: 401 });
    }

    // Parse the form data with file
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const eventId = formData.get('eventId') as string;
    const type = formData.get('type') as 'poster' | 'gallery' | 'tshirt-size-chart';

    // Validate input
    if (!file) {
      return new NextResponse("No file provided", { status: 400 });
    }

    // Restrict file types to images
    const fileType = file.type;
    if (!fileType.startsWith('image/')) {
      return new NextResponse("Only image files are allowed", { status: 400 });
    }

    // Restrict file size (5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return new NextResponse("File size exceeds 5MB limit", { status: 400 });
    }

    // Create a unique file path
    const fileExt = file.name.split('.').pop();
    const fileName = `${Date.now()}_${Math.random().toString(36).substring(2, 15)}.${fileExt}`;

    // Determine storage path based on type
    let storagePath;
    if (type === 'tshirt-size-chart') {
      storagePath = eventId
        ? `events/${eventId}/tshirt/${fileName}`
        : `events/drafts/${userId}/tshirt/${fileName}`;
    } else {
      storagePath = eventId
        ? `events/${eventId}/${type}/${fileName}`
        : `events/drafts/${userId}/${type}/${fileName}`;
    }

    // Get file bytes
    const arrayBuffer = await file.arrayBuffer();
    const bytes = new Uint8Array(arrayBuffer);

    // Upload to Supabase Storage
    logger.info(`API: Uploading file to 'images' bucket: ${storagePath}`);

    const { data, error: _error } = await supabase
      .storage
      .from('images')
      .upload(storagePath, bytes, {
        contentType: fileType,
        upsert: true // Changed to true to overwrite if file exists
      });

    if (_error) {
      logger.error("Error uploading file:", _error);
      return new NextResponse("Error uploading file", { status: 500 });
    }

    // Get public URL for the uploaded file
    logger.info('API: Getting public URL for:', storagePath);

    const { data: { publicUrl } } = supabase
      .storage
      .from('images')
      .getPublicUrl(storagePath);

    logger.info('API: Public URL retrieved:', publicUrl);

    // Return the file URL and path
    return NextResponse.json({
      success: true,
      url: publicUrl,
      path: storagePath
    });
  } catch (_error) {
    logger.error("Error handling file upload:", _error);

    // Return a more detailed error message
    let errorMessage = "Internal Server Error";
    let statusCode = 500;

    if (error instanceof Error) {
      errorMessage = error.message;

      // Check if it's a Supabase storage error
      if ('code' in error && typeof (error as unknown).code === 'string') {
        const code = (error as unknown).code;
        logger.error(`Supabase storage error code: ${code}`);

        // Map common Supabase storage error codes to appropriate messages
        if (code === '23505') {
          errorMessage = 'A file with this name already exists';
          statusCode = 409; // Conflict
        } else if (code === '42501') {
          errorMessage = 'Permission denied to upload file';
          statusCode = 403; // Forbidden
        } else if (code === '413') {
          errorMessage = 'File size exceeds the maximum allowed limit';
          statusCode = 413; // Payload Too Large
        }
      }
    }

    logger.error(`Returning error response: ${statusCode} - ${errorMessage}`);

    return NextResponse.json({
      success: false,
      error: errorMessage
    }, { status: statusCode });
  }
}