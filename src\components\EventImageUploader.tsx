'use client';

import { useState, useCallback } from 'react';
import { Upload, X, ImagePlus, Loader2 } from 'lucide-react';
import { logger } from '@/lib/logger';

interface EventImageUploaderProps {
  eventId?: string;
  onImageUploaded?: (imageType: 'poster' | 'gallery', imageData: { url: string; path: string }) => void;
  maxGalleryImages?: number;
  className?: string;
}

export default function EventImageUploader({
  eventId,
  onImageUploaded,
  maxGalleryImages = 10,
  className = '',
}: EventImageUploaderProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);

  const handleUpload = useCallback(async (
    files: FileList | null,
    type: 'poster' | 'gallery'
  ) => {
    if (!files || files.length === 0) return;

    setIsUploading(true);
    setUploadProgress(0);
    setError(null);

    try {
      if (!files || files.length === 0) {
        throw new Error('No file selected');
      }

      const file = files[0];
      if (!file) {
        throw new Error('No file selected');
      }

      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', type);

      if (eventId) {
        formData.append('eventId', eventId);
      }

      // Upload the file
      const uploadResponse = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!uploadResponse.ok) {
        const errorData = await uploadResponse.json();
        throw new Error(errorData.message || 'Failed to upload image');
      }

      const uploadData = await uploadResponse.json();

      // If we have an event ID, associate the image with the event
      if (eventId) {
        const associateResponse = await fetch('/api/events/images', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            eventId,
            url: uploadData.url,
            path: uploadData.path,
            type,
          }),
        });

        if (!associateResponse.ok) {
          const errorData = await associateResponse.json();
          throw new Error(errorData.message || 'Failed to associate image with event');
        }
      }

      // Notify parent component
      if (onImageUploaded) {
        onImageUploaded(type, {
          url: uploadData.url,
          path: uploadData.path,
        });
      }

      setUploadProgress(100);
    } catch (err) {
      logger.error('Upload error:', err);
      setError(err instanceof Error ? err.message : 'An error occurred during upload');
    } finally {
      setIsUploading(false);
    }
  }, [eventId, onImageUploaded]);

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          Event Poster
        </label>
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
          <input
            type="file"
            id="posterUpload"
            accept="image/*"
            onChange={(e) => handleUpload(e.target.files, 'poster')}
            className="hidden"
            disabled={isUploading}
          />
          <label
            htmlFor="posterUpload"
            className="flex flex-col items-center justify-center cursor-pointer"
          >
            {isUploading ? (
              <div className="flex flex-col items-center space-y-2">
                <Loader2 className="w-8 h-8 text-blue-500 animate-spin" />
                <span className="text-sm text-gray-500">Uploading... {uploadProgress}%</span>
              </div>
            ) : (
              <>
                <ImagePlus className="w-8 h-8 text-gray-400" />
                <span className="mt-2 text-sm text-gray-500">
                  Click to upload event poster
                </span>
                <span className="mt-1 text-xs text-gray-400">
                  PNG, JPG, GIF up to 5MB
                </span>
              </>
            )}
          </label>
        </div>
      </div>

      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          Event Gallery Images
        </label>
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
          <input
            type="file"
            id="galleryUpload"
            accept="image/*"
            multiple
            onChange={(e) => handleUpload(e.target.files, 'gallery')}
            className="hidden"
            disabled={isUploading}
          />
          <label
            htmlFor="galleryUpload"
            className="flex flex-col items-center justify-center cursor-pointer"
          >
            {isUploading ? (
              <div className="flex flex-col items-center space-y-2">
                <Loader2 className="w-8 h-8 text-blue-500 animate-spin" />
                <span className="text-sm text-gray-500">Uploading... {uploadProgress}%</span>
              </div>
            ) : (
              <>
                <Upload className="w-8 h-8 text-gray-400" />
                <span className="mt-2 text-sm text-gray-500">
                  Click to upload gallery images
                </span>
                <span className="mt-1 text-xs text-gray-400">
                  PNG, JPG, GIF up to 5MB (max {maxGalleryImages} images)
                </span>
              </>
            )}
          </label>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 text-red-600 p-3 rounded-md flex items-start">
          <X className="w-5 h-5 mr-2 flex-shrink-0 mt-0.5" />
          <span>{error}</span>
        </div>
      )}
    </div>
  );
}