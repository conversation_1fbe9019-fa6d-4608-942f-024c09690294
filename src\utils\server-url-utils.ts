import { logger } from '@/lib/logger';
/**
 * Utility functions for URL handling that can be used in server contexts
 * These functions are specifically designed to be used in middleware and server components
 */

/**
 * Check if a URL is an event registration URL
 * @param url The URL to check
 * @returns True if the URL is an event registration URL, false otherwise
 */
export function isEventRegistrationUrl(url: string): boolean {
  // Check if the URL contains '/events/' and '/register'
  return url.includes('/events/') && url.includes('/register');
}

/**
 * Get the event ID or slug from an event registration URL
 * @param url The URL to extract the event ID from
 * @returns The event ID/slug or null if not found
 */
export function getEventIdFromUrl(url: string): string | null {
  // Parse the URL to extract the event ID
  try {
    const urlObj = new URL(url.startsWith('http') ? url : `https://example.com${url}`);
    const pathParts = urlObj.pathname.split('/');

    // Find the index of 'events' in the path
    const eventsIndex = pathParts.findIndex(part => part === 'events');

    // If 'events' is found and there's a part after it, that's the event ID or slug
    if (eventsIndex !== -1 && eventsIndex + 1 < pathParts.length) {
      const eventIdOrSlug = pathParts[eventsIndex + 1];
      return eventIdOrSlug || null;
    }
  } catch (error) {
    logger.error('Error parsing event URL:', error);
  }

  return null;
}
