// Simple script to add city column to events table
const { Client } = require('pg');
require('dotenv').config({ path: '.env.local' });
require('dotenv').config();

async function addCityColumn() {
  // Get the database connection string
  const databaseUrl = process.env.DATABASE_URL || process.env.SUPABASE_DATABASE_URL;
  
  if (!databaseUrl) {
    console.error('Missing DATABASE_URL environment variable');
    process.exit(1);
  }
  
  // Create a new client
  const client = new Client({
    connectionString: databaseUrl,
  });
  
  try {
    // Connect to the database
    await client.connect();
    console.log('Connected to database');
    
    // Check if column exists
    const checkResult = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'events' AND column_name = 'city';
    `);
    
    if (checkResult.rows.length > 0) {
      console.log('City column already exists. No action needed.');
    } else {
      // Add the city column
      console.log('Adding city column to events table...');
      await client.query('ALTER TABLE events ADD COLUMN city TEXT;');
      console.log('City column added successfully!');
    }
    
    // Notify schema reload
    await client.query("SELECT pg_notify('pgrst', 'reload schema');");
    console.log('Schema cache reloaded');
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    // Close the client
    await client.end();
    console.log('Database connection closed');
  }
}

addCityColumn(); 