import { PostgrestError } from '@supabase/supabase-js';
import { logger } from '@/lib/logger';

export class DatabaseError extends Error {
  code: string;
  details: string | null;
  
  constructor(error: PostgrestError) {
    super(error.message);
    this.name = 'DatabaseError';
    this.code = error.code;
    this.details = error.details;
  }
}

export class AuthorizationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'AuthorizationError';
  }
}

export function handleDatabaseError(error: PostgrestError): never {
  logger.error('Database error:', _error);
  throw new DatabaseError(_error);
}

export function handleAuthorizationError(resource: string, action: string): never {
  const message = `Unauthorized: Cannot ${action} ${resource}`;
  logger.error(message);
  throw new AuthorizationError(message);
}

/**
 * Format error for client-side display
 */
export function formatErrorForClient(error: unknown): { message: string; code?: string } {
  if (error instanceof DatabaseError) {
    return {
      message: error.message,
      code: error.code,
    };
  }
  
  if (error instanceof AuthorizationError) {
    return {
      message: error.message,
      code: 'auth_error',
    };
  }
  
  if (error instanceof Error) {
    return {
      message: error.message,
    };
  }
  
  return {
    message: 'An unknown error occurred',
  };
}
