'use client'

import { logger } from '@/lib/logger';

import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/contexts/auth-context'
import { UserAvatarMenu } from './user-avatar-menu'
import { useEffect, useState } from 'react'

export function AuthButtons() {
  const router = useRouter()
  const { isSignedIn, user, loading, refreshSession } = useAuth()
  const [hasResetCookie, setHasResetCookie] = useState(false)
  const [isMounted, setIsMounted] = useState(false)
  const [loadingTimeout, setLoadingTimeout] = useState(false)

  // Set mounted state and check for reset cookie on client side
  useEffect(() => {
    setIsMounted(true)
    const resetCookieExists = document.cookie.includes('sb-reset-complete')
    setHasResetCookie(resetCookieExists)

    // Only refresh session on mount if we don&apos;t already have a user
    // This prevents unnecessary refreshes when the component remounts
    if (!user && !loading) {
      refreshSession()
    }

    // Set a timeout to show loading state for at least 300ms to prevent flickering
    const timer = setTimeout(() => {
      setLoadingTimeout(true)
    }, 300)

    return () => clearTimeout(timer)
  }, [refreshSession, user, loading])

  // Show loading state during initial load or when refreshing session
  if (loading || (!isMounted && !hasResetCookie)) {
    return <div className="h-9 w-9 rounded-full bg-gray-200 animate-pulse"></div>
  }

  // If we&apos;re authenticated and have a user object, show the avatar menu
  if (isSignedIn && user) {
    if (process.env.NODE_ENV === 'development') {
      logger.info('Rendering UserAvatarMenu with user:', user.id)
    }
    return <UserAvatarMenu user={user} loading={false} />
  }

  // If we&apos;re authenticated but don&apos;t have a user object yet, show loading state
  // but don&apos;t trigger another refresh to avoid loops
  if (isSignedIn && !user) {
    if (process.env.NODE_ENV === 'development') {
      logger.info('Authenticated but no user object, showing loading state')
    }
    // We don&apos;t call refreshSession() here to avoid refresh loops
    return <div className="h-9 w-9 rounded-full bg-gray-200 animate-pulse"></div>
  }

  // If we&apos;re not signed in and not loading, or if cookies have been reset,
  // show the sign-in and join buttons
  if (process.env.NODE_ENV === 'development') {
    logger.info('Rendering sign-in/join buttons')
  }
  return (
    <div className="flex items-center space-x-2">
      <Link href="/sign-in">
        <Button variant="outline" size="sm">
          Sign in
        </Button>
      </Link>
      <Link href="/sign-up">
        <Button size="sm">
          Join Now
        </Button>
      </Link>
    </div>
  )
}
