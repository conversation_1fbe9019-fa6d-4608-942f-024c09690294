import { createAdminClient } from '@/lib/supabase/admin-client';
import { logger } from '@/lib/logger';

export async function createDataExportTables() {
  try {
    // Get Supabase admin client
    const supabase = await createAdminClient();

    // Check if the table already exists
    try {
      const { data, error: queryError } = await supabase
        .from('data_exports')
        .select('id')
        .limit(1);

      if (!queryError) {
        // Table exists
        logger.info('Data exports table exists and is accessible');
        return { success: true, message: 'Data exports table exists' };
      } else {
        // Table doesn't exist or isn't accessible
        logger.info('Data exports table check failed:', queryError);
        return {
          success: false,
          error: 'Data exports table does not exist or is not accessible. Please run the migration to create it.'
        };
      }
    } catch (err) {
      logger.error('Error checking data exports table:', err);
      return {
        success: false,
        error: `Error checking data exports table: ${err instanceof Error ? err.message : 'Unknown error'}`
      };
    }
  } catch (error) {
    logger.error('Error checking data export tables:', error);
    return {
      success: false,
      error: `Error checking data export tables: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}