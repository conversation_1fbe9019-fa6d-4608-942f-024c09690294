'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Menu, X, LogOut } from 'lucide-react'
import { NavItem, renderNavIcon } from './nav-utils'
import { createClient } from '@/lib/supabase/client'
import { signOut } from '@/lib/supabase/auth'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { logger } from '@/lib/logger'

interface User {
  id: string
  first_name: string
  last_name: string
  avatar?: string
}

interface MobileNavProps {
  navItems: NavItem[]
}

export function MobileNav({ navItems }: MobileNavProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [user, setUser] = useState<User | null>(null)
  const [isSigningOut, setIsSigningOut] = useState(false)
  const supabase = createClient()

  useEffect(() => {
    // Get user data
    const getUserData = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession()

        if (session) {
          const { data: userData } = await supabase
            .from('users')
            .select('id, first_name, last_name, avatar')
            .eq('auth_user_id', session.user.id)
            .single()

          setUser(userData)
        }
      } catch (_error) {
        logger.error('Error fetching user data:', _error)
      }
    }

    getUserData()
  }, [supabase])

  const handleSignOut = async () => {
    try {
      setIsSigningOut(true)
      await signOut()
      // The signOut function will handle the redirect
    } catch (_error) {
      logger.error('Error signing out:', _error)
      // Redirect to home page even if there&apos;s an error
      window.location.href = '/'
    } finally {
      setIsSigningOut(false)
    }
  }

  return (
    <>
      {/* Mobile header with menu button */}
      <div className="md:hidden sticky top-0 z-10 flex items-center justify-between px-4 py-3 bg-white border-b">
        <button
          type="button"
          className="inline-flex items-center justify-center p-2 rounded-md text-gray-500 hover:text-gray-900 hover:bg-gray-100 focus:outline-hidden"
          onClick={() => setIsOpen(true)}
        >
          <span className="sr-only">Open menu</span>
          <Menu className="h-6 w-6" />
        </button>
        <div className="flex items-center space-x-3">
          <span className="text-sm font-medium">Fuiyoo</span>
          {user && (
            <Avatar className="h-8 w-8">
              <AvatarImage src={user.avatar} alt={`${user.first_name} ${user.last_name}`} />
              <AvatarFallback>
                {user.first_name?.[0]}{user.last_name?.[0] || ''}
              </AvatarFallback>
            </Avatar>
          )}
        </div>
      </div>

      {/* Mobile sidebar drawer */}
      {isOpen && (
        <div className="md:hidden fixed inset-0 z-40">
          <div
            className="fixed inset-0 bg-gray-600 bg-opacity-75"
            onClick={() => setIsOpen(false)}
          ></div>
          <div className="fixed inset-y-0 left-0 flex flex-col w-64 max-w-xs bg-white">
            <div className="flex items-center justify-between h-16 px-4 border-b">
              <h2 className="text-lg font-semibold text-primary">Fuiyoo Menu</h2>
              <button
                type="button"
                className="inline-flex items-center justify-center p-2 rounded-md text-gray-500 hover:text-gray-900 hover:bg-gray-100 focus:outline-hidden"
                onClick={() => setIsOpen(false)}
              >
                <span className="sr-only">Close menu</span>
                <X className="h-6 w-6" />
              </button>
            </div>
            <nav className="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className="flex items-center px-4 py-2 text-sm font-medium rounded-md text-gray-700 hover:bg-gray-100 hover:text-primary"
                  onClick={() => setIsOpen(false)}
                >
                  {renderNavIcon(item.iconName)}
                  {item.label}
                </Link>
              ))}

              {/* User profile */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                {user && (
                  <div className="flex items-center px-4">
                    <div className="flex-shrink-0">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={user.avatar} alt={`${user.first_name} ${user.last_name}`} />
                        <AvatarFallback>
                          {user.first_name?.[0]}{user.last_name?.[0] || ''}
                        </AvatarFallback>
                      </Avatar>
                    </div>
                    <div className="ml-3 flex-1">
                      <p className="text-sm font-medium text-gray-700">{user.first_name} {user.last_name}</p>
                      <Link href="/dashboard/profile" className="text-xs font-medium text-gray-500 hover:text-primary">
                        View profile
                      </Link>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={handleSignOut}
                      disabled={isSigningOut}
                      className="text-gray-500 hover:text-gray-700"
                    >
                      {isSigningOut ? (
                        <span className="h-5 w-5 animate-spin">⟳</span>
                      ) : (
                        <LogOut className="h-5 w-5" />
                      )}
                    </Button>
                  </div>
                )}
              </div>
            </nav>
          </div>
        </div>
      )}
    </>
  )
}
