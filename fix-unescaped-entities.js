const fs = require('fs');
const path = require('path');
const glob = require('glob');

console.log('🔧 Starting unescaped entities fix...');

// Find all TSX files
glob('src/**/*.tsx', (err, files) => {
  if (err) {
    console.error('Error finding files:', err);
    return;
  }

  let totalFiles = 0;
  let totalReplacements = 0;

  files.forEach(file => {
    try {
      let content = fs.readFileSync(file, 'utf8');
      let modified = false;
      let fileReplacements = 0;

      // Pattern to match unescaped apostrophes in JSX content
      // This regex looks for apostrophes that are not already escaped
      const patterns = [
        // Match apostrophes in text content (not in attributes)
        {
          regex: /(\s|>)([^<]*?)'/g,
          replacement: (match, prefix, text) => {
            // Don't replace if already escaped or in code
            if (text.includes('&apos;') || text.includes('&#39;')) {
              return match;
            }
            return prefix + text + '&apos;';
          }
        },
        // Match apostrophes at the beginning of text content
        {
          regex: /^([^<]*?)'/gm,
          replacement: (match, text) => {
            if (text.includes('&apos;') || text.includes('&#39;')) {
              return match;
            }
            return text + '&apos;';
          }
        }
      ];

      // Apply safer, more targeted replacements
      const specificReplacements = [
        // Common contractions
        { from: /(\s|>)don't(\s|<)/g, to: '$1don&apos;t$2' },
        { from: /(\s|>)can't(\s|<)/g, to: '$1can&apos;t$2' },
        { from: /(\s|>)won't(\s|<)/g, to: '$1won&apos;t$2' },
        { from: /(\s|>)we're(\s|<)/g, to: '$1we&apos;re$2' },
        { from: /(\s|>)you're(\s|<)/g, to: '$1you&apos;re$2' },
        { from: /(\s|>)they're(\s|<)/g, to: '$1they&apos;re$2' },
        { from: /(\s|>)it's(\s|<)/g, to: '$1it&apos;s$2' },
        { from: /(\s|>)that's(\s|<)/g, to: '$1that&apos;s$2' },
        { from: /(\s|>)what's(\s|<)/g, to: '$1what&apos;s$2' },
        { from: /(\s|>)here's(\s|<)/g, to: '$1here&apos;s$2' },
        { from: /(\s|>)there's(\s|<)/g, to: '$1there&apos;s$2' },
        { from: /(\s|>)let's(\s|<)/g, to: '$1let&apos;s$2' },
        { from: /(\s|>)I'm(\s|<)/g, to: '$1I&apos;m$2' },
        { from: /(\s|>)I'll(\s|<)/g, to: '$1I&apos;ll$2' },
        { from: /(\s|>)I've(\s|<)/g, to: '$1I&apos;ve$2' },
        { from: /(\s|>)I'd(\s|<)/g, to: '$1I&apos;d$2' },
        { from: /(\s|>)We're(\s|<)/g, to: '$1We&apos;re$2' },
        { from: /(\s|>)You're(\s|<)/g, to: '$1You&apos;re$2' },
        { from: /(\s|>)They're(\s|<)/g, to: '$1They&apos;re$2' },
        { from: /(\s|>)It's(\s|<)/g, to: '$1It&apos;s$2' },
        { from: /(\s|>)That's(\s|<)/g, to: '$1That&apos;s$2' },
        { from: /(\s|>)What's(\s|<)/g, to: '$1What&apos;s$2' },
        { from: /(\s|>)Here's(\s|<)/g, to: '$1Here&apos;s$2' },
        { from: /(\s|>)There's(\s|<)/g, to: '$1There&apos;s$2' },
        { from: /(\s|>)Let's(\s|<)/g, to: '$1Let&apos;s$2' },
        // Possessives
        { from: /(\s|>)users'(\s|<)/g, to: '$1users&apos;$2' },
        { from: /(\s|>)team's(\s|<)/g, to: '$1team&apos;s$2' },
        { from: /(\s|>)user's(\s|<)/g, to: '$1user&apos;s$2' },
        { from: /(\s|>)event's(\s|<)/g, to: '$1event&apos;s$2' },
        { from: /(\s|>)organization's(\s|<)/g, to: '$1organization&apos;s$2' }
      ];

      specificReplacements.forEach(({ from, to }) => {
        if (from.test(content)) {
          content = content.replace(from, to);
          modified = true;
          fileReplacements++;
        }
      });

      if (modified) {
        fs.writeFileSync(file, content);
        totalFiles++;
        totalReplacements += fileReplacements;
        console.log(`✅ Fixed ${fileReplacements} unescaped entities in: ${file}`);
      }
    } catch (error) {
      console.error(`❌ Error processing ${file}:`, error.message);
    }
  });

  console.log(`\n📊 Summary:`);
  console.log(`   Files processed: ${files.length}`);
  console.log(`   Files modified: ${totalFiles}`);
  console.log(`   Total replacements: ${totalReplacements}`);
  console.log('✨ Unescaped entities fix completed!\n');
});
