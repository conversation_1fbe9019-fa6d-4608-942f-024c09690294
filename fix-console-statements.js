const fs = require('fs');
const path = require('path');
const glob = require('glob');

console.log('🔧 Starting console statements fix...');

// Files to exclude from console statement fixes (scripts, migrations, etc.)
const excludePatterns = [
  'src/scripts/**/*',
  'src/migrations/**/*',
  'src/lib/logger.ts' // Don't modify the logger itself
];

const shouldExclude = (filePath) => {
  return excludePatterns.some(pattern => {
    const regex = new RegExp(pattern.replace(/\*\*/g, '.*').replace(/\*/g, '[^/]*'));
    return regex.test(filePath);
  });
};

const addLoggerImport = (content, filePath) => {
  // Check if logger is already imported
  if (content.includes("from '@/lib/logger'") || content.includes('from "../lib/logger"')) {
    return content;
  }

  // Check if we need to add logger import
  if (content.includes('logger.')) {
    // Find the last import statement
    const importRegex = /^import\s+.*?;$/gm;
    const imports = content.match(importRegex);
    
    if (imports && imports.length > 0) {
      const lastImport = imports[imports.length - 1];
      const lastImportIndex = content.lastIndexOf(lastImport);
      const insertIndex = lastImportIndex + lastImport.length;
      
      const loggerImport = "\nimport { logger } from '@/lib/logger';";
      content = content.slice(0, insertIndex) + loggerImport + content.slice(insertIndex);
    } else {
      // No imports found, add at the beginning
      content = "import { logger } from '@/lib/logger';\n" + content;
    }
  }
  
  return content;
};

// Find all TypeScript and TSX files
glob('src/**/*.{ts,tsx}', (err, files) => {
  if (err) {
    console.error('Error finding files:', err);
    return;
  }

  let totalFiles = 0;
  let totalReplacements = 0;

  files.forEach(file => {
    // Skip excluded files
    if (shouldExclude(file)) {
      return;
    }

    try {
      let content = fs.readFileSync(file, 'utf8');
      let modified = false;
      let fileReplacements = 0;

      // Console statement replacements
      const replacements = [
        { from: /console\.log\(/g, to: 'logger.info(' },
        { from: /console\.error\(/g, to: 'logger.error(' },
        { from: /console\.warn\(/g, to: 'logger.warn(' },
        { from: /console\.debug\(/g, to: 'logger.debug(' },
        { from: /console\.info\(/g, to: 'logger.info(' }
      ];

      replacements.forEach(({ from, to }) => {
        const matches = content.match(from);
        if (matches) {
          content = content.replace(from, to);
          modified = true;
          fileReplacements += matches.length;
        }
      });

      if (modified) {
        // Add logger import if needed
        content = addLoggerImport(content, file);
        
        fs.writeFileSync(file, content);
        totalFiles++;
        totalReplacements += fileReplacements;
        console.log(`✅ Fixed ${fileReplacements} console statements in: ${file}`);
      }
    } catch (error) {
      console.error(`❌ Error processing ${file}:`, error.message);
    }
  });

  console.log(`\n📊 Summary:`);
  console.log(`   Files processed: ${files.length}`);
  console.log(`   Files modified: ${totalFiles}`);
  console.log(`   Total replacements: ${totalReplacements}`);
  console.log('✨ Console statements fix completed!\n');
});
