import { isAdmin } from "../../../lib/auth";
import { UserManagement } from "./components/UserManagement";
import { UserRole } from "../../../types/roles";
import { adminSupabase } from '@/lib/supabase/admin-client';
import { logger } from '@/lib/logger';

// Define a serializable user type
interface SerializableUser {
  id: string;
  firstName: string | null;
  lastName: string | null;
  emailAddress: string | null;
  role: string;
  createdAt: number;
}

export default async function UsersPage() {
  const supabase = await createClient();
  const { data: { session } } = await supabase.auth.getSession();

  if (!session) {
    redirect('/sign-in');
  }

  const isUserAdmin = await isAdmin();

  if (!isUserAdmin) {
    redirect('/dashboard');
  }

  // Get first 50 users to display in the admin panel
  const adminClient = await adminSupabase();
  const { data: users, error: _error } = await adminClient
    .from('users')
    .select('id, first_name, last_name, email, role, created_at, avatar_url')
    .order('created_at', { ascending: false })
    .limit(50);

  if (_error) {
    logger.error("Error fetching users:", _error);
    throw new Error("Failed to fetch users");
  }

  // Serialize the users to avoid passing non-serializable data to client components
  const serializedUsers: SerializableUser[] = users?.map(user => {
    // Use type assertion to handle potential type errors
    const typedUser = user as unknown;
    return {
      id: typedUser.id ?? '',
      firstName: typedUser.first_name ?? null,
      lastName: typedUser.last_name ?? null,
      emailAddress: typedUser.email ?? null,
      role: typedUser.role || UserRole.USER,
      createdAt: typedUser.created_at ? new Date(typedUser.created_at).getTime() : Date.now(),
    };
  }) ?? [];

  return (
    <div className="container mx-auto px-4">
      <h1 className="text-2xl font-semibold mb-6">User Management</h1>
      <UserManagement initialUsers={serializedUsers} />
    </div>
  );
}