/**
 * Turbopack configuration file
 * @see https://turbo.build/pack/docs/reference/configuration
 */
module.exports = {
  // Define optimization level (1-3, higher = more optimization but longer build time)
  optimizationLevel: 3,

  // Persistent cache options
  cache: {
    // Size limit for the cache (in MB)
    maxSize: 5000,
    // Duration to keep items in cache (in days)
    ttl: 30,
  },

  // Resolve options
  resolve: {
    // Don't follow symlinks (improves performance)
    followSymlinks: false,
    // Prefer modern ES modules when available
    preferEsm: true,
  },

  // Advanced performance options
  performance: {
    // Use incremental typechecking when possible
    incrementalTypecheck: true,
    // Skip module evaluation for known pure modules
    fastRefresh: true,
  },

  // Memory usage controls
  memoryLimit: {
    // Heap size limit (in MB)
    heapSize: 8192,
    // Enable automatic garbage collection
    aggressiveGc: true,
  },

  // Development server options
  devServer: {
    // Hot module replacement options
    hmr: {
      // Minimize full page refreshes
      optimizeForSpeed: true,
    },
    hot: true,
  },

  // Resolvers configuration for imports
  resolveAlias: {
    '@': 'src',
  },

  // Additional environment variables needed in development
  env: {
    NEXT_PUBLIC_DEV_ENV: "true",
    NEXT_PUBLIC_DEV_URL: "http://localhost:3000",
    NEXT_PUBLIC_DEV_API_URL: "http://localhost:3000/api"
  }
};