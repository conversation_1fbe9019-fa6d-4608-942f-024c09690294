#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to fix Supabase schema cache issues for saved_contacts table
 * 
 * This script applies the necessary SQL to fix schema cache issues with
 * the saved_contacts table in Supabase. It alters column types to force
 * a schema cache refresh.
 * 
 * Usage:
 *   node scripts/fix-schema-cache.js
 * 
 * Requirements:
 *   - SUPABASE_URL and SUPABASE_SERVICE_KEY environment variables must be set
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Verify environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_KEY environment variables must be set.');
  console.log('These can be found in your Supabase project settings under "API".');
  console.log('For SUPABASE_SERVICE_KEY, use the "service_role" key (with full database access).');
  process.exit(1);
}

// Create Supabase client with admin privileges
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

async function fixSchemaCache() {
  console.log('Starting schema cache fix for saved_contacts table...');
  
  try {
    // Step 1: Alter emergency contact fields
    console.log('Altering emergency_contact_name column...');
    const { error: error1 } = await supabase.rpc('pg_catalog.pg_execute', {
      query: 'ALTER TABLE saved_contacts ALTER COLUMN emergency_contact_name TYPE TEXT;'
    });
    
    if (error1) throw new Error(`Error altering emergency_contact_name: ${error1.message}`);
    
    console.log('Altering emergency_contact_no column...');
    const { error: error2 } = await supabase.rpc('pg_catalog.pg_execute', {
      query: 'ALTER TABLE saved_contacts ALTER COLUMN emergency_contact_no TYPE TEXT;'
    });
    
    if (error2) throw new Error(`Error altering emergency_contact_no: ${error2.message}`);
    
    console.log('Altering emergency_contact_relationship column...');
    const { error: error3 } = await supabase.rpc('pg_catalog.pg_execute', {
      query: 'ALTER TABLE saved_contacts ALTER COLUMN emergency_contact_relationship TYPE TEXT;'
    });
    
    if (error3) throw new Error(`Error altering emergency_contact_relationship: ${error3.message}`);
    
    // Step 2: Alter basic fields as well to ensure complete refresh
    console.log('Altering first_name column...');
    const { error: error4 } = await supabase.rpc('pg_catalog.pg_execute', {
      query: 'ALTER TABLE saved_contacts ALTER COLUMN first_name TYPE TEXT;'
    });
    
    if (error4) throw new Error(`Error altering first_name: ${error4.message}`);
    
    console.log('Altering last_name column...');
    const { error: error5 } = await supabase.rpc('pg_catalog.pg_execute', {
      query: 'ALTER TABLE saved_contacts ALTER COLUMN last_name TYPE TEXT;'
    });
    
    if (error5) throw new Error(`Error altering last_name: ${error5.message}`);
    
    // Step 3: Force schema cache reload
    console.log('Forcing schema cache reload...');
    const { error: error6 } = await supabase.rpc('pg_catalog.pg_execute', {
      query: "SELECT pg_notify('pgrst', 'reload schema');"
    });
    
    if (error6) {
      console.warn(`Warning: Could not force schema reload: ${error6.message}`);
      console.warn('You may need to restart the PostgREST service manually.');
    } else {
      console.log('Schema reload notification sent!');
    }
    
    // Step 4: Test the schema cache
    console.log('Testing schema cache update...');
    const { data, error: testError } = await supabase
      .from('saved_contacts')
      .select('emergency_contact_name')
      .limit(1);
      
    if (testError) {
      console.warn(`Warning: Schema test failed: ${testError.message}`);
      console.warn('The schema cache may not be fully updated yet.');
      console.warn('You may need to restart the PostgREST service from the Supabase dashboard.');
    } else {
      console.log('Schema test successful! The fix has been applied.');
    }
    
    console.log('\nSchema cache fix completed!');
    console.log('Please try your application again. If issues persist:');
    console.log('1. Restart the PostgREST service from your Supabase dashboard');
    console.log('2. Contact Supabase support if the issue continues');
    
  } catch (error) {
    console.error('Error fixing schema cache:', error.message);
    process.exit(1);
  }
}

// Run the fix
fixSchemaCache(); 