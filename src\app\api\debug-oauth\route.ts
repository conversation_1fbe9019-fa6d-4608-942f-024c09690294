import { logger } from '@/lib/logger';
import { NextRequest, NextResponse } from 'next/server'

export const dynamic = 'force-dynamic'

/**
 * Debug endpoint to check OAuth configuration
 * This is useful for troubleshooting authentication issues
 */
export async function GET(request: NextRequest) {
  try {
    // Get the origin for the current request
    const origin = request.headers.get('origin') || request.nextUrl.origin

    // Get the host
    const host = new URL(origin).host

    // Check if we're running locally
    const isLocalhost = host.includes('localhost') || host.includes('127.0.0.1')

    // Construct the callback URL that should be configured in Google Console
    const callbackUrl = `${origin}/auth/callback`

    // Return information about the OAuth configuration
    return NextResponse.json({
      environment: process.env.NODE_ENV,
      origin,
      host,
      isLocalhost,
      recommendedCallbackUrl: callbackUrl,
      googleConsoleSetup: {
        authorizedJavascriptOrigins: [origin],
        authorizedRedirectUris: [callbackUrl],
        notes: [
          "Make sure these URLs are configured in your Google Cloud Console",
          `For localhost testing, you need to add ${process.env.NEXT_PUBLIC_DEV_URL || 'http://localhost:3000'} as an authorized origin`,
          `For localhost testing, you need to add ${process.env.NEXT_PUBLIC_DEV_URL || 'http://localhost:3000'}/auth/callback as a redirect URI`,
          "If using a custom domain, add that as well"
        ]
      },
      supabaseSetup: {
        notes: [
          "In Supabase Dashboard, go to Authentication > Providers > Google",
          "Make sure the Google provider is enabled",
          "Verify that the Client ID and Client Secret match your Google Cloud Console project",
          "The Redirect URL should be set to your Supabase project URL + /auth/v1/callback"
        ]
      }
    })
  } catch (err) {
    logger.error('Error in debug-oauth route:', err)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
