"use client";

import { useState } from "react";
import Link from "next/link";

export default function ApplyForOrganizerButton() {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  return (
    <>
      <button
        onClick={openModal}
        className="px-4 py-2 bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))] font-medium rounded-md hover:bg-[hsl(var(--primary-hover))] transition"
      >
        Apply to Become an Event Organizer
      </button>

      {/* Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black/50 dark:bg-black/70 flex items-center justify-center z-50">
          <div className="bg-[hsl(var(--background))] text-[hsl(var(--foreground))] rounded-lg shadow-xl max-w-lg w-full p-6 border border-[hsl(var(--border))]">
            <h3 className="text-xl font-bold mb-4">Become an Event Organizer</h3>
            <p className="mb-4">
              Thank you for your interest in becoming an event organizer! This process will require you to complete a multi-step application form.
            </p>

            <h4 className="font-semibold text-lg mb-2">You&apos;ll need to provide:</h4>
            <ul className="list-disc ml-5 mb-4">
              <li>Organization information</li>
              <li>Contact details</li>
              <li>Previous event experience</li>
              <li>ID verification documents</li>
              <li>Organization documents (if applicable)</li>
            </ul>

            <p className="mb-6">
              The application process takes approximately 15-20 minutes to complete. You can save your progress and return to finish it later.
            </p>

            <div className="flex justify-end space-x-3">
              <button
                onClick={closeModal}
                className="px-4 py-2 border border-[hsl(var(--border))] rounded-md hover:bg-[hsl(var(--muted))] dark:hover:bg-[hsl(var(--dark-muted))] transition"
              >
                Cancel
              </button>
              <Link
                href="/dashboard/organizations/apply"
                className="px-4 py-2 bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))] rounded-md hover:bg-[hsl(var(--primary-hover))] transition"
              >
                Start Application
              </Link>
            </div>
          </div>
        </div>
      )}
    </>
  );
}