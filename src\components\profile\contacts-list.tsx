'use client';

import React from 'react';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, Plus, Pencil, Trash2, ChevronRight } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useForm, Controller } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { getSavedContacts, createContact, updateContact, deleteContact } from '@/app/actions/contacts';
import { ContactSchema, type ContactFormData } from '@/lib/schemas/contacts';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import Select from 'react-select';
import { getCountries, getStates, type Country, type State } from '@/data/countries';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select as UISelect,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from '@/components/ui/use-toast';
import { logger } from '@/lib/logger';

type Contact = {
  id: string;
  first_name: string;
  last_name: string | null;
  relationship: string;
  email: string | null;
  phone: string | null;
  date_of_birth: string | null;
  gender: string | null;
  tshirt_size: string | null;
  address: string | null;
  city: string | null;
  state: string | null;
  country: string | null;
  postcode: string | null;
  emergency_contact_name: string | null;
  emergency_contact_no: string | null;
  emergency_contact_relationship: string | null;
  created_at: string;
  updated_at: string;
};

interface SelectOption {
  value: string;
  label: string;
  icon?: string;
}

interface ControlledSelectProps {
  name: string;
  control: any;
  options: SelectOption[];
  placeholder: string;
  isDisabled?: boolean;
  isLoading?: boolean;
  onChange?: (value: string | undefined) => void;
}

const ControlledSelect = ({
  name,
  control,
  options,
  placeholder,
  isDisabled,
  isLoading,
  onChange
}: ControlledSelectProps) => (
  <Controller
    name={name}
    control={control}
    render={({ field }) => (
      <Select
        {...field}
        className="react-select"
        classNamePrefix="react-select"
        options={options}
        placeholder={placeholder}
        isClearable
        isDisabled={isDisabled}
        isLoading={isLoading}
        value={options.find(x => x.value === field.value) || null}
        onChange={(option) => {
          field.onChange(option?.value);
          onChange?.(option?.value);
        }}
        formatOptionLabel={(option) => (
          <div className="flex items-center">
            {option.icon && <span className="mr-2">{option.icon}</span>}
            <span>{option.label}</span>
          </div>
        )}
      />
    )}
  />
);

export function ContactsList() {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [confirmDelete, setConfirmDelete] = useState<string | null>(null);
  const [countryOptions, setCountryOptions] = useState<SelectOption[]>([]);
  const [stateOptions, setStateOptions] = useState<SelectOption[]>([]);
  const [isLoadingStates, setIsLoadingStates] = useState(false);

  const form = useForm<z.infer<typeof ContactSchema>>({
    resolver: zodResolver(ContactSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      relationship: '',
      email: '',
      phone: '',
      dateOfBirth: '',
      gender: '',
      tShirtSize: '',
      address: '',
      city: '',
      state: '',
      country: '',
      postcode: '',
      emergencyContactName: '',
      emergencyContactNo: '',
      emergencyContactRelationship: '',
    },
  });

  // Fetch contacts on component mount
  useEffect(() => {
    const fetchContacts = async () => {
      logger.info("ContactsList: Starting to fetch contacts");
      setLoading(true);
      try {
        logger.info("ContactsList: Calling getSavedContacts server action");
        const result = await getSavedContacts();
        logger.info("ContactsList: Server action returned:", result);

        const { contacts, error } = result;

        if (error) {
          logger.error("ContactsList: Error from server action:", error);
          toast({
            title: "Error fetching contacts",
            description: error,
            variant: "destructive",
          });
          return;
        }

        logger.info("ContactsList: Contacts retrieved:", contacts?.length || 0);
        setContacts(contacts || []);
      } catch (error) {
        logger.error("ContactsList: Error in fetchContacts:", error);
        toast({
          title: "Error",
          description: "Failed to load your contacts. Please try again.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchContacts();
  }, []);

  // Fetch countries on mount
  useEffect(() => {
    const fetchCountryOptions = async () => {
      try {
        const countries = await getCountries();
        const options = countries.map((country: Country) => ({
          value: country.code,
          label: country.name,
          icon: country.flag
        }));
        setCountryOptions(options);
      } catch (error) {
        logger.error('Error fetching country options:', error);
        toast({
          title: "Error",
          description: "Failed to load country options. Please try again.",
          variant: "destructive",
        });
      }
    };

    fetchCountryOptions();
  }, []);

  const handleCountryChange = async (countryCode: string | undefined) => {
    if (!countryCode) {
      setStateOptions([]);
      return;
    }

    setIsLoadingStates(true);
    try {
      const states = await getStates(countryCode);
      const options = states.map((state: State) => ({
        value: state.code,
        label: state.name
      }));
      setStateOptions(options);
    } catch (error) {
      logger.error('Error fetching states:', error);
      toast({
        title: "Error",
        description: "Failed to load states. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoadingStates(false);
    }
  };

  // Filter contacts based on search term
  const filteredContacts = contacts.filter(contact => {
    const fullName = `${contact.first_name} ${contact.last_name || ''}`.toLowerCase();
    return fullName.includes(searchTerm.toLowerCase()) ||
      (contact.email && contact.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (contact.relationship && contact.relationship.toLowerCase().includes(searchTerm.toLowerCase()));
  });

  // Open dialog for creating/editing a contact
  const openContactDialog = (contact: Contact | null = null) => {
    setSelectedContact(contact);

    if (contact) {
      form.reset({
        firstName: contact.first_name,
        lastName: contact.last_name || '',
        relationship: contact.relationship,
        email: contact.email || '',
        phone: contact.phone || '',
        dateOfBirth: contact.date_of_birth || '',
        gender: contact.gender || '',
        tShirtSize: contact.tshirt_size || '',
        address: contact.address || '',
        city: contact.city || '',
        state: contact.state || '',
        country: contact.country || '',
        postcode: contact.postcode || '',
        emergencyContactName: contact.emergency_contact_name || '',
        emergencyContactNo: contact.emergency_contact_no || '',
        emergencyContactRelationship: contact.emergency_contact_relationship || '',
      });
    } else {
      form.reset({
        firstName: '',
        lastName: '',
        relationship: '',
        email: '',
        phone: '',
        dateOfBirth: '',
        gender: '',
        tShirtSize: '',
        address: '',
        city: '',
        state: '',
        country: '',
        postcode: '',
        emergencyContactName: '',
        emergencyContactNo: '',
        emergencyContactRelationship: '',
      });
    }

    setDialogOpen(true);
  };

  // Handle form submission
  const onSubmit = async (data: z.infer<typeof ContactSchema>) => {
    logger.info("Form submission data:", data);

    try {
      // Get form validation state
      const isValid = await form.trigger();
      logger.info("Form validation state:", { isValid, errors: form.formState.errors });

      if (!isValid) {
        logger.error("Form validation failed:", form.formState.errors);
        return;
      }

      if (selectedContact) {
        // Update existing contact
        logger.info("Updating existing contact:", selectedContact.id);
        const { contact, error } = await updateContact(selectedContact.id, data);
        logger.info("Update contact response:", { contact, error });
        if (error) {
          logger.error("Error updating contact:", error);
          toast({
            title: "Error updating contact",
            description: error,
            variant: "destructive",
          });
          return;
        }

        // Update local state
        setContacts(prev => prev.map(c => c.id === selectedContact.id ? (contact || c) : c));

        toast({
          title: "Contact updated",
          description: "Contact has been updated successfully",
        });
      } else {
        // Create new contact
        logger.info("Creating new contact");
        const { contact, error } = await createContact(data);
        logger.info("Create contact response:", { contact, error });
        if (error) {
          logger.error("Error creating contact:", error);
          toast({
            title: "Error creating contact",
            description: error,
            variant: "destructive",
          });
          return;
        }

        // Add to local state
        setContacts(prev => contact ? [...prev, contact] : prev);

        toast({
          title: "Contact added",
          description: "New contact has been added successfully",
        });
      }

      // Close dialog and reset form
      setDialogOpen(false);
      form.reset();
    } catch (error) {
      logger.error("Error submitting contact form:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle contact deletion
  const handleDeleteContact = async (id: string) => {
    try {
      const { success, error } = await deleteContact(id);

      if (error) {
        toast({
          title: "Error deleting contact",
          description: error,
          variant: "destructive",
        });
        return;
      }

      // Remove from local state
      setContacts(prev => prev.filter(c => c.id !== id));

      toast({
        title: "Contact deleted",
        description: "Contact has been removed successfully",
      });

      // Close confirmation dialog
      setConfirmDelete(null);
    } catch (error) {
      logger.error("Error deleting contact:", error);
      toast({
        title: "Error",
        description: "Failed to delete contact. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Search and add controls */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between">
        <div className="relative w-full sm:w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search contacts..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Button onClick={() => openContactDialog()}>
          <Plus className="mr-2 h-4 w-4" />
          Add Contact
        </Button>
      </div>

      {/* Contacts list */}
      {loading ? (
        <div className="space-y-4">
          {[1, 2, 3].map((item) => (
            <Card key={item} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-5 bg-gray-200 rounded w-1/3 mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredContacts.length === 0 ? (
        <div className="text-center py-12 border rounded-lg">
          <div className="text-4xl mb-4">👥</div>
          <h3 className="text-lg font-medium mb-2">No contacts found</h3>
          <p className="text-muted-foreground mb-6">
            {searchTerm ? "No contacts match your search" : "You haven't added any contacts yet"}
          </p>
          {searchTerm ? (
            <Button variant="outline" onClick={() => setSearchTerm('')}>
              Clear search
            </Button>
          ) : (
            <Button onClick={() => openContactDialog()}>
              <Plus className="mr-2 h-4 w-4" />
              Add your first contact
            </Button>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          {filteredContacts.map((contact) => (
            <Card key={contact.id} className="hover:bg-accent/5 transition-colors">
              <CardContent className="p-6 flex justify-between items-center">
                <div>
                  <h3 className="font-medium text-base">
                    {contact.first_name} {contact.last_name}
                  </h3>
                  <p className="text-muted-foreground text-sm mt-1">
                    {contact.relationship}
                    {contact.email && ` • ${contact.email}`}
                  </p>
                  {contact.phone && (
                    <p className="text-sm mt-1">{contact.phone}</p>
                  )}
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => openContactDialog(contact)}
                  >
                    <Pencil className="h-4 w-4" />
                    <span className="sr-only">Edit</span>
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setConfirmDelete(contact.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                    <span className="sr-only">Delete</span>
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => openContactDialog(contact)}
                  >
                    <ChevronRight className="h-4 w-4" />
                    <span className="sr-only">Details</span>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Contact Form Dialog */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="sm:max-w-[640px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {selectedContact ? "Edit Contact" : "Add New Contact"}
            </DialogTitle>
            <DialogDescription>
              {selectedContact
                ? "Update contact information for faster event registration"
                : "Add contact information for faster event registration"}
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <Tabs defaultValue="basic">
                <TabsList className="grid grid-cols-3 mb-4">
                  <TabsTrigger value="basic">Basic Info</TabsTrigger>
                  <TabsTrigger value="address">Address</TabsTrigger>
                  <TabsTrigger value="emergency">Emergency Contact</TabsTrigger>
                </TabsList>

                <TabsContent value="basic" className="space-y-4">
                  <div className="grid gap-4 grid-cols-1 sm:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="firstName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>First Name</FormLabel>
                          <FormControl>
                            <Input placeholder="First name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="lastName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Last Name</FormLabel>
                          <FormControl>
                            <Input placeholder="Last name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="relationship"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Relationship</FormLabel>
                        <UISelect
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select relationship" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="Family">Family</SelectItem>
                            <SelectItem value="Friend">Friend</SelectItem>
                            <SelectItem value="Spouse">Spouse</SelectItem>
                            <SelectItem value="Colleague">Colleague</SelectItem>
                            <SelectItem value="Other">Other</SelectItem>
                          </SelectContent>
                        </UISelect>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid gap-4 grid-cols-1 sm:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email</FormLabel>
                          <FormControl>
                            <Input type="email" placeholder="Email address" {...field} value={field.value || ""} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Phone</FormLabel>
                          <FormControl>
                            <Input placeholder="Phone number" {...field} value={field.value || ""} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid gap-4 grid-cols-1 sm:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="dateOfBirth"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Date of Birth</FormLabel>
                          <FormControl>
                            <Input type="date" {...field} value={field.value || ""} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="gender"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Gender</FormLabel>
                          <FormControl>
                            <RadioGroup
                              onValueChange={field.onChange}
                              defaultValue={field.value || ""}
                              className="flex space-x-4"
                            >
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="male" id="male" />
                                <Label htmlFor="male">Male</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="female" id="female" />
                                <Label htmlFor="female">Female</Label>
                              </div>
                            </RadioGroup>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="tShirtSize"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>T-Shirt Size</FormLabel>
                        <UISelect
                          onValueChange={field.onChange}
                          defaultValue={field.value || ""}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select size" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="XS">XS</SelectItem>
                            <SelectItem value="S">S</SelectItem>
                            <SelectItem value="M">M</SelectItem>
                            <SelectItem value="L">L</SelectItem>
                            <SelectItem value="XL">XL</SelectItem>
                            <SelectItem value="XXL">XXL</SelectItem>
                            <SelectItem value="XXXL">XXXL</SelectItem>
                          </SelectContent>
                        </UISelect>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>

                <TabsContent value="address" className="space-y-4">
                  <FormField
                    control={form.control}
                    name="address"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Street Address</FormLabel>
                        <FormControl>
                          <Input placeholder="Street address" {...field} value={field.value || ""} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid gap-4 grid-cols-1 sm:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="city"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>City</FormLabel>
                          <FormControl>
                            <Input placeholder="City" {...field} value={field.value || ""} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="state"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>State/Province</FormLabel>
                          <FormControl>
                            <ControlledSelect
                              name="state"
                              control={form.control}
                              options={stateOptions}
                              placeholder="Select state/province..."
                              isDisabled={!form.watch('country') || isLoadingStates}
                              isLoading={isLoadingStates}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid gap-4 grid-cols-1 sm:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="country"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Country</FormLabel>
                          <FormControl>
                            <ControlledSelect
                              name="country"
                              control={form.control}
                              options={countryOptions}
                              placeholder="Select country..."
                              onChange={handleCountryChange}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="postcode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Postal/ZIP Code</FormLabel>
                          <FormControl>
                            <Input placeholder="Postal/ZIP code" {...field} value={field.value || ""} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </TabsContent>

                <TabsContent value="emergency" className="space-y-4">
                  <FormField
                    control={form.control}
                    name="emergencyContactName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Emergency Contact Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Full name" {...field} value={field.value || ""} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="emergencyContactNo"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Emergency Contact Number</FormLabel>
                        <FormControl>
                          <Input placeholder="Phone number" {...field} value={field.value || ""} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="emergencyContactRelationship"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Relationship to Emergency Contact</FormLabel>
                        <UISelect
                          onValueChange={field.onChange}
                          defaultValue={field.value || ""}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select relationship" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="Parent">Parent</SelectItem>
                            <SelectItem value="Spouse">Spouse</SelectItem>
                            <SelectItem value="Sibling">Sibling</SelectItem>
                            <SelectItem value="Child">Child</SelectItem>
                            <SelectItem value="Friend">Friend</SelectItem>
                            <SelectItem value="Other">Other</SelectItem>
                          </SelectContent>
                        </UISelect>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>
              </Tabs>

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">
                  {selectedContact ? "Update Contact" : "Add Contact"}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={!!confirmDelete} onOpenChange={(open) => !open && setConfirmDelete(null)}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this contact? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="gap-2 sm:gap-0">
            <Button
              type="button"
              variant="outline"
              onClick={() => setConfirmDelete(null)}
            >
              Cancel
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={() => confirmDelete && handleDeleteContact(confirmDelete)}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
