import { logger } from '@/lib/logger';
import { createClient } from '@/lib/supabase/pages-client'
import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'

// Force dynamic rendering to ensure the route is not cached
export const dynamic = 'force-dynamic'

/**
 * <PERSON>le sign-out on the server side
 * This ensures cookies are properly cleared
 *
 * @param request The Next.js request object
 * @returns A JSON response indicating success or failure
 */
export async function GET(request: NextRequest) {
  return handleSignOut(request);
}

/**
 * Support POST method for programmatic access
 *
 * @param request The Next.js request object
 * @returns A JSON response indicating success or failure
 */
export async function POST(request: NextRequest) {
  return handleSignOut(request);
}

/**
 * Shared handler for both GET and POST requests
 *
 * @param request The Next.js request object
 * @returns A JSON response indicating success or failure
 */
async function handleSignOut(request: NextRequest) {
  try {
    const supabase = await createClient()

    // Sign out on the server side
    const { error: _error } = await supabase.auth.signOut()

    if (_error) {
      logger.error('Error signing out:', _error)
      return NextResponse.json(
        { success: false, error: error.message },
        {
          status: 500,
          headers: {
            'Cache-Control': 'no-store, max-age=0',
            'Content-Type': 'application/json'
          }
        }
      )
    }

    // Clear all cookies related to authentication
    const cookieStore = await cookies()
    const authCookies = [
      // Supabase cookies
      'sb-access-token',
      'sb-refresh-token',
      'supabase-auth-token',
      'sb:token',
      'sb-eibzxudhnojsdxksgowo-auth-token',
      'sb-eibzxudhnojsdxksgowo-auth-token.0',
      'sb-eibzxudhnojsdxksgowo-auth-token.1',
      'sb-eibzxudhnojsdxksgowo-auth-token-code-verifier',
      'sb-provider-token',
      'sb-auth-token',
      '__supabase_session',

      // Google OAuth cookies
      'g_state',
      'g_csrf_token',
      'g_auth_token',
      'g_user',

      // Any other potential auth cookies
      'auth_token',
      'auth_session',
      'auth_refresh_token'
    ]

    for (const cookieName of authCookies) {
      try {
        await cookieStore.delete(cookieName)
      } catch (e) {
        logger.warn(`Failed to delete cookie ${cookieName}:`, _e)
      }
    }

    // Create a response object
    const response = NextResponse.json(
      { success: true },
      {
        status: 200,
        headers: {
          'Cache-Control': 'no-store, max-age=0',
          'Content-Type': 'application/json',
          'X-Content-Type-Options': 'nosniff',
          'Referrer-Policy': 'strict-origin-when-cross-origin'
        }
      }
    )

    // Set a special cookie to indicate that the session has been cleared
    // This will be used by the AuthButtons component to show the sign-in buttons
    // instead of the loading state
    response.cookies.set({
      name: 'sb-reset-complete',
      value: 'true',
      maxAge: 60, // Only keep this cookie for 60 seconds
      path: '/',
    })

    return response
  } catch (_error) {
    logger.error('Unexpected error during sign-out:', _error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error during sign-out'
      },
      {
        status: 500,
        headers: {
          'Cache-Control': 'no-store, max-age=0',
          'Content-Type': 'application/json'
        }
      }
    )
  }
}
