import { logger } from '@/lib/logger';
/**
 * Date utility functions for consistent date handling across the application
 */

/**
 * Converts any date input (string, Date object, or null/undefined) to an ISO string
 * @param date Date value to convert
 * @returns ISO string or null if date is null/undefined/invalid
 */
export function toISOString(date: Date | string | null | undefined): string | null {
  if (!date) return null;
  
  try {
    if (typeof date === 'string') {
      // Check if the string is already in a valid ISO format
      const parsed = new Date(date);
      if (isNaN(parsed.getTime())) return null;
      return parsed.toISOString();
    } else if (date instanceof Date) {
      if (isNaN(date.getTime())) return null;
      return date.toISOString();
    }
    return null;
  } catch (_error) {
    logger.error('Error converting date to ISO string:', _error);
    return null;
  }
}

/**
 * Converts any date input to a Date object
 * @param date Date value to convert
 * @returns Date object or null if date is null/undefined/invalid
 */
export function toDate(date: Date | string | null | undefined): Date | null {
  if (!date) return null;
  
  try {
    if (typeof date === 'string') {
      const parsed = new Date(date);
      if (isNaN(parsed.getTime())) return null;
      return parsed;
    } else if (date instanceof Date) {
      if (isNaN(date.getTime())) return null;
      return date;
    }
    return null;
  } catch (_error) {
    logger.error('Error converting to Date object:', _error);
    return null;
  }
}

/**
 * Converts any date input to a Date object or undefined (never null)
 * Useful for optional date fields that don't accept null
 * @param date Date value to convert
 * @returns Date object or undefined if date is null/undefined/invalid
 */
export function toOptionalDate(date: Date | string | null | undefined): Date | undefined {
  if (!date) return undefined;
  
  try {
    if (typeof date === 'string') {
      const parsed = new Date(date);
      if (isNaN(parsed.getTime())) return undefined;
      return parsed;
    } else if (date instanceof Date) {
      if (isNaN(date.getTime())) return undefined;
      return date;
    }
    return undefined;
  } catch (_error) {
    logger.error('Error converting to Date object:', _error);
    return undefined;
  }
}

/**
 * Format a date for display in the UI
 * @param date Date value to format
 * @param options Intl.DateTimeFormatOptions for formatting
 * @returns Formatted date string or empty string if date is invalid
 */
export function formatDate(
  date: Date | string | null | undefined,
  options: Intl.DateTimeFormatOptions = { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }
): string {
  const dateObj = toDate(date);
  if (!dateObj) return '';
  
  try {
    return new Intl.DateTimeFormat('en-US', options).format(dateObj);
  } catch (_error) {
    logger.error('Error formatting date:', _error);
    return '';
  }
}

/**
 * Converts a Date object or ISO string to a local datetime-local input value
 * @param date Date to convert
 * @returns String in format YYYY-MM-DDThh:mm suitable for datetime-local inputs
 */
export function toInputDateTime(date: Date | string | null | undefined): string {
  const dateObj = toDate(date);
  if (!dateObj) return '';
  
  try {
    // Format as YYYY-MM-DDThh:mm
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');
    const hours = String(dateObj.getHours()).padStart(2, '0');
    const minutes = String(dateObj.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  } catch (_error) {
    logger.error('Error converting date to input format:', _error);
    return '';
  }
}

/**
 * Check if a date is valid
 * @param date Date to check
 * @returns true if date is valid, false otherwise
 */
export function isValidDate(date: Date | string | null | undefined): boolean {
  if (!date) return false;
  
  try {
    if (typeof date === 'string') {
      const parsed = new Date(date);
      return !isNaN(parsed.getTime());
    } else if (date instanceof Date) {
      return !isNaN(date.getTime());
    }
    return false;
  } catch (_error) {
    return false;
  }
} 