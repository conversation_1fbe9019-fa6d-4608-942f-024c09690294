import { NextResponse } from "next/server";
import { logger } from '@/lib/logger';

export async function GET(request: Request) {
  try {
    // Extract the authorization token from the request headers
    const authHeader = request.headers.get('Authorization');
    let token = '';

    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
    }

    // Create Supabase client
    const supabase = await createClient();

    // If we have a token, set it explicitly
    if (token) {
      // Set the auth token for this request
      supabase.auth.setSession({
        access_token: token,
        refresh_token: '',
      });
    }

    // Get the current user
    const { data: { user: authUser }, error: authError } = await supabase.auth.getUser();

    if (authError || !authUser) {
      logger.error("Authentication error:", authError);
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Get the internal user ID from the users table
    const { data: userData, error: userIdError } = await supabase
      .from('users')
      .select('id')
      .eq('auth_user_id', authUser.id)
      .single();

    if (userIdError || !userData) {
      logger.error("Error fetching user ID:", userIdError);
      return new NextResponse("User not found", { status: 404 });
    }

    const userId = userData.id;

    // Get user information
    const { data: user, error: _error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (_error) {
      logger.error("Error fetching user profile:", _error);
      return new NextResponse("Error fetching user profile", { status: 500 });
    }

    if (!user) {
      return new NextResponse("User not found", { status: 404 });
    }

    // Parse eventCategories if it exists and is a string
    let categories = [];
    if (user.eventCategories) {
      try {
        categories = typeof user.eventCategories === 'string'
          ? JSON.parse(user.eventCategories)
          : user.eventCategories;
      } catch (_error) {
        logger.error('Error parsing eventCategories:', _error);
        categories = [];
      }
    }

    // Return the user data with properly structured stats
    const response = {
      ...user,
      stats: {
        eventsAttended: 0, // TODO: Add actual count
        eventsHosted: 0, // TODO: Add actual count
        categories
      },
      tshirtSize: user.tshirt_size || (user as unknown).tshirtSize // Try both column formats
    };

    logger.info("GET /api/user/profile returning:", {
      id: response.id,
      username: response.username,
      tshirtSize: response.tshirtSize
    });

    return NextResponse.json(response);
  } catch (_error) {
    logger.error("Error in profile API:", _error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}

export async function PUT(request: Request) {
  try {
    // Extract the authorization token from the request headers
    const authHeader = request.headers.get('Authorization');
    let token = '';

    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
    }

    // Create Supabase client
    const supabase = await createClient();

    // If we have a token, set it explicitly
    if (token) {
      // Set the auth token for this request
      supabase.auth.setSession({
        access_token: token,
        refresh_token: '',
      });
    }

    // Get the current session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      logger.error("Session error:", sessionError);
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // Get the internal user ID from the users table
    const { data: userData, error: userIdError } = await supabase
      .from('users')
      .select('id')
      .eq('auth_user_id', session.user.id)
      .single();

    if (userIdError || !userData) {
      logger.error("Error fetching user ID:", userIdError);
      return new NextResponse("User not found", { status: 404 });
    }

    const userId = userData.id;

    const body = await request.json();

    // Log received fields to help with debugging
    logger.info("Profile update received:", {
      username: body.username,
      tshirtSize: body.tshirtSize
    });

    // Validate required fields
    if (!body.first_name) {
      return new NextResponse("First name is required", { status: 400 });
    }

    // Validate gender
    if (body.gender && !['male', 'female'].includes(body.gender)) {
      return new NextResponse("Gender must be either 'male' or 'female'", { status: 400 });
    }

    // Process categories if provided
    if (body.categories && Array.isArray(body.categories)) {
      // Validate categories exist
      const { data: validCategories } = await supabase
        .from('event_categories')
        .select('name')
        .in('name', body.categories);

      if (!validCategories || validCategories.length !== body.categories.length) {
        return new NextResponse("Invalid categories provided", { status: 400 });
      }

      // Store categories as JSONB array
      body.eventCategories = body.categories;
      delete body.categories;
    }

    // Update the user profile
    logger.info("Updating profile with data:", {
      username: body.username,
      tshirtSize: body.tshirtSize
    });

    const { error: _error } = await supabase
      .from('users')
      .update({
        first_name: body.first_name,
        last_name: body.last_name,
        username: body.username,
        gender: body.gender,
        bio: body.bio,
        avatar: body.avatar,
        nationality: body.nationality,
        ic: body.ic,
        passport: body.passport,
        dateOfBirth: body.dateOfBirth,
        contactNo: body.contactNo,
        address: body.address,
        apartment: body.apartment,
        city: body.city,
        postcode: body.postcode,
        country: body.country,
        state: body.state,
        interests: body.interests,
        isPublic: body.isPublic !== undefined ? body.isPublic : 1,
        emergencyContactName: body.emergencyContactName,
        emergencyContactNo: body.emergencyContactNo,
        emergencyContactRelationship: body.emergencyContactRelationship,
        eventCategories: body.eventCategories,
        tshirt_size: body.tshirtSize,
        tshirtSize: body.tshirtSize, // Update both column formats for compatibility
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (_error) {
      logger.error("Error updating user profile:", _error);
      return new NextResponse(JSON.stringify({
        message: "Error updating user profile",
        error
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get the updated user data to return in the response
    const { data: updatedUser, error: fetchError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (fetchError) {
      logger.error("Error fetching updated user data:", fetchError);
    } else {
      logger.info("Profile updated successfully, current values:", {
        username: updatedUser.username,
        tshirtSize: updatedUser.tshirt_size
      });
    }

    return NextResponse.json({
      message: "Profile updated successfully",
      user: updatedUser || undefined
    });
  } catch (_error) {
    logger.error("Error in profile update API:", _error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}