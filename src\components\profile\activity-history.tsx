'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from '@/components/ui/use-toast';
import { getUserActivity } from '@/app/actions/activity';
import { format, formatDistance } from 'date-fns';
import { Activity, Clock, Search, Eye, ArrowRight } from 'lucide-react';
import { Input } from '@/components/ui/input';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

type ActivityItem = {
  id: string;
  user_id: string;
  activity_type: string;
  activity_description: string;
  previous_value?: Record<string, unknown> | null;
  new_value?: Record<string, unknown> | null;
  field_name?: string | null;
  ip_address?: string | null;
  user_agent?: string | null;
  created_at: string;
};

export function ActivityHistory() {
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [selectedItem, setSelectedItem] = useState<ActivityItem | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);

  const itemsPerPage = 10;

  useEffect(() => {
    fetchActivityData();
  }, [page]);

  const fetchActivityData = async () => {
    setLoading(true);
    try {
      const { activity, total, error } = await getUserActivity(itemsPerPage, (page - 1) * itemsPerPage);

      if (error) {
        throw new Error(error);
      }

      setActivities(prev => page === 1 ? (activity || []) : [...prev, ...(activity || [])]);
      setTotalCount(total || 0);
      setHasMore(total ? (page * itemsPerPage) < total : false);
    } catch (error) {
      console.error("Error fetching activity data:", error);
      toast({
        title: "Error",
        description: "Failed to load activity history. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleLoadMore = () => {
    setPage(prev => prev + 1);
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setPage(1);
  };

  const handleViewDetails = (item: ActivityItem) => {
    setSelectedItem(item);
    setDetailsOpen(true);
  };

  const filteredActivities = activities.filter(item => {
    const searchLower = searchTerm.toLowerCase();
    return (
      item.activity_type.toLowerCase().includes(searchLower) ||
      item.activity_description.toLowerCase().includes(searchLower) ||
      (item.field_name && item.field_name.toLowerCase().includes(searchLower))
    );
  });

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, 'MMM d, yyyy h:mm a');
    } catch (e) {
      return dateString;
    }
  };

  const formatTimeAgo = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return formatDistance(date, new Date(), { addSuffix: true });
    } catch (e) {
      return dateString;
    }
  };

  const getActivityTypeIcon = (type: string) => {
    switch (type) {
      case 'profile_update':
        return <Activity className="h-4 w-4 text-blue-500" />;
      case 'profile_view':
        return <Eye className="h-4 w-4 text-green-500" />;
      case 'login':
        return <ArrowRight className="h-4 w-4 text-purple-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'profile_update':
        return 'bg-blue-100 text-blue-800';
      case 'profile_view':
        return 'bg-green-100 text-green-800';
      case 'login':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const renderDetailsDialog = () => {
    if (!selectedItem) return null;

    return (
      <Dialog open={detailsOpen} onOpenChange={setDetailsOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              Activity Details: {selectedItem.activity_type}
            </DialogTitle>
            <DialogDescription>
              {selectedItem.activity_description}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 my-4">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Date:</span>
              <span>
                {formatDate(selectedItem.created_at)}
              </span>
            </div>

            {selectedItem.field_name && (
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Field Changed:</span>
                <span>{selectedItem.field_name}</span>
              </div>
            )}

            {selectedItem.previous_value && (
              <div className="mt-4">
                <h4 className="text-sm font-medium mb-2">Previous Value:</h4>
                <div className="bg-muted p-3 rounded-md overflow-x-auto">
                  <pre className="text-xs whitespace-pre-wrap break-all">
                    {JSON.stringify(selectedItem.previous_value, null, 2)}
                  </pre>
                </div>
              </div>
            )}

            {selectedItem.new_value && (
              <div className="mt-4">
                <h4 className="text-sm font-medium mb-2">New Value:</h4>
                <div className="bg-muted p-3 rounded-md overflow-x-auto">
                  <pre className="text-xs whitespace-pre-wrap break-all">
                    {JSON.stringify(selectedItem.new_value, null, 2)}
                  </pre>
                </div>
              </div>
            )}

            <div className="border-t pt-4 mt-4">
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>IP Address:</span>
                <span>{selectedItem.ip_address || 'Not available'}</span>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between gap-4 mb-6">
        <h2 className="text-xl font-semibold">Profile Activity</h2>

        <div className="relative w-full sm:w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search activities..."
            className="pl-8"
            value={searchTerm}
            onChange={handleSearch}
          />
        </div>
      </div>

      {loading && page === 1 ? (
        <div className="space-y-4">
          {[1, 2, 3, 4].map(i => (
            <div key={i} className="flex gap-4 p-4 border rounded-lg animate-pulse">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div className="space-y-2 flex-1">
                <Skeleton className="h-4 w-1/3" />
                <Skeleton className="h-3 w-full" />
              </div>
            </div>
          ))}
        </div>
      ) : filteredActivities.length === 0 ? (
        <div className="text-center py-12 border rounded-lg">
          <Activity className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium mb-2">No activities found</h3>
          <p className="text-muted-foreground mb-6">
            {searchTerm
              ? "No activities match your search criteria."
              : "Your profile activity history will appear here."
            }
          </p>
          {searchTerm && (
            <Button variant="outline" onClick={() => setSearchTerm('')}>
              Clear search
            </Button>
          )}
        </div>
      ) : (
        <>
          <div className="space-y-4">
            {filteredActivities.map((activity) => (
              <div
                key={activity.id}
                className="border rounded-lg p-4 hover:bg-accent/5 transition-colors cursor-pointer"
                onClick={() => handleViewDetails(activity)}
              >
                <div className="flex items-start gap-4">
                  <div className="mt-1">
                    {getActivityTypeIcon(activity.activity_type)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge className={getActivityColor(activity.activity_type)} variant="outline">
                        {activity.activity_type}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {formatTimeAgo(activity.created_at)}
                      </span>
                    </div>
                    <p className="text-sm">{activity.activity_description}</p>
                    {activity.field_name && (
                      <p className="text-xs text-muted-foreground mt-1">
                        Field: {activity.field_name}
                      </p>
                    )}
                  </div>

                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <Eye className="h-4 w-4" />
                          <span className="sr-only">View Details</span>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>View Details</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
            ))}
          </div>

          {hasMore && (
            <div className="flex justify-center mt-6">
              <Button
                variant="outline"
                onClick={handleLoadMore}
                disabled={loading}
              >
                {loading ? "Loading..." : "Load More"}
              </Button>
            </div>
          )}
        </>
      )}

      {renderDetailsDialog()}
    </div>
  );
}
