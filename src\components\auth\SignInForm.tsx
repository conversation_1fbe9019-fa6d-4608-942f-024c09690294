'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import Link from 'next/link'
import GoogleSignInButton from './GoogleSignInButton'
import { isEventRegistrationUrl } from '@/lib/supabase/auth'
import { getBaseUrl } from '@/utils/url-utilities'

export default function SignInForm() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const router = useRouter()
  const searchParams = useSearchParams()
  let redirectUrl = searchParams?.get('redirect_url') || '/dashboard'
  const noRedirect = searchParams?.get('no_redirect') === 'true'

  // Prevent redirect loops with auth callback or if no_redirect is set
  if (redirectUrl.includes('/auth/callback') || redirectUrl.includes('no_redirect=true') || noRedirect) {
    console.warn('Detected potential auth redirect loop in SignInForm, redirecting to dashboard with no_redirect=true')
    redirectUrl = '/dashboard?no_redirect=true'
  }

  // Also prevent redirects to sign-in page (which would cause a loop)
  if (redirectUrl.includes('/sign-in')) {
    console.warn('Detected redirect to sign-in page, redirecting to dashboard instead')
    redirectUrl = '/dashboard?no_redirect=true'
  }

  // Make sure the redirect URL is properly formatted
  if (redirectUrl && !redirectUrl.startsWith('/') && !redirectUrl.startsWith('http')) {
    redirectUrl = `/${redirectUrl}`;
  }

  // Log if this is an event registration URL
  useEffect(() => {
    if (isEventRegistrationUrl(redirectUrl)) {
      console.log('Sign-in initiated from event registration page:', redirectUrl)
    }
  }, [redirectUrl])

  // Check for parameters in URL
  const errorFromUrl = searchParams?.get('error')
  const errorDescription = searchParams?.get('error_description')
  const resetAuth = searchParams?.get('reset_auth')

  // Handle reset_auth parameter and errors
  useEffect(() => {
    // Check if we need to reset the auth state
    if (resetAuth === 'true') {
      console.log('Resetting auth state due to reset_auth parameter');

      // Clear auth state both client-side and server-side
      try {
        // Clear local storage items related to Supabase
        Object.keys(localStorage).forEach(key => {
          if (key.startsWith('sb-') || key.includes('supabase')) {
            localStorage.removeItem(key);
          }
        });

        // Clear session storage items related to Supabase
        Object.keys(sessionStorage).forEach(key => {
          if (key.startsWith('sb-') || key.includes('supabase')) {
            sessionStorage.removeItem(key);
          }
        });

        // Also call the server-side API to clear cookies
        fetch('/api/auth/reset-state', {
          method: 'POST',
          credentials: 'include', // Important for cookies
        })
          .then(response => response.json())
          .then(data => {
            console.log('Server-side auth state reset:', data);
          })
          .catch(err => {
            console.error('Error calling reset-state API:', err);
          });

        console.log('Auth state reset complete');
      } catch (e) {
        console.error('Error clearing storage:', e);
      }

      // Set a user-friendly error message
      setError('Previous authentication session expired. Please sign in again.');
    }
    // Handle error messages from URL
    else if (errorFromUrl) {
      let errorMessage = decodeURIComponent(errorFromUrl);

      // If we have an error description, add it to the error message
      if (errorDescription) {
        const decodedDescription = decodeURIComponent(errorDescription);
        errorMessage = `${errorMessage}: ${decodedDescription}`;
      }

      // Special handling for common OAuth errors
      if (errorFromUrl === 'invalid_request' && errorDescription?.includes('bad_oauth_state')) {
        errorMessage = 'Authentication session expired or invalid. Please try signing in again.';
      }

      setError(errorMessage);

      // Log the error for debugging
      console.error('Auth error from URL:', errorMessage);
    }
  }, [errorFromUrl, errorDescription, resetAuth])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      const supabase = createClient()

      // Sign in with email and password
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        throw error
      }

      // Check if we have a session before redirecting
      if (data.session) {
        console.log('Sign-in successful, redirecting to:', redirectUrl)

        // Ensure we're not redirecting to a page that would cause a loop
        let finalRedirectUrl = redirectUrl;

        // If we're redirecting to sign-in or auth callback, force dashboard
        if (finalRedirectUrl.includes('/sign-in') ||
          finalRedirectUrl.includes('/auth/callback')) {
          console.warn('Preventing redirect loop after sign-in, redirecting to dashboard')
          finalRedirectUrl = '/dashboard'
        }

        // Make sure the redirect URL has no_redirect=true to prevent middleware redirect loops
        const urlObj = new URL(
          finalRedirectUrl.startsWith('http')
            ? finalRedirectUrl
            : `${window.location.origin}${finalRedirectUrl.startsWith('/') ? finalRedirectUrl : `/${finalRedirectUrl}`}`
        );

        // Add no_redirect=true to prevent redirect loops
        urlObj.searchParams.set('no_redirect', 'true');

        // Add a timestamp to prevent caching issues
        urlObj.searchParams.set('_ts', Date.now().toString());

        const finalUrl = urlObj.toString();
        console.log('Final redirect URL after sign-in:', finalUrl);

        // Force a small delay to ensure the session is properly set
        setTimeout(() => {
          // Use window.location for a full page refresh to ensure auth state is updated
          window.location.href = finalUrl;
        }, 500)
      } else {
        // If no session, something went wrong
        setError('Authentication successful but no session was created')
        setLoading(false)
      }
    } catch (err: unknown) {
      console.error('Sign-in error:', err)
      setError(err.message || 'An error occurred during sign-in')
      setLoading(false)
    }
  }

  return (
    <div className="w-full max-w-md mx-auto space-y-6 text-foreground">
      <div className="text-center">
        <h1 className="text-2xl font-bold">Sign In</h1>
        <p className="text-muted-foreground mt-2">Welcome back! Sign in to your account</p>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="email" className="text-foreground">Email</Label>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            disabled={loading}
          />
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="password" className="text-foreground">Password</Label>
            <Link href="/reset-password" className="text-sm text-primary hover:underline">
              Forgot password?
            </Link>
          </div>
          <Input
            id="password"
            type="password"
            placeholder="••••••••"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            disabled={loading}
          />
        </div>

        <Button
          type="submit"
          className="w-full"
          disabled={loading}
        >
          {loading ? 'Signing in...' : 'Sign In with Email'}
        </Button>
      </form>

      <div className="relative my-6">
        <div className="absolute inset-0 flex items-center">
          <Separator className="w-full" />
        </div>
        <div className="relative flex justify-center">
          <span className="bg-card px-2 text-sm text-muted-foreground">or</span>
        </div>
      </div>

      <GoogleSignInButton redirectTo={redirectUrl} />

      <div className="text-center text-sm mt-6 text-foreground">
        Don't have an account?{' '}
        <Link
          href={`/sign-up${redirectUrl !== '/dashboard' ? `?redirect_url=${encodeURIComponent(redirectUrl)}` : ''}`}
          className="text-primary hover:underline"
        >
          Sign up
        </Link>
      </div>
    </div>
  )
}
