'use server';

import { revalidatePath } from 'next/cache';
import { createId } from '@paralleldrive/cuid2';
import { createClient } from '@/lib/supabase/pages-client';
import { createAdminClient } from '@/lib/supabase/admin-client';
import { createDataExport, DataExport, EXPORT_STATUS } from '@/lib/schemas/data-export-db';
import { type ExportFormat, type ExportType, type DataExportRequest, type DataExportResponse } from '@/lib/schemas/data-export';
import { createDataExportTables } from '@/app/migrations/data-export-tables';
import { cookies } from 'next/headers';

/**
 * Server action to request data export
 */
export async function requestDataExport(
  request: DataExportRequest
): Promise<DataExportResponse> {
  try {
    // Initialize regular Supabase client with cookies for authentication
    const supabaseAuth = await createClient();

    // Get the authenticated user
    const { data: { user } } = await supabaseAuth.auth.getUser();

    if (!user?.id) {
      return {
        success: false,
        error: 'You must be logged in to request a data export',
      };
    }

    const userId = user.id;

    // Verify that the provided email matches the user's registered email
    if (!user.email) {
      return {
        success: false,
        error: 'Unable to verify your account email. Please try again later.',
      };
    }

    // Email verification should already be done on the client side,
    // but we double-check here for security
    if (request.email.trim().toLowerCase() !== user.email.trim().toLowerCase()) {
      return {
        success: false,
        error: 'The provided email does not match your registered email address.',
      };
    }

    // Initialize admin client for database operations
    const supabase = await createAdminClient();

    // Check if the data_exports table exists
    try {
      const tableCheck = await createDataExportTables();

      if (!tableCheck.success) {
        console.error('Data exports table check failed:', tableCheck.error);
        return {
          success: false,
          error: 'The data export system is not properly configured. Please contact support.',
        };
      }

      console.log('Data exports table check passed');
    } catch (error) {
      console.error('Error checking data export tables:', error);
      return {
        success: false,
        error: 'Failed to verify data export system configuration. Please try again later.',
      };
    }

    // Create an export request in the database
    const exportId = createId();
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + 7); // Expires after 7 days

    try {
      // Supabase client already initialized above

      // Create the data export object
      const exportData = createDataExport({
        userId,
        exportFormat: request.format,
        exportType: request.type,
        email: request.email,
        expiresAt: expiryDate
      });

      // Insert into Supabase - use explicit fields to avoid type issues
      const insertData = {
        id: exportData.id,
        user_id: exportData.user_id,
        status: exportData.status,
        export_format: exportData.export_format,
        export_type: exportData.export_type,
        email: exportData.email,
        requested_at: new Date().toISOString(),
        expires_at: exportData.expires_at || null,
        download_count: 0,
        is_deleted: false
      };

      console.log('Inserting data export:', insertData);

      try {
        const { error: insertError } = await supabase
          .from('data_exports')
          .insert(insertData);

        if (insertError) {
          console.error('Insert error details:', insertError);

          // If the table doesn't exist yet, this is likely the first run
          if (insertError.message && insertError.message.includes('does not exist')) {
            return {
              success: false,
              error: 'The export system is not yet configured. Please try again later or contact support.',
            };
          }

          return {
            success: false,
            error: `Failed to save export request: ${insertError.message || 'Unknown error'}`,
          };
        }
      } catch (error) {
        console.error('Exception during insert:', error);
        return {
          success: false,
          error: `Failed to save export request: ${error instanceof Error ? error.message : 'Unknown error'}`,
        };
      }

      // For demo purposes, we'll immediately update the status to processing
      try {
        // Simulate processing delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Update the status to processing
        const updateData = {
          status: EXPORT_STATUS.PROCESSING,
          processed_at: new Date().toISOString()
        };

        console.log('Updating export status:', updateData);

        try {
          const { error: updateError } = await supabase
            .from('data_exports')
            .update(updateData)
            .eq('id', exportId);

          if (updateError) {
            console.error('Error updating export status:', updateError);
          }
        } catch (error) {
          console.error('Exception during update:', error);
        }
      } catch (error) {
        console.error('Error in processing delay:', error);
      }

      // Success case
      console.log(`Data export requested by user ${userId}: ${request.type} in ${request.format} format to ${request.email}`);
      revalidatePath('/dashboard/profile');

      return {
        success: true,
        exportId: exportId,
        message: `Your ${request.type} data export in ${request.format.toUpperCase()} format has been requested and will be sent to ${request.email}`,
      };
    } catch (error) {
      console.error('Error saving export request:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to save export request. Please try again later.',
      };
    }
  } catch (error) {
    console.error('Error processing data export request:', error);
    return {
      success: false,
      error: 'Failed to process data export request. Please try again later.',
    };
  }
}

/**
 * Get the user's data export requests
 */
export async function getUserDataExports(): Promise<{
  exports?: unknown[]; // Using any to avoid type issues with Supabase rows
  error?: string;
}> {
  try {
    // Initialize regular Supabase client with cookies for authentication
    const supabaseAuth = await createClient();

    // Get the authenticated user
    const { data: { user } } = await supabaseAuth.auth.getUser();

    if (!user?.id) {
      return { error: 'You must be logged in to view your export history' };
    }

    const userId = user.id;

    // Initialize admin client for database operations
    const supabase = await createAdminClient();

    // Check if the data_exports table exists
    try {
      const tableCheck = await createDataExportTables();

      if (!tableCheck.success) {
        console.log('Data exports table check failed:', tableCheck.error);
        // Return empty array instead of error to avoid confusing the user
        return { exports: [] };
      }

      console.log('Data exports table check passed');
    } catch (error) {
      console.log('Error checking data export tables:', error);
      // Return empty array instead of error to avoid confusing the user
      return { exports: [] };
    }

    // Supabase client already initialized above

    // Get user's exports, ordered by most recent first
    try {
      console.log('Fetching export history for user:', userId);

      const { data, error } = await supabase
        .from('data_exports')
        .select('*')
        .eq('user_id', userId)
        .eq('is_deleted', false)
        .order('requested_at', { ascending: false });

      if (error) {
        console.error('Error fetching export history:', error);

        // If table doesn't exist, return empty array
        if (error.message && error.message.includes('does not exist')) {
          console.log('Data exports table does not exist yet');
          return { exports: [] };
        }

        return {
          error: `Failed to fetch export history: ${error.message}`,
          exports: []
        };
      }

      console.log(`Found ${data?.length || 0} export records`);
      return { exports: data || [] };
    } catch (error) {
      console.error('Exception fetching export history:', error);
      return {
        error: `Exception fetching export history: ${error instanceof Error ? error.message : 'Unknown error'}`,
        exports: []
      };
    }
  } catch (error) {
    console.error('Error fetching data exports:', error);
    return { error: error instanceof Error ? error.message : 'Failed to fetch your export history' };
  }
}