import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import React, { useState } from 'react';
import { EventCalendar } from '@/components/events/event-calendar';
import { PageHeader } from '@/components/page-header';
import { Container } from '@/components/ui/container';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useEvents } from '@/hooks/use-events';
import { CalendarIcon, Filter, List } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Event } from '@/repositories/event-repository';

'use client';

export function EventCalendarPage() {
  const router = useRouter();
  const { events, loading } = useEvents();
  const [viewMode, setViewMode] = useState<'calendar' | 'list'>('calendar');
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);

  const handleEventClick = (event: Event) => {
    setSelectedEvent(event);
    // Alternatively, navigate to the event
    // router.push(`/events/${event.id}`);
  };

  return (
    <>
      <PageHeader
        title="Event Calendar"
        description="View and manage your events"
        actions={
          <Button
            onClick={() => router.push('/events/new')}
          >
            Create Event
          </Button>
        }
      />

      <Container className="mt-8">
        <div className="flex justify-between items-center mb-6">
          <Tabs
            defaultValue="calendar"
            value={viewMode}
            onValueChange={(v) => setViewMode(v as 'calendar' | 'list')}
            className="w-full"
          >
            <div className="flex justify-between items-center">
              <TabsList>
                <TabsTrigger value="calendar">
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  Calendar View
                </TabsTrigger>
                <TabsTrigger value="list">
                  <List className="mr-2 h-4 w-4" />
                  List View
                </TabsTrigger>
              </TabsList>

              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                Filter Events
              </Button>
            </div>

            <TabsContent value="calendar" className="mt-6">
              <EventCalendar
                events={events}
                isLoading={loading}
                onEventClick={handleEventClick}
              />
            </TabsContent>

            <TabsContent value="list" className="mt-6">
              <Card>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {loading ? (
                      <div className="flex items-center justify-center h-96">
                        <div className="animate-pulse">Loading events...</div>
                      </div>
                    ) : events.length === 0 ? (
                      <div className="text-center py-12">
                        <CalendarIcon className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                        <h3 className="text-lg font-medium">No events found</h3>
                        <p className="text-muted-foreground mt-2">
                          Create your first event to get started
                        </p>
                        <Button
                          className="mt-4"
                          onClick={() => router.push('/events/new')}
                        >
                          Create Event
                        </Button>
                      </div>
                    ) : (
                      events.map((event) => (
                        <div
                          key={event.id}
                          className="border rounded-lg p-4 hover:bg-accent cursor-pointer"
                          onClick={() => router.push(`/events/${event.id}`)}
                        >
                          <h3 className="font-medium">{event.title}</h3>
                          <div className="flex justify-between text-sm text-muted-foreground mt-2">
                            <span>
                              {event.startDate ? (
                                typeof event.startDate === 'string'
                                  ? new Date(event.startDate).toLocaleDateString()
                                  : event.startDate instanceof Date
                                    ? event.startDate.toLocaleDateString()
                                    : 'Date not available'
                              ) : 'Date not available'}
                            </span>
                            <span>{event.location}</span>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </Container>
    </>
  );
}