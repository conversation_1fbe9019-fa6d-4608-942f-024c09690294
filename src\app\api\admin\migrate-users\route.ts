import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/pages-client'

export const dynamic = 'force-dynamic'

/**
 * Admin endpoint to migrate users from the old structure to the new one
 * This will update the auth_user_id field in the public.users table
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()

    // Check if the user is an admin
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user role from database
    const { data: userData } = await supabase
      .from('users')
      .select('role')
      .eq('email', user.email ?? '')
      .single()

    if (!userData || (userData.role !== 'admin' && userData.role !== 'super_admin')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get all auth users
    // Use a type assertion to avoid TypeScript errors
    const supabaseAny = supabase as unknown;
    const { data: authUsers, error: authError } = await supabaseAny
      .from('auth.users')
      .select('id, email')

    if (authError) {
      console.error('Error fetching auth users:', authError)
      return NextResponse.json({ error: 'Error fetching auth users' }, { status: 500 })
    }

    // Get all public users
    const { data: publicUsers, error: publicError } = await supabase
      .from('users')
      .select('id, email')

    if (publicError) {
      console.error('Error fetching public users:', publicError)
      return NextResponse.json({ error: 'Error fetching public users' }, { status: 500 })
    }

    // Match users by email and update auth_user_id
    const updates = []
    for (const publicUser of publicUsers) {
      // Use type assertion to handle potential type errors
      const typedPublicUser = publicUser as unknown;

      // Check if the user already has an auth_user_id
      const { data: existingAuthId } = await supabaseAny
        .from('users')
        .select('auth_user_id')
        .eq('id', typedPublicUser.id)
        .single();

      if (!existingAuthId?.auth_user_id) {
        // Find matching auth user by email
        const matchingAuthUser = authUsers?.find((au: unknown) => {
          const authUser = au as { email?: string; id?: string };
          return authUser.email === typedPublicUser.email;
        });

        if (matchingAuthUser) {
          // Use type assertion for matchingAuthUser
          const typedAuthUser = matchingAuthUser as unknown;

          // Use a different property name to avoid TypeScript errors
          const updateData: Record<string, unknown> = {};
          updateData.auth_user_id = typedAuthUser.id;

          const { error: updateError } = await supabase
            .from('users')
            .update(updateData)
            .eq('id', typedPublicUser.id)

          if (updateError) {
            console.error(`Error updating user ${typedPublicUser.id}:`, updateError)
            updates.push({ id: typedPublicUser.id, success: false, error: updateError.message })
          } else {
            updates.push({ id: typedPublicUser.id, success: true, auth_user_id: typedAuthUser.id })
          }
        } else {
          updates.push({ id: typedPublicUser.id, success: false, error: 'No matching auth user found' })
        }
      } else {
        updates.push({ id: typedPublicUser.id, success: true, message: 'Already has auth_user_id' })
      }
    }

    return NextResponse.json({
      success: true,
      message: 'User migration completed',
      updates
    })
  } catch (err) {
    console.error('Error in migrate-users route:', err)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
