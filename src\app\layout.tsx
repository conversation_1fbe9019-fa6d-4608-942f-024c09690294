import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import React from 'react';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';
import { Providers } from './providers';
import { <PERSON>ieBanner } from '@/components/cookie/cookie-banner';
import { CookiePreferences } from '@/components/cookie/cookie-preferences';

import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Fuiyoo',
  description: 'Multi-tenant application platform',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.className} antialiased min-h-screen`}>
        <Providers>
          <div className="flex flex-col min-h-screen bg-background text-foreground">
            <Header />
            <main className="flex-grow">
              {children}
            </main>
            <Footer />
            <CookieBanner />
            <CookiePreferences />
          </div>
        </Providers>
      </body>
    </html>
  );
}