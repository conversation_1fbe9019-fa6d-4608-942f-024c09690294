import { BaseRepository } from '@/lib/db/base-repository';
import { ContactSchema, type ContactFormData } from '@/lib/schemas/contacts';

/**
 * Repository for the saved_contacts table
 * Implements the BaseRepository pattern with retry logic and schema cache management
 */
export class ContactsRepository extends BaseRepository<ContactFormData> {
  constructor() {
    // Initialize with table name and validation schema
    super('saved_contacts', ContactSchema);
  }

  /**
   * Find contacts by user ID
   */
  async findByUserId(userId: string) {
    return this.find({ userId });
  }

  /**
   * Upsert contact (create or update)
   */
  async upsert(data: ContactFormData) {
    if (data.id) {
      return this.update(data.id, data);
    } else {
      return this.create(data);
    }
  }

  /**
   * Mark a contact as emergency contact
   */
  async markAsEmergencyContact(id: string, userId: string) {
    // Get admin client to perform advanced operation
    return this.executeCustom(async (supabase) => {
      // First clear any existing emergency contacts for this user
      const { error: clearError } = await supabase
        .from('saved_contacts')
        .update({ isEmergencyContact: false })
        .eq('userId', userId)
        .eq('isEmergencyContact', true);

      if (clearError) {
        return {
          success: false,
          error: clearError,
          message: `Failed to clear existing emergency contacts: ${clearError.message}`,
        };
      }

      // Set the new emergency contact
      const { data: result, error } = await supabase
        .from('saved_contacts')
        .update({ isEmergencyContact: true })
        .eq('id', id)
        .eq('userId', userId)
        .select()
        .single();

      if (error) {
        return {
          success: false,
          error,
          message: `Failed to mark contact as emergency contact: ${error.message}`,
        };
      }

      return {
        success: true,
        data: result as ContactFormData,
      };
    });
  }

  /**
   * Execute a custom operation with the repository's error handling
   */
  private async executeCustom<R>(
    operation: (supabase: unknown) => Promise<R>
  ) {
    try {
      const { createClient } = await import('@/lib/supabase/server');
      const supabase = await createClient();
      
      return await operation(supabase);
    } catch (error) {
      return {
        success: false,
        error,
        message: `Custom operation failed: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }
} 