"use client"

import * as React from "react"
import { ChevronDown } from "lucide-react"
import { cn } from "@/lib/utils"

const AccordionContext = React.createContext<{
  expanded: string | null
  setExpanded: React.Dispatch<React.SetStateAction<string | null>>
} | undefined>(undefined)

type AccordionProps = {
  children: React.ReactNode
  defaultValue?: string
  className?: string
}

export function Accordion({ children, defaultValue, className }: AccordionProps) {
  const [expanded, setExpanded] = React.useState<string | null>(defaultValue || null)

  return (
    <AccordionContext.Provider value={{ expanded, setExpanded }}>
      <div className={cn("divide-y divide-border rounded-lg border", className)}>
        {children}
      </div>
    </AccordionContext.Provider>
  )
}

type AccordionItemProps = {
  children: React.ReactNode
  value: string
  className?: string
}

export function AccordionItem({ children, value, className }: AccordionItemProps) {
  const context = React.useContext(AccordionContext)

  if (!context) {
    throw new Error("AccordionItem must be used within an Accordion")
  }

  const { expanded, setExpanded } = context
  const isExpanded = expanded === value

  const toggleExpanded = () => {
    setExpanded(isExpanded ? null : value)
  }

  return (
    <div className={cn("px-4", className)}>
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child) && child.type === AccordionTrigger) {
          return React.cloneElement(child as React.ReactElement<AccordionTriggerProps>, {
            isExpanded,
            onClick: toggleExpanded,
          })
        }
        if (React.isValidElement(child) && child.type === AccordionContent) {
          return React.cloneElement(child as React.ReactElement<AccordionContentProps>, {
            isExpanded,
          })
        }
        return child
      })}
    </div>
  )
}

type AccordionTriggerProps = {
  children: React.ReactNode
  className?: string
  isExpanded?: boolean
  onClick?: () => void
}

export function AccordionTrigger({ children, className, isExpanded, onClick }: AccordionTriggerProps) {
  return (
    <button
      type="button"
      onClick={onClick}
      className={cn(
        "flex w-full items-center justify-between py-4 font-medium transition-all hover:underline",
        className
      )}
      aria-expanded={isExpanded}
    >
      {children}
      <ChevronDown
        className={cn(
          "h-4 w-4 shrink-0 transition-transform duration-200",
          isExpanded ? "rotate-180" : "rotate-0"
        )}
      />
    </button>
  )
}

type AccordionContentProps = {
  children: React.ReactNode
  className?: string
  isExpanded?: boolean
}

export function AccordionContent({ children, className, isExpanded }: AccordionContentProps) {
  return (
    <div
      className={cn(
        "overflow-hidden transition-all duration-300",
        isExpanded ? "max-h-96 pb-4" : "max-h-0"
      )}
    >
      <div className={cn("pt-0", className)}>
        {children}
      </div>
    </div>
  )
}