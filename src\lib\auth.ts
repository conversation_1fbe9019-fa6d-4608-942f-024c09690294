import { UserRole } from '@/types/roles';
import { adminSupabase } from '@/lib/supabase/admin-client';
import { logger } from '@/lib/logger';

/**
 * Get the current user's ID from Supabase Auth
 * @returns The user ID or null if not authenticated
 */
export async function getUserId(): Promise<string | null> {
  const supabase = await createClient();
  const { data: { session } } = await supabase.auth.getSession();
  return session?.user?.id || null;
}

/**
 * Get the current user's role from Supabase Auth
 * This function is cached to avoid multiple API calls for the same user
 * @returns The user's role or USER if not found
 */
export const getUserRole = cache(async (): Promise<UserRole | null> => {
  const supabase = await createClient();
  const { data: { session } } = await supabase.auth.getSession();

  if (!session?.user) {
    return null;
  }

  try {
    // First check user metadata from the session
    const metadata = session.user.user_metadata as { role?: UserRole } || {};
    if (metadata.role) {
      return metadata.role;
    }

    // If not found in metadata, check the users table
    const { data: userData, error: _error } = await supabase
      .from('users')
      .select('role')
      .eq('auth_user_id', session.user.id)
      .single();

    if (_error) {
      logger.error("Error getting user role from database:", _error);
      return UserRole.USER;
    }

    // Validate that the role is a valid UserRole
    if (userData.role && Object.values(UserRole).includes(userData.role as UserRole)) {
      return userData.role as UserRole;
    }

    return UserRole.USER;
  } catch (_error) {
    logger.error("Error getting user role:", _error);
    return UserRole.USER;
  }
});

/**
 * Check if the current user has the specified role
 * @param role The role to check for
 * @returns True if the user has the role, false otherwise
 */
export async function hasRole(role: UserRole): Promise<boolean> {
  const userRole = await getUserRole();
  return userRole === role;
}

/**
 * Check if the current user has any of the specified roles
 * @param roles The roles to check for
 * @returns True if the user has any of the roles, false otherwise
 */
export async function hasAnyRole(roles: UserRole[]): Promise<boolean> {
  const userRole = await getUserRole();

  if (!userRole) {
    return false;
  }

  return roles.includes(userRole);
}

/**
 * Check if the current user is an admin
 * @returns True if the user is an admin or super admin, false otherwise
 */
export async function isAdmin(): Promise<boolean> {
  return hasAnyRole([UserRole.ADMIN, UserRole.SUPER_ADMIN]);
}

/**
 * Check if the current user is a super admin
 * @returns True if the user is a super admin, false otherwise
 */
export async function isSuperAdmin(): Promise<boolean> {
  return hasRole(UserRole.SUPER_ADMIN);
}

/**
 * Check if the current user is an event organizer
 * @returns True if the user is an event organizer, false otherwise
 */
export async function isEventOrganizer(): Promise<boolean> {
  return hasRole(UserRole.EVENT_ORGANIZER);
}

/**
 * Check if the current user has a specific role or higher
 * @param requiredRole The minimum role required
 * @returns True if the user has the required role or higher
 */
export async function hasRoleOrHigher(requiredRole: UserRole): Promise<boolean> {
  const userRole = await getUserRole();

  if (!userRole) {
    return false;
  }

  // Role hierarchy: SUPER_ADMIN > ADMIN > EVENT_ORGANIZER > USER
  switch (requiredRole) {
    case UserRole.USER:
      return true; // All authenticated users have at least USER role
    case UserRole.EVENT_ORGANIZER:
      return userRole === UserRole.EVENT_ORGANIZER || userRole === UserRole.ADMIN || userRole === UserRole.SUPER_ADMIN;
    case UserRole.ADMIN:
      return userRole === UserRole.ADMIN || userRole === UserRole.SUPER_ADMIN;
    case UserRole.SUPER_ADMIN:
      return userRole === UserRole.SUPER_ADMIN;
    default:
      return false;
  }
}