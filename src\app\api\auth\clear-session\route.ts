import { logger } from '@/lib/logger';
import { NextRequest, NextResponse } from 'next/server'

export const dynamic = 'force-dynamic'

// Keep track of requests to prevent loops
const requestCache = new Set<string>()

/**
 * Clear all Supabase-related cookies
 */
export async function GET(request: NextRequest) {
  return handleClearSession(request);
}

/**
 * Support POST method for programmatic access
 */
export async function POST(request: NextRequest) {
  return handleClearSession(request);
}

/**
 * Shared handler for both GET and POST requests
 */
async function handleClearSession(request: NextRequest) {
  // Generate a unique ID for this request based on timestamp
  const requestId = Date.now().toString()

  // Check if we've seen too many requests recently (potential loop)
  if (requestCache.size > 5) {
    logger.warn('Too many clear-session requests detected, potential loop')
    requestCache.clear() // Reset the cache
    return NextResponse.json({ success: false, error: 'Too many requests' }, { status: 429 })
  }

  // Add this request to the cache
  requestCache.add(requestId)

  // Clear the cache after 10 seconds
  setTimeout(() => {
    requestCache.delete(requestId)
  }, 10000)

  // Clear all Supabase-related cookies
  const supabaseCookies = [
    // Supabase cookies
    'sb-access-token',
    'sb-refresh-token',
    'supabase-auth-token',
    '__supabase_session',
    'sb-provider-token',
    'sb-auth-token',
    'sb-eibzxudhnojsdxksgowo-auth-token',
    'sb-eibzxudhnojsdxksgowo-auth-token.0',
    'sb-eibzxudhnojsdxksgowo-auth-token.1',
    'sb-eibzxudhnojsdxksgowo-auth-token-code-verifier'
  ]

  // Create a response object
  const response = NextResponse.json({ success: true })

  // Set cookies to expire in the response
  for (const cookieName of supabaseCookies) {
    try {
      // Use the response object to set cookies with expired date
      response.cookies.set({
        name: cookieName,
        value: '',
        expires: new Date(0),
        path: '/',
      })
    } catch (error) {
      logger.error(`Error clearing cookie ${cookieName}:`, error)
    }
  }

  // Set a special cookie to indicate that the session has been cleared
  // This will be used by the AuthButtons component to show the sign-in buttons
  // instead of the loading state
  response.cookies.set({
    name: 'sb-reset-complete',
    value: 'true',
    maxAge: 60, // Only keep this cookie for 60 seconds
    path: '/',
  })

  return response
}
