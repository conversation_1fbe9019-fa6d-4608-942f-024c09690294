const fs = require('fs');
const path = require('path');

console.log('🚀 FINAL ESLINT CLEANUP - Quick Wins Phase');
console.log('==========================================');

// Fix 1: Unnecessary escapes (Priority 1 - 4 errors)
const fixUnnecessaryEscapes = () => {
  console.log('\n🔧 Fixing unnecessary escapes...');
  const files = [
    'src/lib/url-utils.ts',
    'src/utils/url-utilities.ts'
  ];
  
  let totalFixes = 0;
  
  files.forEach(file => {
    try {
      let content = fs.readFileSync(file, 'utf8');
      const originalContent = content;
      
      // Fix unnecessary escapes in regex patterns
      content = content.replace(/\\\//g, '/');
      
      if (content !== originalContent) {
        fs.writeFileSync(file, content);
        totalFixes++;
        console.log(`  ✅ Fixed escapes in: ${file}`);
      }
    } catch (error) {
      console.error(`  ❌ Error fixing ${file}:`, error.message);
    }
  });
  
  console.log(`  📊 Fixed unnecessary escapes in ${totalFixes} files`);
  return totalFixes;
};

// Fix 2: Empty interfaces (Priority 2 - 2 errors)
const fixEmptyInterfaces = () => {
  console.log('\n🔧 Fixing empty interfaces...');
  const files = [
    'src/components/ui/command.tsx',
    'src/components/ui/textarea.tsx'
  ];
  
  let totalFixes = 0;
  
  files.forEach(file => {
    try {
      let content = fs.readFileSync(file, 'utf8');
      const originalContent = content;
      
      // Replace empty interfaces with type aliases
      content = content.replace(
        /interface\s+(\w+)\s+extends\s+([^{]+)\s*{\s*}/g,
        'type $1 = $2;'
      );
      
      if (content !== originalContent) {
        fs.writeFileSync(file, content);
        totalFixes++;
        console.log(`  ✅ Fixed empty interface in: ${file}`);
      }
    } catch (error) {
      console.error(`  ❌ Error fixing ${file}:`, error.message);
    }
  });
  
  console.log(`  📊 Fixed empty interfaces in ${totalFixes} files`);
  return totalFixes;
};

// Fix 3: Simple any types (Priority 3 - targeting easy wins)
const fixSimpleAnyTypes = () => {
  console.log('\n🔧 Fixing simple any types...');
  
  const getAllTSFiles = (dir) => {
    const files = [];
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        files.push(...getAllTSFiles(fullPath));
      } else if (item.endsWith('.ts') || item.endsWith('.tsx')) {
        files.push(fullPath);
      }
    }
    
    return files;
  };
  
  const files = getAllTSFiles('src');
  let totalFixes = 0;
  let filesModified = 0;
  
  const simpleReplacements = [
    // Simple variable declarations
    { from: /:\s*any\s*=\s*null/g, to: ': unknown = null' },
    { from: /:\s*any\s*=\s*undefined/g, to: ': unknown = undefined' },
    { from: /:\s*any\s*=\s*{}/g, to: ': Record<string, unknown> = {}' },
    { from: /:\s*any\s*=\s*\[\]/g, to: ': unknown[] = []' },
    
    // Generic types
    { from: /Promise<any>/g, to: 'Promise<unknown>' },
    { from: /Array<any>/g, to: 'Array<unknown>' },
    
    // Simple catch blocks
    { from: /catch\s*\(\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*:\s*any\s*\)/g, to: 'catch ($1: unknown)' },
  ];
  
  files.forEach(file => {
    try {
      let content = fs.readFileSync(file, 'utf8');
      const originalContent = content;
      let fileFixCount = 0;
      
      simpleReplacements.forEach(({ from, to }) => {
        const matches = content.match(from);
        if (matches) {
          content = content.replace(from, to);
          fileFixCount += matches.length;
        }
      });
      
      if (content !== originalContent) {
        fs.writeFileSync(file, content);
        filesModified++;
        totalFixes += fileFixCount;
        console.log(`  ✅ Fixed ${fileFixCount} any types in: ${file}`);
      }
    } catch (error) {
      console.error(`  ❌ Error fixing ${file}:`, error.message);
    }
  });
  
  console.log(`  📊 Fixed ${totalFixes} simple any types in ${filesModified} files`);
  return totalFixes;
};

// Execute all quick wins
console.log('Starting automated quick wins...\n');

const escapesFixes = fixUnnecessaryEscapes();
const interfacesFixes = fixEmptyInterfaces();
const anyTypesFixes = fixSimpleAnyTypes();

const totalQuickWinFixes = escapesFixes + interfacesFixes + anyTypesFixes;

console.log('\n🎉 QUICK WINS SUMMARY');
console.log('====================');
console.log(`✅ Unnecessary escapes fixed: ${escapesFixes}`);
console.log(`✅ Empty interfaces fixed: ${interfacesFixes}`);
console.log(`✅ Simple any types fixed: ${anyTypesFixes}`);
console.log(`🚀 Total quick win fixes: ${totalQuickWinFixes}`);
console.log('\nReady for manual complex fixes phase...');
