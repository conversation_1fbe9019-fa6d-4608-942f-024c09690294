/**
 * Environment-specific configuration
 *
 * This module demonstrates how to use the environment utilities
 * to configure different behavior based on the current build environment.
 */

import { isTurbopack, isWebpack, isDev, isProd, logEnvironmentInfo } from '@/utils/env-helpers';
import { logger } from '@/lib/logger';

// Log the current environment when this module is imported
if (typeof window !== 'undefined') {
  // Only log in browser context
  logEnvironmentInfo();
}

/**
 * Configuration settings that can vary by environment
 */
export const envConfig = {
  /**
   * API request timeout in milliseconds
   * Longer in development for easier debugging
   */
  apiTimeout: isDev() ? 30000 : 10000,

  /**
   * Whether to enable verbose logging
   */
  verboseLogging: isDev(),

  /**
   * Cache duration in seconds
   * Shorter in development to see changes more quickly
   */
  cacheDuration: isDev() ? 60 : 3600,

  /**
   * Whether to enable analytics
   * Disabled in development to avoid skewing metrics
   */
  enableAnalytics: isProd(),

  /**
   * Base URL for API requests
   * Uses different endpoints for development and production
   * Can be configured with NEXT_PUBLIC_DEV_API_URL environment variable
   */
  apiBaseUrl: isDev()
    ? `${process?.env?.NEXT_PUBLIC_DEV_API_URL || 'http://localhost:3000/api'}`
    : `${process?.env?.NEXT_PUBLIC_SITE_URL || 'https://fuiyoo.netlify.app'}/api`,

  /**
   * Feature flags that can be enabled/disabled per environment
   */
  features: {
    // Example of a feature only available in Turbopack development
    experimentalUI: isTurbopack(),

    // Example of a feature only available in production
    premiumContent: isWebpack() && isProd(),

    // Feature available in all environments
    basicFunctionality: true,
  }
};

/**
 * Logs all configuration settings
 * Useful for debugging environment-specific configuration
 */
export function logConfig(): void {
  console.group('Environment Configuration');
  Object.entries(envConfig).forEach(([key, value]) => {
    if (typeof value === 'object') {
      console.group(key);
      Object.entries(value).forEach(([subKey, subValue]) => {
        logger.info(`${subKey}: ${subValue}`);
      });
      console.groupEnd();
    } else {
      logger.info(`${key}: ${value}`);
    }
  });
  console.groupEnd();
}