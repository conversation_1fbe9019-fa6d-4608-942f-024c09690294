import { createClient } from '@/lib/supabase/pages-client';
import { NextResponse } from "next/server";

export async function POST(request: Request) {
  try {
    const supabase = await createClient();
    const { data: { session } } = await supabase.auth.getSession();

    // Only allow authenticated users
    if (!session || !session.user) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const userId = session.user.id;

    // Check if user has admin role
    const { data: user } = await supabase
      .from('users')
      .select('role')
      .eq('id', userId)
      .single();

    if (!user || !user.role || !['ADMIN', 'SUPER_ADMIN'].includes(user.role)) {
      return new NextResponse("Forbidden", { status: 403 });
    }

    // Apply the migration
    // Use a type assertion to access the query method
    const supabaseAny = supabase as unknown;
    const { error } = await supabaseAny.query(`
      -- Add tshirt_size column to users table
      ALTER TABLE users ADD COLUMN IF NOT EXISTS tshirt_size TEXT;

      -- Add comment for documentation
      COMMENT ON COLUMN users.tshirt_size IS 'User T-shirt size preference';

      -- Update schema_migrations table if it exists
      INSERT INTO schema_migrations (version, applied_at)
      VALUES ('0012_add_tshirt_size_to_users', NOW())
      ON CONFLICT (version) DO UPDATE SET applied_at = NOW();
    `);

    if (error) {
      console.error("Error applying migration:", error);
      return new NextResponse("Error applying migration", { status: 500 });
    }

    return NextResponse.json({ message: "Migration applied successfully" });
  } catch (error) {
    console.error("Error in migration API:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}