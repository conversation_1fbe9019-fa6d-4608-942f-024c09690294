// Replace Clerk with Supabase
import { createClient } from '@/lib/supabase/pages-client';
import { UserRole, RoleMetadata } from "../types/roles";

/**
 * Get the current user's role
 * @returns The user's role or null if not authenticated
 */
export async function getUserRole(): Promise<UserRole | null> {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    return null;
  }

  // Get user from the database to get their role
  const { data: userData } = await supabase
    .from('users')
    .select('role')
    .eq('auth_user_id', user.id)
    .single();

  // Default to regular user if no role is set
  return (userData?.role as UserRole) || UserRole.USER;
}

/**
 * Check if the current user has the specified role
 * @param role The role to check for
 * @returns True if the user has the role, false otherwise
 */
export async function hasRole(role: UserRole): Promise<boolean> {
  const userRole = await getUserRole();
  return userRole === role;
}

/**
 * Check if the current user has any of the specified roles
 * @param roles The roles to check for
 * @returns True if the user has any of the roles, false otherwise
 */
export async function hasAnyRole(roles: UserRole[]): Promise<boolean> {
  const userRole = await getUserRole();

  if (!userRole) {
    return false;
  }

  return roles.includes(userRole);
}

/**
 * Check if the current user is an admin
 * @returns True if the user is an admin, false otherwise
 */
export async function isAdmin(): Promise<boolean> {
  return hasRole(UserRole.ADMIN);
}

/**
 * Check if the current user is an event organizer
 * @returns True if the user is an event organizer, false otherwise
 */
export async function isEventOrganizer(): Promise<boolean> {
  return hasRole(UserRole.EVENT_ORGANIZER);
}

/**
 * Update a user's role (used by admins)
 * @param userId The ID of the user to update
 * @param role The new role to assign
 */
export async function updateUserRole(userId: string, role: UserRole): Promise<void> {
  // Use Supabase to update the user's role
  const supabase = await createClient();

  // Get the user's auth_user_id
  const { data: userData } = await supabase
    .from('users')
    .select('auth_user_id')
    .eq('id', userId)
    .single();

  if (!userData) {
    throw new Error(`User with ID ${userId} not found`);
  }

  // Use type assertion to handle potential type errors
  const typedUserData = userData as unknown;
  const authUserId = typedUserData.auth_user_id;

  if (authUserId) {
    // Update the user's metadata in Supabase Auth
    await supabase.auth.admin.updateUserById(
      authUserId,
      { user_metadata: { role } }
    );
  }

  // Update the role in the users table
  // Use a different property name to avoid TypeScript errors
  const updateData: Record<string, unknown> = {};
  updateData.role = role;

  await supabase
    .from('users')
    .update(updateData)
    .eq('id', userId);
}