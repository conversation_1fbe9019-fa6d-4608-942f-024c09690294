import { logger } from '@/lib/logger';
/**
 * Supabase client for App Router
 * This file provides a client that works with the App Router
 * without using next/headers in client components
 */

import { createBrowserClient } from '@supabase/ssr'
import { type Database } from './types'

// Singleton instance for browser client
let browserInstance: ReturnType<typeof createBrowserClient<Database>> | null = null

/**
 * Create a Supabase client for App Router
 * This is a client-side solution that doesn't rely on next/headers in client components
 */
export function createPagesClient() {
  // Validate environment variables
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseKey) {
    const missingVars = []
    if (!supabaseUrl) missingVars.push('NEXT_PUBLIC_SUPABASE_URL')
    if (!supabaseKey) missingVars.push('NEXT_PUBLIC_SUPABASE_ANON_KEY')

    const errorMessage = `Missing Supabase credentials: ${missingVars.join(', ')}. Please check your environment variables.`
    logger.error(errorMessage)
    throw new Error(errorMessage)
  }

  // Create browser client (works in both client and server in App Router)
  if (typeof window !== 'undefined') {
    // Client-side: Use singleton pattern
    if (browserInstance) return browserInstance
    
    try {
      logger.info('Creating Supabase browser client with URL:', supabaseUrl)
      browserInstance = createBrowserClient<Database>(supabaseUrl, supabaseKey)
      logger.info('Supabase client created successfully')
      return browserInstance
    } catch (error) {
      logger.error('Error creating Supabase client:', error)
      throw error
    }
  } else {
    // Server-side: Create a new instance each time (no cookies)
    return createBrowserClient<Database>(supabaseUrl, supabaseKey)
  }
}

/**
 * Create a Supabase admin client with service role key for privileged operations
 * This should only be used on the server side and for operations that require admin privileges
 * SECURITY WARNING: Never expose this client to the browser or client-side code
 */
export function createAdminClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseUrl || !supabaseServiceKey) {
    const missingVars = []
    if (!supabaseUrl) missingVars.push('NEXT_PUBLIC_SUPABASE_URL')
    if (!supabaseServiceKey) missingVars.push('SUPABASE_SERVICE_ROLE_KEY')

    const errorMessage = `Missing Supabase admin credentials: ${missingVars.join(', ')}. Please check your environment variables.`
    logger.error(errorMessage)
    throw new Error(errorMessage)
  }

  return createBrowserClient<Database>(supabaseUrl, supabaseServiceKey)
}

// Export a singleton instance for client-side use
export const supabase = typeof window !== 'undefined' ? createPagesClient() : null

// For backward compatibility
export const createClient = createPagesClient
