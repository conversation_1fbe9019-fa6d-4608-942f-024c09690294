import { createAdminClient } from '@/lib/supabase/admin-client';
import { z } from 'zod';
import { asAny } from '@/lib/supabase/extended-types';
import { logger } from '@/lib/logger';

/**
 * Schema Management Service
 *
 * Handles schema cache refreshes, validations, and health checks to ensure
 * database schema and cache consistency.
 */
export class SchemaManager {
  /**
   * Forces a schema cache refresh using the pg_notify mechanism
   * This should be called after schema changes (migrations)
   */
  static async refreshSchemaCache() {
    try {
      const supabase = await createAdminClient();
      const supabaseAny = asAny(supabase);

      // Send notification to PostgREST to reload schema
      const { data, error: _error } = await supabaseAny.rpc('pg_notify', {
        channel: 'pgrst',
        payload: 'reload schema'
      });

      if (_error) {
        logger.error('Failed to refresh schema cache:', _error);
        return {
          success: false,
          error,
          message: `Schema cache refresh failed: ${error.message}`
        };
      }

      logger.info('Schema cache refresh requested successfully');
      return {
        success: true,
        message: 'Schema cache refresh requested successfully'
      };
    } catch (_error) {
      logger.error('Error during schema cache refresh:', _error);
      return {
        success: false,
        error,
        message: error instanceof Error ? error.message : 'Unknown error during schema cache refresh'
      };
    }
  }

  /**
   * Validates schema consistency by comparing postgres schema with api responses
   * Useful for detecting schema cache issues
   */
  static async validateSchemaConsistency(tableName: string) {
    try {
      const supabase = await createAdminClient();
      const supabaseAny = asAny(supabase);

      // Get table structure from information_schema
      const { data: schemaData, error: schemaError } = await supabaseAny.rpc('exec_sql', {
        sql: `SELECT column_name, data_type, is_nullable
              FROM information_schema.columns
              WHERE table_name = '${tableName}'
              ORDER BY ordinal_position`
      });

      if (schemaError) {
        logger.error(`Failed to get schema for ${tableName}:`, schemaError);
        return {
          success: false,
          error: schemaError,
          message: `Schema validation failed: ${schemaError.message}`
        };
      }

      // Query the table through API to test if schema cache matches actual schema
      const { data: tableData, error: tableError } = await supabaseAny
        .from(tableName)
        .select('*')
        .limit(1);

      // Test if we can access the expected columns
      if (tableError) {
        logger.error(`Schema validation failed for ${tableName}:`, tableError);

        // If there's an error, try forcing a schema refresh
        await this.refreshSchemaCache();

        return {
          success: false,
          error: tableError,
          schemaData, // Return schema data for diagnosis
          message: `Schema validation failed, refresh attempted: ${tableError.message}`
        };
      }

      return {
        success: true,
        schemaData,
        message: `Schema validation successful for ${tableName}`
      };
    } catch (_error) {
      logger.error('Error during schema validation:', _error);
      return {
        success: false,
        error,
        message: error instanceof Error ? error.message : 'Unknown error during schema validation'
      };
    }
  }

  /**
   * Gets migration status from schema_migrations table
   */
  static async getMigrationStatus() {
    try {
      const supabase = await createAdminClient();
      const supabaseAny = asAny(supabase);

      const { data: migrations, error: _error } = await supabaseAny
        .from('schema_migrations')
        .select('*')
        .order('applied_at', { ascending: false });

      if (_error) {
        logger.error('Failed to get migration status:', _error);
        return {
          success: false,
          error,
          message: `Failed to get migration status: ${error.message}`
        };
      }

      return {
        success: true,
        migrations,
        message: 'Migration status retrieved successfully'
      };
    } catch (_error) {
      logger.error('Error getting migration status:', _error);
      return {
        success: false,
        error,
        message: error instanceof Error ? error.message : 'Unknown error getting migration status'
      };
    }
  }

  /**
   * Comprehensive schema health check
   * Checks database connection, schema consistency, and migration status
   */
  static async checkSchemaHealth() {
    try {
      const supabase = await createAdminClient();
      const supabaseAny = asAny(supabase);

      // Check database connection
      const connCheck = await supabaseAny.from('schema_migrations').select('count').limit(1);

      // If connection fails, don't proceed with other checks
      if (connCheck.error) {
        return {
          success: false,
          status: 'error',
          database: 'disconnected',
          error: connCheck.error,
          message: `Database connection failed: ${connCheck.error.message}`
        };
      }

      // Check a few critical tables for schema consistency
      const criticalTables = ['saved_contacts', 'users', 'schema_migrations'];
      const schemaChecks = await Promise.all(
        criticalTables.map(table => this.validateSchemaConsistency(table))
      );

      // Get migration status
      const migrationStatus = await this.getMigrationStatus();

      // Determine overall health
      const allHealthy = schemaChecks.every(check => check.success) && migrationStatus.success;

      return {
        success: allHealthy,
        status: allHealthy ? 'healthy' : 'unhealthy',
        database: 'connected',
        tables: criticalTables.reduce((acc, table, i) => {
          if (i < schemaChecks.length && schemaChecks[i]) {
            acc[table] = {
              status: schemaChecks[i].success ? 'healthy' : 'error',
              message: schemaChecks[i].message
            };
          } else {
            acc[table] = {
              status: 'unknown',
              message: 'Schema check not performed'
            };
          }
          return acc;
        }, {} as Record<string, unknown>),
        migrations: migrationStatus.success ? {
          status: 'available',
          count: migrationStatus.migrations?.length || 0,
          latest: migrationStatus.migrations?.[0]
        } : {
          status: 'error',
          message: migrationStatus.message
        },
        checkedAt: new Date().toISOString()
      };
    } catch (_error) {
      logger.error('Error checking schema health:', _error);
      return {
        success: false,
        status: 'error',
        error,
        message: error instanceof Error ? error.message : 'Unknown error checking schema health',
        checkedAt: new Date().toISOString()
      };
    }
  }
}

// Schema for the migration template
export const MigrationSchema = z.object({
  name: z.string().regex(/^\d{4}_[a-z0-9_]+$/),
  sql: z.string(),
  description: z.string(),
});

export type MigrationType = z.infer<typeof MigrationSchema>;

/**
 * Creates a SQL migration with proper transaction wrapping and schema cache refresh
 */
export function createMigration(migration: MigrationType): string {
  return `-- ${migration.description}
-- Migration: ${migration.name}.sql

-- Start transaction for atomic migration
BEGIN;

${migration.sql}

-- Force schema cache reload
SELECT pg_notify('pgrst', 'reload schema');

-- Record migration in schema_migrations table
INSERT INTO schema_migrations (version, applied_at)
VALUES ('${migration.name}', NOW())
ON CONFLICT (version) DO UPDATE SET applied_at = NOW();

-- Commit transaction
COMMIT;
`;
}