'use server';

import { createServerActionClient } from "@/lib/supabase/actions";
import { revalidatePath } from "next/cache";
import { logger } from '@/lib/logger';

/**
 * Get activity history for the current user
 */
export async function getUserActivity(limit = 20, offset = 0) {
  try {
    const supabase = await createServerActionClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user?.id) {
      throw new Error("Unauthorized");
    }

    // Get the internal user ID from the users table
    const { data: userData, error: userIdError } = await supabase
      .from('users')
      .select('id')
      .eq('auth_user_id', user.id)
      .single();

    if (userIdError || !userData) {
      logger.error("Error fetching user ID:", userIdError);
      throw new Error("User not found");
    }

    const userId = userData.id;

    // Supabase client already created above

    const { data, error, count } = await supabase
      .from('profile_activity')
      .select('*', { count: 'exact' })
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      throw new Error(`Error fetching activity history: ${error.message}`);
    }

    return {
      activity: data,
      total: count,
      offset,
      limit
    };
  } catch (error) {
    logger.error("Error in getUserActivity:", error);
    return { error: error instanceof Error ? error.message : "Unknown error" };
  }
}

/**
 * Log a manual activity for the current user
 */
export async function logUserActivity(activityData: {
  activityType: string;
  activityDescription: string;
  fieldName?: string;
  previousValue?: Record<string, unknown>;
  newValue?: Record<string, unknown>;
  requestInfo?: { ip?: string; userAgent?: string; }
}) {
  try {
    const supabase = await createServerActionClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user?.id) {
      throw new Error("Unauthorized");
    }

    // Get the internal user ID from the users table
    const { data: userData, error: userIdError } = await supabase
      .from('users')
      .select('id')
      .eq('auth_user_id', user.id)
      .single();

    if (userIdError || !userData) {
      logger.error("Error fetching user ID:", userIdError);
      throw new Error("User not found");
    }

    const userId = userData.id;

    const { data, error } = await supabase
      .from('profile_activity')
      .insert({
        user_id: userId,
        activity_type: activityData.activityType,
        activity_description: activityData.activityDescription,
        field_name: activityData.fieldName || null,
        previous_value: activityData.previousValue || null,
        new_value: activityData.newValue || null,
        ip_address: activityData.requestInfo?.ip || null,
        user_agent: activityData.requestInfo?.userAgent || null,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Error logging activity: ${error.message}`);
    }

    revalidatePath('/dashboard/profile/activity');
    return { activity: data };
  } catch (error) {
    logger.error("Error in logUserActivity:", error);
    return { error: error instanceof Error ? error.message : "Unknown error" };
  }
}

/**
 * Get emergency contact access logs
 */
export async function getEmergencyContactAccessLogs(limit = 20, offset = 0) {
  try {
    const supabase = await createServerActionClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user?.id) {
      throw new Error("Unauthorized");
    }

    // Get the internal user ID from the users table
    const { data: userData, error: userIdError } = await supabase
      .from('users')
      .select('id')
      .eq('auth_user_id', user.id)
      .single();

    if (userIdError || !userData) {
      logger.error("Error fetching user ID:", userIdError);
      throw new Error("User not found");
    }

    const userId = userData.id;

    const { data, error, count } = await supabase
      .from('emergency_contact_access_logs')
      .select('*', { count: 'exact' })
      .eq('user_id', userId)
      .order('accessed_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      throw new Error(`Error fetching emergency contact access logs: ${error.message}`);
    }

    return {
      logs: data,
      total: count,
      offset,
      limit
    };
  } catch (error) {
    logger.error("Error in getEmergencyContactAccessLogs:", error);
    return { error: error instanceof Error ? error.message : "Unknown error" };
  }
}

/**
 * Log emergency contact access
 */
export async function logEmergencyContactAccess(logData: {
  userId: string;
  eventId: string;
  registrationId: string;
  reason: string;
  requestInfo?: { ip?: string; userAgent?: string; }
}) {
  try {
    const supabase = await createServerActionClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user?.id) {
      throw new Error("Unauthorized");
    }

    // Get the internal user ID from the users table
    const { data: userData, error: userIdError } = await supabase
      .from('users')
      .select('id')
      .eq('auth_user_id', user.id)
      .single();

    if (userIdError || !userData) {
      logger.error("Error fetching user ID:", userIdError);
      throw new Error("User not found");
    }

    const currentUserId = userData.id;

    const { data, error } = await supabase
      .from('emergency_contact_access_logs')
      .insert({
        user_id: logData.userId,
        event_id: logData.eventId,
        registration_id: logData.registrationId,
        accessed_by: currentUserId,
        accessed_at: new Date().toISOString(),
        reason: logData.reason,
        ip_address: logData.requestInfo?.ip || null,
        user_agent: logData.requestInfo?.userAgent || null
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Error logging emergency contact access: ${error.message}`);
    }

    return { log: data };
  } catch (error) {
    logger.error("Error in logEmergencyContactAccess:", error);
    return { error: error instanceof Error ? error.message : "Unknown error" };
  }
}