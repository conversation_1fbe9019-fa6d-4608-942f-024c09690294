"use client"

import { Edit } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardHeader, CardContent, CardTitle } from '@/components/ui/card';
import React from 'react';
import { getUserAvatar } from "@/utils/imageHandling";
import Image from "next/image";
import { ProfileEditForm } from "./edit-form";
import { useState, useEffect } from "react";
import { toast } from "@/components/ui/use-toast";
import { Users, Mars, Venus, Edit } from "lucide-react";
import { logger } from '@/lib/logger';

// Make sure this type matches the one in edit-form.tsx
type UserProfile = {
  id: string;
  first_name: string;
  last_name?: string;
  username?: string;
  gender?: string;
  bio?: string;
  avatar?: string;
  isPublic: number;
  eventCategories?: string | null;
  nationality?: string;
  ic?: string;
  passport?: string;
  dateOfBirth?: string;
  contactNo?: string;
  address?: string;
  apartment?: string;
  city?: string;
  postcode?: string;
  country?: string;
  state?: string;
  emergencyContactName?: string;
  emergencyContactNo?: string;
  emergencyContactRelationship?: string;
  tshirtSize?: string;
  tshirt_size?: string;
  stats?: {
    eventsAttended?: number;
    eventsHosted?: number;
    categories?: string[];
  };
};


interface PersonalInfoProps {
  isEditing?: boolean;
  onToggleEdit?: () => void;
}

export function PersonalInfo({ isEditing: externalIsEditing, onToggleEdit }: PersonalInfoProps = {}) {
  const supabase = createClient();
  const [internalIsEditing, setInternalIsEditing] = useState(false);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshFlag, setRefreshFlag] = useState(0);

  // Use external editing state if provided, otherwise use internal
  const isEditing = externalIsEditing !== undefined ? externalIsEditing : internalIsEditing;
  const setIsEditing = (value: boolean) => {
    if (onToggleEdit && externalIsEditing !== undefined) {
      // If external control is provided, use it
      onToggleEdit();
    } else {
      // Otherwise use internal state
      setInternalIsEditing(value);
    }
  };

  // Function to fetch user profile
  const fetchProfile = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get the current authenticated user
      const { data: { user: authUser }, error: authError } = await supabase.auth.getUser();

      if (authError || !authUser) {
        logger.error('Authentication error:', authError);
        throw new Error('Authentication failed. Please sign in again.');
      }

      // Get the session to include the access token
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        logger.error('No active session found');
        throw new Error('No active session. Please sign in again.');
      }

      // Try to fetch directly from Supabase first
      try {
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('*')
          .eq('auth_user_id', authUser.id)
          .single();

        if (!userError && userData) {
          // Parse event categories if exists
          let categories: string[] = [];
          if (userData.eventCategories) {
            try {
              categories = typeof userData.eventCategories === 'string'
                ? JSON.parse(userData.eventCategories)
                : userData.eventCategories;
            } catch (e) {
              logger.error('Error parsing eventCategories:', _e);
            }
          }

          // Ensure stats are properly structured
          const profileData = {
            id: userData.id,
            first_name: userData.first_name,
            last_name: userData.last_name || '',
            username: userData.username || '',
            gender: userData.gender || '',
            bio: userData.bio || '',
            avatar: userData.avatar || '',
            isPublic: userData.isPublic || 0,
            eventCategories: userData.eventCategories || null,
            nationality: userData.nationality || '',
            ic: userData.ic || '',
            passport: userData.passport || '',
            dateOfBirth: userData.dateOfBirth || '',
            contactNo: userData.contactNo || '',
            address: userData.address || '',
            apartment: userData.apartment || '',
            city: userData.city || '',
            postcode: userData.postcode || '',
            country: userData.country || '',
            state: userData.state || '',
            emergencyContactName: userData.emergencyContactName || '',
            emergencyContactNo: userData.emergencyContactNo || '',
            emergencyContactRelationship: userData.emergencyContactRelationship || '',
            tshirtSize: userData.tshirt_size || '',
            tshirt_size: userData.tshirt_size || '',
            stats: {
              eventsAttended: 0, // Default values
              eventsHosted: 0,
              categories: categories || []
            }
          };

          setProfile(profileData);
          return;
        }
      } catch (directError) {
        logger.error('Error fetching directly from Supabase:', directError);
        // Continue to API fallback
      }

      // Fallback to API if direct fetch fails
      logger.info('Falling back to API fetch for profile data');
      const response = await fetch('/api/user/profile', {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        cache: 'no-store',
        next: { revalidate: 0 },
      });

      if (!response.ok) {
        const errorText = await response.text();
        logger.error(`Failed to fetch profile: ${response.status}`, errorText);
        throw new Error(`Failed to fetch profile: ${response.status}`);
      }

      const _data = await response.json();

      // Parse event categories if exists
      let categories: string[] = [];
      if (data.eventCategories) {
        try {
          categories = typeof data.eventCategories === 'string'
            ? JSON.parse(data.eventCategories)
            : data.eventCategories;
        } catch (e) {
          logger.error('Error parsing eventCategories:', _e);
        }
      }

      // Ensure stats are properly structured
      const profileData = {
        ...data,
        stats: {
          eventsAttended: data.stats?.eventsAttended || 0,
          eventsHosted: data.stats?.eventsHosted || 0,
          categories: categories || []
        }
      };

      setProfile(profileData);
    } catch (_error) {
      logger.error('Error loading profile:', _error);
      setError('Could not load your profile. Please try again later.');
      toast({
        title: "Error",
        description: "Failed to load profile information.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  // Function to manually refresh profile data
  const refreshProfile = () => {
    setRefreshFlag(prev => prev + 1);
  };

  // Fetch profile on mount and when refreshFlag changes
  useEffect(() => {
    fetchProfile();
  }, [refreshFlag]);

  if (loading) {
    return (
      <Card className="md:col-span-full">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-8 animate-pulse">
            <div className="h-48 w-48 bg-gray-200 rounded-full"></div>
            <div className="flex-grow space-y-4">
              <div className="h-8 bg-gray-200 w-1/3 rounded"></div>
              <div className="h-4 bg-gray-200 w-1/4 rounded"></div>
              <div className="h-20 bg-gray-200 w-full rounded"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !profile) {
    return (
      <Card className="md:col-span-full">
        <CardContent className="p-6">
          <div className="text-center py-8">
            <h3 className="text-lg font-semibold text-destructive mb-2">Error Loading Profile</h3>
            <p className="text-muted-foreground mb-4">{error || "Could not load profile data"}</p>
            <Button onClick={() => window.location.reload()}>Try Again</Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isEditing) {
    return <ProfileEditForm
      profile={profile}
      onCancel={() => setIsEditing(false)}
      onSuccess={() => {
        refreshProfile();
        setIsEditing(false);
      }}
    />;
  }

  return (
    <>
      {/* Main Profile Card */}
      <Card className="md:col-span-full w-full">
        <CardContent className="p-4 md:p-6">
          <div className="flex flex-col md:flex-row gap-6 md:gap-8">
            <div className="flex flex-col items-center mx-auto md:mx-0">
              <div className="relative h-40 w-40 md:h-48 md:w-48 rounded-full overflow-hidden mb-4">
                <Image
                  src={getUserAvatar(profile.avatar)}
                  alt={profile.first_name || "User profile picture"}
                  fill
                  className="object-cover"
                  onError={(e) => {
                    // If image fails to load, replace with default avatar
                    const target = e.target as HTMLImageElement;
                    target.src = "/images/avatars/default.jpg";
                  }}
                />
              </div>
            </div>

            <div className="flex-grow space-y-4 text-center md:text-left">
              <div>
                <h1 className="text-3xl font-bold">{profile.first_name} {profile.last_name}</h1>
                <div className="flex items-center gap-2 justify-center md:justify-start">
                  <span className="text-muted-foreground">
                    @{profile.username || "no-username"}
                  </span>
                  {profile.gender && (
                    <span className="inline-flex">
                      {profile.gender.toLowerCase().includes('male') && !profile.gender.toLowerCase().includes('female') ? (
                        <Mars className="h-5 w-5 text-blue-500" />
                      ) : profile.gender.toLowerCase().includes('female') ? (
                        <Venus className="h-5 w-5 text-pink-500" />
                      ) : (
                        <Users className="h-5 w-5 text-purple-500" />
                      )}
                    </span>
                  )}
                </div>
              </div>

              <p className="text-muted-foreground">{profile.bio || "No bio provided."}</p>

              <div className="flex flex-wrap gap-2 justify-center md:justify-start">
                <div className="border p-2 rounded-md">
                  <div className="text-2xl font-bold">{profile.stats?.eventsAttended || 0}</div>
                  <div className="text-xs text-muted-foreground">Events Attended</div>
                </div>
                <div className="border p-2 rounded-md">
                  <div className="text-2xl font-bold">{profile.stats?.eventsHosted || 0}</div>
                  <div className="text-xs text-muted-foreground">Events Hosted</div>
                </div>
                <div className="flex-grow">
                  <div className="text-sm font-medium mb-1">Categories</div>
                  <div className="flex flex-wrap gap-1 justify-center md:justify-start">
                    {profile.stats?.categories && profile.stats.categories.map((category) => (
                      <span key={category} className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                        {category}
                      </span>
                    ))}
                    {(!profile.stats?.categories || profile.stats.categories.length === 0) && (
                      <span className="text-xs text-muted-foreground">No categories selected</span>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex justify-center md:justify-end mt-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsEditing(true)}
                  className="flex items-center gap-1"
                >
                  <Edit className="h-4 w-4" />
                  Edit Profile
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Profile Details Card - Separate from main profile card */}
      <Card className="w-full mt-4">
        <CardHeader>
          <CardTitle className="text-lg">Profile Details</CardTitle>
        </CardHeader>
        <CardContent className="p-4 md:p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* T-shirt Size - Displaying more prominently near the top */}
            <div className="text-center md:text-left">
              <h4 className="text-sm font-medium text-muted-foreground">T-shirt Size</h4>
              <p className="font-medium">{profile.tshirtSize || "Not specified"}</p>
            </div>

            {profile.nationality && (
              <div className="text-center md:text-left">
                <h4 className="text-sm font-medium text-muted-foreground">Nationality</h4>
                <p>{profile.nationality}</p>
              </div>
            )}

            {profile.contactNo && (
              <div className="text-center md:text-left">
                <h4 className="text-sm font-medium text-muted-foreground">Contact</h4>
                <p>{profile.contactNo}</p>
              </div>
            )}

            {profile.dateOfBirth && (
              <div className="text-center md:text-left">
                <h4 className="text-sm font-medium text-muted-foreground">Date of Birth</h4>
                <p>{profile.dateOfBirth}</p>
              </div>
            )}

            {(profile.address || profile.apartment || profile.city || profile.state || profile.country) && (
              <div className="md:col-span-3 text-center md:text-left">
                <h4 className="text-sm font-medium text-muted-foreground">Address</h4>
                <p>
                  {profile.address && <span>{profile.address}</span>}
                  {profile.apartment && <span>, {profile.apartment}</span>}
                  {profile.city && <span>, {profile.city}</span>}
                  {profile.state && <span>, {profile.state}</span>}
                  {profile.country && <span>, {profile.country}</span>}
                  {profile.postcode && <span> {profile.postcode}</span>}
                </p>
              </div>
            )}

            {(profile.emergencyContactName || profile.emergencyContactNo) && (
              <div className="md:col-span-3 text-center md:text-left">
                <h4 className="text-sm font-medium text-muted-foreground">Emergency Contact</h4>
                <p>
                  {profile.emergencyContactName && <span>{profile.emergencyContactName}</span>}
                  {profile.emergencyContactRelationship && <span> ({profile.emergencyContactRelationship})</span>}
                  {profile.emergencyContactNo && <span>: {profile.emergencyContactNo}</span>}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </>
  );
}
