import { logger } from '@/lib/logger';
import { createClient } from '@/lib/supabase/pages-client'
import { NextResponse } from 'next/server'
import { UserRole } from '@/types/roles'

export async function GET() {
  try {
    const supabase = await createClient()

    // Get the current user
    const { data: { user: authUser } } = await supabase.auth.getUser()

    if (!authUser) {
      return NextResponse.json({
        isAuthenticated: false,
        message: 'Not authenticated',
        role: null
      })
    }

    // Get user data from the database
    const { data: userByAuthId, error: authIdError } = await supabase
      .from('users')
      .select('id, role')
      .eq('auth_user_id', authUser.id)
      .maybeSingle()

    // If found by auth_user_id, use that user
    if (userByAuthId) {
      const user = userByAuthId
      return NextResponse.json({
        isAuthenticated: true,
        userId: user.id,
        role: user.role as UserRole || UserRole.USER,
        isAdmin: user.role === UserRole.ADMIN || user.role === UserRole.SUPER_ADMIN,
        isSuperAdmin: user.role === UserRole.SUPER_ADMIN,
        message: 'Role information retrieved successfully'
      })
    }

    // If not found by auth_user_id, try by email
    // Make sure authUser.email is defined
    if (!authUser.email) {
      return NextResponse.json({
        isAuthenticated: true,
        userId: authUser.id,
        role: UserRole.USER, // Default to regular user
        isAdmin: false,
        isSuperAdmin: false,
        message: 'User email not found, using default role'
      });
    }

    const { data: userByEmail, error: emailError } = await supabase
      .from('users')
      .select('id, role')
      .eq('email', authUser.email)
      .maybeSingle()

    if (userByEmail) {
      // Found user by email, update the auth_user_id for future queries
      // Use a type assertion to avoid TypeScript errors
      const updateData: Record<string, any> = {};
      updateData.auth_user_id = authUser.id;

      const { error: updateError } = await supabase
        .from('users')
        .update(updateData)
        .eq('id', userByEmail.id)

      if (updateError) {
        logger.error('Error updating auth_user_id:', updateError)
      }

      return NextResponse.json({
        isAuthenticated: true,
        userId: userByEmail.id,
        role: userByEmail.role as UserRole || UserRole.USER,
        isAdmin: userByEmail.role === UserRole.ADMIN || userByEmail.role === UserRole.SUPER_ADMIN,
        isSuperAdmin: userByEmail.role === UserRole.SUPER_ADMIN,
        message: 'Role information retrieved successfully (matched by email)'
      })
    }

    // If no user found at all
    logger.error('User not found in database')
    return NextResponse.json({
      isAuthenticated: true,
      userId: authUser.id,
      role: UserRole.USER, // Default to regular user
      isAdmin: false,
      isSuperAdmin: false,
      message: 'User not found in database, using default role'
    })
  } catch (error) {
    logger.error('Error getting user role:', error)

    return NextResponse.json({
      isAuthenticated: false,
      role: null,
      isAdmin: false,
      isSuperAdmin: false,
      message: 'Error retrieving role information'
    }, { status: 500 })
  }
}