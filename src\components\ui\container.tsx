import React, { HTMLAttributes } from 'react';
export interface ContainerProps extends HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  size?: 'default' | 'sm' | 'lg' | 'xl' | '2xl' | 'full';
}

export function Container({
  children,
  size = 'default',
  className,
  ...props
}: ContainerProps) {
  return (
    <div
      className={cn(
        'container mx-auto px-4 sm:px-6',
        {
          'max-w-screen-sm': size === 'sm',
          'max-w-screen-lg': size === 'lg',
          'max-w-screen-xl': size === 'xl',
          'max-w-screen-2xl': size === '2xl',
          'max-w-none': size === 'full',
        },
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
} 