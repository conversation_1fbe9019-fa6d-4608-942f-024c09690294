'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { createClient } from "@/lib/supabase/client"
import { CalendarDays, CreditCard, Ticket, Users } from "lucide-react"
import { useEffect, useState } from "react"
import { toast } from "@/components/ui/use-toast"

interface DashboardStatsProps {
  userId: string
}

export function DashboardStats({ userId }: DashboardStatsProps) {
  const [stats, setStats] = useState({
    events: 0,
    tickets: 0,
    payments: 0,
    organizations: 0,
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchStats = async () => {
      setLoading(true)
      try {
        const supabase = createClient()

        // Get registered events count
        const { count: eventCount } = await supabase
          .from('tickets')
          .select('*', { count: 'exact', head: true })
          .eq('user_id', userId)

        // Get tickets count
        const { count: ticketCount } = await supabase
          .from('registrations')
          .select('*', { count: 'exact', head: true })
          .eq('user_id', userId)

        // Get registrations first to get their IDs
        const { data: registrations } = await supabase
          .from('registrations')
          .select('id')
          .eq('user_id', userId)
        
        // Get payments count using the registration IDs
        const registrationIds = registrations ? registrations.map(reg => reg.id) : []
        const { count: paymentCount } = await supabase
          .from('payments')
          .select('*', { count: 'exact', head: true })
          .in('registration_id', registrationIds)

        // Get organizations count
        const { count: orgCount } = await supabase
          .from('organizations')
          .select('*', { count: 'exact', head: true })
          .eq('user_id', userId)

        setStats({
          events: eventCount || 0,
          tickets: ticketCount || 0,
          payments: paymentCount || 0,
          organizations: orgCount || 0,
        })
      } catch (error) {
        console.error("Error fetching stats:", error)
        toast({
          title: "Error",
          description: "Failed to load dashboard stats. Please try again later.",
          variant: "destructive"
        })
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [userId])

  const statCards = [
    {
      title: "Registered Events",
      value: stats.events,
      iconType: "CalendarDays",
      color: "text-blue-500",
      bgColor: "bg-blue-100",
    },
    {
      title: "Tickets",
      value: stats.tickets,
      iconType: "Ticket",
      color: "text-green-500",
      bgColor: "bg-green-100",
    },
    {
      title: "Payments",
      value: stats.payments,
      iconType: "CreditCard",
      color: "text-yellow-500",
      bgColor: "bg-yellow-100",
    },
    {
      title: "Organizations",
      value: stats.organizations,
      iconType: "Users",
      color: "text-purple-500",
      bgColor: "bg-purple-100",
    },
  ]

  const renderIcon = (iconType: string, className: string) => {
    switch (iconType) {
      case 'CalendarDays':
        return <CalendarDays className={className} />;
      case &apos;Ticket&apos;:
        return <Ticket className={className} />;
      case &apos;CreditCard&apos;:
        return <CreditCard className={className} />;
      case &apos;Users&apos;:
        return <Users className={className} />;
      default:
        return null;
    }
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {statCards.map((card, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {card.title}
            </CardTitle>
            <div className={`p-2 rounded-full ${card.bgColor}`}>
              {renderIcon(card.iconType, `h-4 w-4 ${card.color}`)}
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? "..." : card.value}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
} 