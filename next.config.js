/** @type {import('next').NextConfig} */
const path = require('path');
const fs = require('fs');
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});

// Log environment information
console.log('Building with NODE_ENV:', process.env.NODE_ENV);

// Check if we're in a Netlify build environment
const isNetlifyBuild = process.env.NETLIFY === 'true';
const netlifyContext = process.env.CONTEXT || 'development';

if (isNetlifyBuild) {
  console.log('Detected Netlify build environment');
  console.log('Netlify context:', netlifyContext);
  console.log('Netlify URL:', process.env.URL);
  console.log('Netlify deploy URL:', process.env.DEPLOY_URL);
  console.log('Netlify site name:', process.env.SITE_NAME);

  // Check for .netlify-build marker file
  if (fs.existsSync('.netlify-build')) {
    console.log('.netlify-build marker file exists - using Netlify environment variables');
  }

  // Log the site URL being used
  console.log('NEXT_PUBLIC_SITE_URL:', process.env.NEXT_PUBLIC_SITE_URL);

  // Ensure we're using the correct site URL based on the context
  if (netlifyContext === 'production' && process.env.NEXT_PUBLIC_SITE_URL !== 'https://fuiyoo.netlify.app') {
    console.warn('Warning: NEXT_PUBLIC_SITE_URL does not match expected production URL');
  } else if (netlifyContext === 'staging' && process.env.NEXT_PUBLIC_SITE_URL !== 'https://staging--fuiyoo.netlify.app') {
    console.warn('Warning: NEXT_PUBLIC_SITE_URL does not match expected staging URL');
  }
}

// React Compiler configuration (commented out until @react/compiler is installed)
// const withReactCompiler = require('@react/compiler/next');

// Create a conditional configuration that only applies turbopack settings in development
const turbopackConfig = process.env.NODE_ENV === 'development'
  ? {
    // Only used in development
    turbopack: {
      // These fields are supported according to Next.js docs
      resolveAlias: {
        '@': 'src'
      }
    }
  }
  : {};

const nextConfig = {
  // Use 'standalone' for Netlify compatibility with Next.js 15
  output: 'standalone',
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.pexels.com',
        port: '',
        pathname: '/photos/**',
      },
      {
        protocol: 'https',
        hostname: 'img.clerk.com',
        port: '',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: 'images.clerk.dev',
        port: '',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: 'lh3.googleusercontent.com',
        port: '',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: '*.googleusercontent.com',
        port: '',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: 'eibzxudhnojsdxksgowo.supabase.co',
        port: '',
        pathname: '/storage/v1/object/public/**',
      },
    ],
  },
  eslint: {
    // Temporarily disable ESLint during builds due to compatibility issues
    // with @typescript-eslint/no-unused-vars rule in src/lib/supabase/types.ts
    // TODO: Upgrade eslint-config-next to match the ESLint version or fix the specific file
    ignoreDuringBuilds: true,
  },
  typescript: {
    // Re-enabled TypeScript checks during build
    ignoreBuildErrors: false,
  },
  experimental: {
    optimizeCss: false,
    optimizePackageImports: ['@/components/ui'],
  },
  // Allow cross-origin requests during development
  crossOrigin: 'anonymous', // Add this to handle cross-origin requests
  allowedDevOrigins: [
    // Google authentication
    'accounts.google.com',
    'apis.google.com',
    'www.googleapis.com',
    'oauth2.googleapis.com',

    // Supabase
    'eibzxudhnojsdxksgowo.supabase.co',
    'supabase.com',
    'supabase.co',
    '*.supabase.co',

    // Local development - include all possible variations
    'localhost',
    'localhost:*',
    '127.0.0.1',
    '127.0.0.1:*',

    // Specific patterns for Next.js resources
    '*.localhost:*',
    '*.127.0.0.1:*',

    // Wildcard patterns to ensure all resources are accessible
    '*',
    'http://*',
    'https://*',

    // Add your development domain if you're using a custom one
    // 'dev.yourdomain.com',
    // '*.dev.yourdomain.com',
  ],
  reactStrictMode: false,
  staticPageGenerationTimeout: 300,
  poweredByHeader: false,
  compress: true,

  // Webpack configuration (used in production)
  webpack: (config, { isServer }) => {
    // Add alias for easier imports
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': path.join(__dirname, 'src'),
    };

    // Configure cache settings
    if (!config.cache) config.cache = {};
    config.cache.type = 'filesystem';

    // Fix for "self is not defined" error
    if (!isServer) {
      config.output = {
        ...config.output,
        globalObject: 'globalThis',
      };
    }

    return config;
  },

  // Apply the turbopack settings only in development
  ...turbopackConfig
};

// Check if React Compiler should be enabled
const useReactCompiler = process.env.USE_REACT_COMPILER === 'true';

if (useReactCompiler) {
  try {
    // Try to load React Compiler
    const withReactCompiler = require('@react/compiler/next');
    console.log('🚀 Using React Compiler for optimized rendering');
    module.exports = withReactCompiler(withBundleAnalyzer(nextConfig));
  } catch (error) {
    console.warn('⚠️ React Compiler requested but not installed. Falling back to standard build.');
    console.warn('   Install with: npm install @react/compiler');
    module.exports = withBundleAnalyzer(nextConfig);
  }
} else {
  // Standard build without React Compiler
  module.exports = withBundleAnalyzer(nextConfig);
}