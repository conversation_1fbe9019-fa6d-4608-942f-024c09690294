import { Button } from '@/components/ui/button';
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Search, Calendar, MapPin } from 'lucide-react';
import { EventCategory, MalaysiaState } from '../types/event';
import { Input } from '@/components/ui/input';

'use client';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface EventSearchProps {
  className?: string;
}

export default function EventSearch({ className = '' }: EventSearchProps) {
  const router = useRouter();
  const [keyword, setKeyword] = useState('');
  const [category, setCategory] = useState('');
  const [state, setState] = useState('');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();

    // Build query parameters
    const params = new URLSearchParams();

    if (keyword) params.append('keyword', keyword);
    if (category && category !== 'all') params.append('category', category);
    if (state && state !== 'all') params.append('state', state);

    // Navigate to events page with filters
    router.push(`/events?${params.toString()}`);
  };

  // Helper to get display name for dropdown values
  const getCategoryDisplay = () => {
    if (!category || category === 'all') return 'Category';
    return category;
  };

  const getStateDisplay = () => {
    if (!state || state === 'all') return 'State';
    return state;
  };

  return (
    <form onSubmit={handleSearch} className={`${className}`}>
      <div className="flex flex-col md:flex-row gap-4 items-stretch">
        {/* Search input field with enhanced styling */}
        <div className="relative flex-grow md:max-w-lg">
          <Input
            type="text"
            placeholder="Search events..."
            value={keyword}
            onChange={(e) => setKeyword(e.target.value)}
            className="pl-12 h-full min-h-[50px] rounded-xl border-0 shadow-sm bg-[hsl(var(--muted))] focus:bg-[hsl(var(--background))] transition-colors duration-200 text-base"
          />
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 bg-primary/10 p-1.5 rounded-full">
            <Search className="text-primary h-4 w-4" />
          </div>
        </div>

        {/* Filter controls with modern styling */}
        <div className="flex flex-row gap-3 flex-wrap md:flex-nowrap">
          <Select value={category} onValueChange={setCategory}>
            <SelectTrigger className="w-[170px] min-h-[50px] rounded-xl border-0 shadow-sm bg-[hsl(var(--muted))] hover:bg-[hsl(var(--background))] focus:bg-[hsl(var(--background))] transition-colors duration-200">
              <div className="flex items-center">
                <div className="bg-[hsl(var(--primary-50))] p-1 rounded-full mr-2">
                  <Calendar className="h-4 w-4 text-[hsl(var(--primary))]" />
                </div>
                <SelectValue placeholder="Category" />
              </div>
            </SelectTrigger>
            <SelectContent className="rounded-xl border border-[hsl(var(--border))] shadow-lg bg-[hsl(var(--background))]">
              <SelectItem value="all">All Categories</SelectItem>
              {Object.values(EventCategory).map((cat) => (
                <SelectItem key={cat} value={cat}>
                  {cat}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={state} onValueChange={setState}>
            <SelectTrigger className="w-[170px] min-h-[50px] rounded-xl border-0 shadow-sm bg-[hsl(var(--muted))] hover:bg-[hsl(var(--background))] focus:bg-[hsl(var(--background))] transition-colors duration-200">
              <div className="flex items-center">
                <div className="bg-[hsl(var(--primary-50))] p-1 rounded-full mr-2">
                  <MapPin className="h-4 w-4 text-[hsl(var(--primary))]" />
                </div>
                <SelectValue placeholder="Location" />
              </div>
            </SelectTrigger>
            <SelectContent className="rounded-xl border border-[hsl(var(--border))] shadow-lg bg-[hsl(var(--background))]">
              <SelectItem value="all">All States</SelectItem>
              {Object.values(MalaysiaState).map((st) => (
                <SelectItem key={st} value={st}>
                  {st}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button
            type="submit"
            className="min-h-[50px] px-6 font-medium rounded-xl bg-[hsl(var(--primary))] hover:bg-[hsl(var(--primary-hover))] text-[hsl(var(--primary-foreground))] shadow-md hover:shadow-lg transition-all duration-200 hover:translate-y-[-2px]"
          >
            <Search className="h-4 w-4 mr-2" />
            Search
          </Button>
        </div>
      </div>

      {/* Popular searches - new feature */}
      <div className="mt-4 flex items-center gap-2 text-sm text-[hsl(var(--muted-foreground))]">
        <span className="text-xs font-medium">Popular:</span>
        {['Sports', 'Concerts', 'Workshops', 'Charity'].map((term, index) => (
          <button
            key={index}
            type="button"
            onClick={() => {
              setKeyword(term);
              handleSearch({ preventDefault: () => { } } as React.FormEvent);
            }}
            className="px-3 py-1 rounded-full bg-[hsl(var(--muted))] hover:bg-[hsl(var(--muted-foreground))]/20 transition-colors text-xs"
          >
            {term}
          </button>
        ))}
      </div>
    </form>
  );
}