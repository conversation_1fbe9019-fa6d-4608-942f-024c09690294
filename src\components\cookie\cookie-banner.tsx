'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useCookieConsent } from '@/contexts/cookie-consent-context'
import { Button } from '@/components/ui/button'
import { X } from 'lucide-react'

export function CookieBanner() {
  const {
    showBanner,
    acceptAll,
    acceptNecessary,
    openPreferences,
  } = useCookieConsent()

  const [expanded, setExpanded] = useState(false)

  if (!showBanner) return null

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 p-4 md:p-6 bg-[hsl(var(--background))] dark:bg-[hsl(var(--dark-background))] border-t shadow-lg">
      <div className="container mx-auto max-w-7xl">
        <div className="flex flex-col space-y-4">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h3 className="text-lg font-semibold mb-2"><PERSON>ie Preferences</h3>
              <p className="text-sm text-muted-foreground mb-2">
                We use cookies to enhance your browsing experience, serve personalized ads or content, and analyze our traffic. By clicking &quot;Accept All&quot;, you consent to our use of cookies.
              </p>

              {expanded && (
                <div className="mt-4 text-sm text-muted-foreground">
                  <p className="mb-2">
                    <strong>Necessary cookies:</strong> These cookies are essential for the website to function properly.
                  </p>
                  <p className="mb-2">
                    <strong>Analytics cookies:</strong> These cookies help us understand how visitors interact with our website.
                  </p>
                  <p className="mb-2">
                    <strong>Marketing cookies:</strong> These cookies are used to track visitors across websites to display relevant advertisements.
                  </p>
                  <p className="mb-2">
                    <strong>Preference cookies:</strong> These cookies enable the website to remember information that changes the way the website behaves or looks.
                  </p>
                  <p className="mt-4">
                    For more information, please read our{' '}
                    <Link href="/privacy-policy" className="text-primary underline">
                      Privacy Policy
                    </Link>
                    .
                  </p>
                </div>
              )}

              <button
                onClick={() => setExpanded(!expanded)}
                className="text-sm text-primary underline mt-2"
              >
                {expanded ? 'Show less' : 'Learn more'}
              </button>
            </div>

            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 shrink-0 rounded-full"
              onClick={acceptNecessary}
              aria-label="Close cookie banner"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          <div className="flex flex-col sm:flex-row gap-2 justify-end">
            <Button
              variant="outline"
              size="sm"
              onClick={openPreferences}
            >
              Customize
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={acceptNecessary}
            >
              Reject All
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={acceptAll}
            >
              Accept All
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
