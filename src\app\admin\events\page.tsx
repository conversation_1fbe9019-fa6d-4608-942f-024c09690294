"use client";

import { useEffect, useState } from "react";
import { EventStatus } from "../../../types/events";
import { Event as AdminEvent } from "../../../types/events";
import { Event as ApiEvent } from "@/repositories/event-repository";
import { useEvents } from "@/hooks/use-events";
import { Loader2 } from "lucide-react";
import { logger } from '@/lib/logger';

export default function EventsPage() {
  const [events, setEvents] = useState<AdminEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);

  const { events: apiEvents, loading: apiLoading, error: apiError } = useEvents();

  useEffect(() => {
    if (apiEvents && apiEvents.length > 0) {
      // Convert API events to admin format
      const formattedEvents: AdminEvent[] = apiEvents.map((event: ApiEvent) => ({
        id: event.id,
        title: event.title || 'Untitled Event',
        organizerId: event.organizerId || 'unknown',
        organizerName: event.organizerName || 'Unknown Organizer',
        status: (event.status as unknown as EventStatus) || EventStatus.PENDING,
        startDate: typeof event.startDate === 'string' ? event.startDate : new Date().toISOString(),
        endDate: typeof event.endDate === 'string' ? event.endDate : new Date().toISOString(),
        venue: event.location || 'TBA',
        capacity: event.totalCapacity || 100,
        ticketsSold: 0, // This would come from registrations in a real implementation
        createdAt: typeof event.createdAt === 'object' ? event.createdAt.toISOString() : new Date().toISOString(),
      }));

      setEvents(formattedEvents);
      setLoading(false);
    } else if (!apiLoading && apiEvents) {
      // API returned empty results
      setEvents([]);
      setLoading(false);
    }

    if (apiError) {
      logger.error("Error fetching events:", apiError);
      setMessage({ type: 'error', text: 'Failed to load events: ' + apiError.message });
      setLoading(false);
    }
  }, [apiEvents, apiLoading, apiError]);

  const handleStatusChange = async (eventId: string, newStatus: EventStatus) => {
    try {
      // TODO: Implement actual API call
      setEvents(events.map(event =>
        event.id === eventId ? { ...event, status: newStatus } : event
      ));
      setMessage({ type: 'success', text: 'Event status updated successfully' });
    } catch (error) {
      logger.error("Error updating event status:", error);
      setMessage({ type: 'error', text: 'Failed to update event status' });
    }
  };

  return (
    <div className="container mx-auto px-4">
      <h1 className="text-2xl font-semibold mb-6">Event Management</h1>

      {message && (
        <div className={`p-4 mb-6 rounded-md ${message.type === 'success' ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'
          }`}>
          {message.text}
        </div>
      )}

      {loading ? (
        <div className="text-center py-8 flex flex-col items-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
          <span>Loading events...</span>
        </div>
      ) : apiError ? (
        <div className="text-center py-8 text-red-500">
          Error loading events: {apiError.message}
        </div>
      ) : events.length === 0 ? (
        <div className="text-center py-8 text-gray-500">No events found.</div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Event Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Organizer
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date & Venue
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tickets
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {events.map((event) => (
                <tr key={event.id}>
                  <td className="px-6 py-4">
                    <div className="font-medium text-gray-900">{event.title}</div>
                    <div className="text-sm text-gray-500">ID: {event.id}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">{event.organizerName}</div>
                    <div className="text-sm text-gray-500">ID: {event.organizerId}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">
                      {new Date(event.startDate).toLocaleDateString()}
                    </div>
                    <div className="text-sm text-gray-500">{event.venue}</div>
                  </td>
                  <td className="px-6 py-4">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${event.status === EventStatus.APPROVED ? 'bg-green-100 text-green-800' :
                      event.status === EventStatus.PENDING ? 'bg-yellow-100 text-yellow-800' :
                        event.status === EventStatus.REJECTED ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800'
                      }`}>
                      {event.status}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">
                      {event.ticketsSold} / {event.capacity}
                    </div>
                    <div className="text-sm text-gray-500">
                      {Math.round((event.ticketsSold / event.capacity) * 100)}% sold
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    {event.status === EventStatus.PENDING && (
                      <div className="space-x-2">
                        <button
                          onClick={() => handleStatusChange(event.id, EventStatus.APPROVED)}
                          className="text-green-600 hover:text-green-900"
                        >
                          Approve
                        </button>
                        <button
                          onClick={() => handleStatusChange(event.id, EventStatus.REJECTED)}
                          className="text-red-600 hover:text-red-900"
                        >
                          Reject
                        </button>
                      </div>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}