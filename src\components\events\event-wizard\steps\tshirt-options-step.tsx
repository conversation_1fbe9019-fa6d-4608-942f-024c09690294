import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useState, useRef } from 'react';
import { useWizard } from '@/components/events/event-wizard/wizard-container';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import { Plus, Trash2, Loader2, X, Image as ImageIcon } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import Image from 'next/image';
import { logger } from '@/lib/logger';

'use client';

// Default T-shirt sizes
const DEFAULT_SIZES = ["XS", "S", "M", "L", "XL", "XXL", "XXXL"];

export function TshirtOptionsStep() {
  const { formData, updateFormData, nextStep, prevStep } = useWizard();

  // Initialize tshirtOptions if not present
  const tshirtOptions = formData.tshirtOptions || {
    enabled: false,
    sizes: [...DEFAULT_SIZES],
    description: '',
    sizeChartImage: null
  };

  // Local state for managing sizes
  const [sizes, setSizes] = useState<string[]>(tshirtOptions.sizes || [...DEFAULT_SIZES]);
  const [newSize, setNewSize] = useState('');

  // Handle enabling/disabling T-shirt options
  const handleToggle = (enabled: boolean) => {
    updateFormData({
      tshirtOptions: {
        ...tshirtOptions,
        enabled
      }
    });
  };

  // Handle description change
  const handleDescriptionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    updateFormData({
      tshirtOptions: {
        ...tshirtOptions,
        description: e.target.value
      }
    });
  };

  // Handle size selection
  const handleSizeToggle = (size: string, checked: boolean) => {
    let updatedSizes;

    if (checked) {
      updatedSizes = [...sizes, size];
    } else {
      updatedSizes = sizes.filter(s => s !== size);
    }

    setSizes(updatedSizes);
    updateFormData({
      tshirtOptions: {
        ...tshirtOptions,
        sizes: updatedSizes
      }
    });
  };

  // Add custom size
  const handleAddSize = () => {
    if (newSize && !sizes.includes(newSize)) {
      const updatedSizes = [...sizes, newSize];
      setSizes(updatedSizes);
      updateFormData({
        tshirtOptions: {
          ...tshirtOptions,
          sizes: updatedSizes
        }
      });
      setNewSize('');
    }
  };

  // Remove size
  const handleRemoveSize = (size: string) => {
    const updatedSizes = sizes.filter(s => s !== size);
    setSizes(updatedSizes);
    updateFormData({
      tshirtOptions: {
        ...tshirtOptions,
        sizes: updatedSizes
      }
    });
  };

  // Handle image upload
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dropZoneRef = useRef<HTMLDivElement>(null);

  // Function to trigger file input click
  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Handle drag events
  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    // Only set isDragging to false if we&apos;re leaving the dropzone itself, not its children
    if (e.currentTarget === e.target) {
      setIsDragging(false);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const file = e.dataTransfer.files[0] || null;
      handleFileUpload(file);
    }
  };

  // Process file upload
  const handleFileUpload = async (file: File | null) => {
    if (!file) {
      toast({
        title: "Upload Failed",
        description: "No file selected",
        variant: "destructive",
      });
      return;
    }
    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid File Type",
        description: "Please upload an image file (JPEG, PNG, GIF)",
        variant: "destructive",
      });
      return;
    }

    // Validate file size (5MB max)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      toast({
        title: "File Too Large",
        description: "Image must be less than 5MB",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsUploading(true);
      setUploadProgress(10);

      // Create form data for the API
      const apiFormData = new FormData();
      apiFormData.append('file', file);
      apiFormData.append('type', 'tshirt-size-chart');

      if (formData.id) {
        apiFormData.append('eventId', formData.id);
      }

      setUploadProgress(30);
      logger.info('Sending file to API route', {
        fileSize: file.size,
        fileType: file.type,
        fileName: file.name,
        eventId: formData.id || 'none'
      });

      // Get the full URL for the API endpoint
      const apiUrl = new URL('/api/upload', window.location.origin).toString();
      logger.info('Full API URL:', apiUrl);

      // Upload via API route
      const response = await fetch(apiUrl, {
        method: 'POST',
        body: apiFormData
      });

      if (!response.ok) {
        let errorMessage = 'Failed to upload image';
        try {
          const errorData = await response.json();
          logger.error('API upload error:', errorData);
          errorMessage = errorData.error || errorMessage;
        } catch (parseError) {
          logger.error('Error parsing error response:', parseError);
          errorMessage = `Upload failed: ${response.status} ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      let result;
      try {
        result = await response.json();
        logger.info('API upload successful:', result);
      } catch (parseError) {
        logger.error('Error parsing success response:', parseError);
        throw new Error('Invalid response format from server');
      }

      setUploadProgress(100);

      // Update form data with image info
      updateFormData({
        tshirtOptions: {
          ...tshirtOptions,
          sizeChartImage: {
            url: result.url,
            path: result.path
          }
        }
      });
    } catch (err) {
      logger.error('Error uploading size chart image:', err);

      // Get more detailed error information
      const errorDetails = err instanceof Error
        ? err.message
        : 'Failed to upload image';

      // Log additional debugging information
      logger.error('Upload error details:', {
        error: err,
        message: errorDetails,
        apiUrl: new URL('/api/upload', window.location.origin).toString(),
        fileInfo: file ? {
          type: file.type,
          size: file.size,
          name: file.name
        } : 'No file'
      });

      // Try direct upload to Supabase as fallback
      try {
        logger.info('Attempting direct Supabase upload as fallback...');
        const supabase = createClient();

        // Create a unique file path
        const fileExt = file.name.split('.').pop();
        const fileName = `${Date.now()}_${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
        const storagePath = formData.id
          ? `events/${formData.id}/tshirt/${fileName}`
          : `events/drafts/direct-upload/tshirt/${fileName}`;

        // Upload to Supabase Storage
        const { error: _error } = await supabase
          .storage
          .from('images')
          .upload(storagePath, file, {
            contentType: file.type,
            upsert: true
          });

        if (_error) {
          logger.error('Fallback upload failed:', _error);
          throw error;
        }

        // Get public URL
        const { data: { publicUrl } } = supabase
          .storage
          .from('images')
          .getPublicUrl(storagePath);

        logger.info('Fallback upload successful:', { path: storagePath, url: publicUrl });

        // Update form data with image info
        updateFormData({
          tshirtOptions: {
            ...tshirtOptions,
            sizeChartImage: {
              url: publicUrl,
              path: storagePath
            }
          }
        });

        // Show success message
        toast({
          title: "Upload Successful",
          description: "Size chart image uploaded successfully (using fallback method)",
          variant: "default",
        });

        return; // Exit early on success
      } catch (fallbackError) {
        logger.error('Fallback upload also failed:', fallbackError);
      }

      // Show error toast if both methods failed
      toast({
        title: "Upload Failed",
        description: errorDetails,
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // Handle file input change
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0] || null;
    handleFileUpload(file);
  };

  // Remove the size chart image
  const handleRemoveImage = () => {
    updateFormData({
      tshirtOptions: {
        ...tshirtOptions,
        sizeChartImage: null
      }
    });
  };

  // Override next/prev buttons
  const originalNextButton = document.getElementById('wizard-next-button') as HTMLButtonElement;
  if (originalNextButton) {
    originalNextButton.onclick = nextStep;
  }

  const originalPrevButton = document.getElementById('wizard-prev-button') as HTMLButtonElement;
  if (originalPrevButton) {
    originalPrevButton.onclick = prevStep;
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-semibold mb-2">T-Shirt Options</h2>
        <p className="text-gray-500">Configure T-shirt options for your event participants</p>
      </div>

      <div className="flex items-center space-x-2">
        <Switch
          id="tshirt-enabled"
          checked={tshirtOptions.enabled}
          onCheckedChange={handleToggle}
        />
        <Label htmlFor="tshirt-enabled">Provide T-shirts for this event</Label>
      </div>

      {tshirtOptions.enabled && (
        <div className="space-y-6 mt-4">
          <div className="space-y-2">
            <Label htmlFor="tshirt-description">T-Shirt Description</Label>
            <Textarea
              id="tshirt-description"
              value={tshirtOptions.description || ''}
              onChange={handleDescriptionChange}
              placeholder="Describe the T-shirt material, style, etc."
              rows={3}
            />
            <p className="text-xs text-gray-500">
              Provide details about the T-shirt material, style, fit, and any other relevant information.
            </p>
          </div>

          <div className="space-y-2">
            <Label>Available Sizes</Label>
            <Card>
              <CardContent className="pt-6">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {DEFAULT_SIZES.map((size) => (
                    <div key={size} className="flex items-center space-x-2">
                      <Checkbox
                        id={`size-${size}`}
                        checked={sizes.includes(size)}
                        onCheckedChange={(checked) => handleSizeToggle(size, checked as boolean)}
                      />
                      <Label htmlFor={`size-${size}`} className="font-normal">
                        {size}
                      </Label>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-2">
            <Label>Custom Sizes</Label>
            <div className="flex space-x-2">
              <Input
                placeholder="Add custom size (e.g., 4XL)"
                value={newSize}
                onChange={(e) => setNewSize(e.target.value)}
                className="flex-1"
              />
              <Button type="button" onClick={handleAddSize} disabled={!newSize}>
                <Plus className="h-4 w-4 mr-1" /> Add
              </Button>
            </div>

            {sizes.filter(size => !DEFAULT_SIZES.includes(size)).length > 0 && (
              <div className="mt-2">
                <p className="text-sm font-medium mb-2">Custom sizes:</p>
                <div className="flex flex-wrap gap-2">
                  {sizes.filter(size => !DEFAULT_SIZES.includes(size)).map((size) => (
                    <div key={size} className="flex items-center bg-secondary text-secondary-foreground px-3 py-1 rounded-md">
                      {size}
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-5 w-5 ml-1 p-0"
                        onClick={() => handleRemoveSize(size)}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          <div className="space-y-2">
            <Label>Size Chart Image</Label>

            {tshirtOptions.sizeChartImage ? (
              <div className="relative w-full rounded-md overflow-hidden border">
                <div className="relative w-full h-[200px]">
                  <Image
                    src={tshirtOptions.sizeChartImage.url}
                    alt="T-shirt Size Chart"
                    fill
                    className="object-contain"
                  />
                </div>
                <Button
                  type="button"
                  onClick={handleRemoveImage}
                  variant="destructive"
                  size="sm"
                  className="absolute top-2 right-2"
                >
                  <X className="h-4 w-4 mr-1" /> Remove
                </Button>
              </div>
            ) : (
              <>
                <div
                  className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${isDragging
                    ? 'border-primary bg-primary/5'
                    : 'border-gray-300 hover:border-primary/50'
                    }`}
                  onDragEnter={handleDragEnter}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                  ref={dropZoneRef}
                >
                  <input
                    type="file"
                    id="sizeChartImage"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                    disabled={isUploading}
                    ref={fileInputRef}
                  />
                  <div
                    onClick={triggerFileInput}
                    className="flex flex-col items-center justify-center cursor-pointer"
                  >
                    {isUploading ? (
                      <div className="flex flex-col items-center space-y-2">
                        <Loader2 className="w-8 h-8 text-blue-500 animate-spin" />
                        <span className="text-sm text-gray-500">Uploading... {uploadProgress}%</span>
                      </div>
                    ) : (
                      <>
                        <ImageIcon className="w-8 h-8 text-gray-400" />
                        <span className="mt-2 text-sm text-gray-500">
                          Click or drag and drop to upload size chart image
                        </span>
                        <span className="mt-1 text-xs text-gray-400">
                          PNG, JPG or GIF (max. 5MB)
                        </span>
                      </>
                    )}
                  </div>
                </div>
              </>
            )}

            <p className="text-xs text-gray-500">
              Upload an image showing the size measurements to help participants choose the right size.
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
