#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const rootDir = path.resolve(__dirname, '..');
// Focus on TypeScript and React files
const extensions = ['.ts', '.tsx'];
// Files that need fixing
const targetFiles = [
  'src/components/profile/contacts-list.tsx',
  'src/components/profile/edit-form.tsx',
  'src/components/profile/privacy-settings.tsx',
  'src/components/profile/profile-completion.tsx'
];

// Function to process a file
function processFile(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`File doesn't exist: ${filePath}`);
    return false;
  }

  console.log(`Processing: ${filePath}`);
  let content = fs.readFileSync(filePath, 'utf8');
  const originalContent = content;
  
  // First, make sure we have the React import
  if (!content.includes("import React")) {
    content = "import React from 'react';\n" + content;
  }

  // Fix all broken imports with 'import React, {'
  // Replace them with just 'import {'
  content = content.replace(/import React, \{/g, 'import {');
  
  // Special fix for the last line
  if (content.endsWith('@/components/ui/')) {
    content = content.substring(0, content.length - '@/components/ui/'.length);
  }
  
  // Only write to the file if changes were made
  if (content !== originalContent) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`  - Updated`);
    return true;
  }
  
  return false;
}

// Main execution
console.log('Starting import syntax fix script...');

let updatedCount = 0;
for (const file of targetFiles) {
  const filePath = path.join(rootDir, file);
  const updated = processFile(filePath);
  if (updated) updatedCount++;
}

console.log(`\nDone! Updated ${updatedCount} files.`); 