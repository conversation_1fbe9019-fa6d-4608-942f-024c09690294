import { NextResponse } from 'next/server';

interface State {
  name: string;
  code: string;
}

interface Country {
  name: string;
  code: string;
  flag: string;
}

// Common states for popular countries
const FALLBACK_STATES: Record<string, State[]> = {
  'US': [
    { name: 'Alabama', code: 'AL' },
    { name: 'Alaska', code: 'AK' },
    { name: 'Arizona', code: 'AZ' },
    { name: 'California', code: 'CA' },
    { name: 'Colorado', code: 'CO' },
    { name: 'Florida', code: 'FL' },
    { name: 'New York', code: 'NY' },
    { name: 'Texas', code: 'TX' },
    { name: 'Washington', code: 'WA' },
  ],
  'CA': [
    { name: 'Alberta', code: 'AB' },
    { name: 'British Columbia', code: 'BC' },
    { name: 'Ontario', code: 'ON' },
    { name: 'Quebec', code: 'QC' },
  ],
  'GB': [
    { name: 'England', code: 'ENG' },
    { name: 'Scotland', code: 'SCT' },
    { name: 'Wales', code: 'W<PERSON>' },
    { name: 'Northern Ireland', code: 'NIR' },
  ],
  'MY': [
    { name: '<PERSON><PERSON>', code: 'JHR' },
    { name: 'Ke<PERSON>', code: 'KD<PERSON>' },
    { name: 'Kelantan', code: 'KTN' },
    { name: 'Melaka', code: 'MLK' },
    { name: 'Negeri Sembilan', code: 'NSN' },
    { name: 'Pahang', code: 'PHG' },
    { name: 'Penang', code: 'PNG' },
    { name: 'Perak', code: 'PRK' },
    { name: 'Perlis', code: 'PLS' },
    { name: 'Sabah', code: 'SBH' },
    { name: 'Sarawak', code: 'SWK' },
    { name: 'Selangor', code: 'SGR' },
    { name: 'Terengganu', code: 'TRG' },
    { name: 'Kuala Lumpur', code: 'KUL' },
    { name: 'Labuan', code: 'LBN' },
    { name: 'Putrajaya', code: 'PJY' },
  ],
};

// Fallback country data to use if the API call fails
const FALLBACK_COUNTRIES: Country[] = [
  { name: 'United States', code: 'US', flag: '🇺🇸' },
  { name: 'United Kingdom', code: 'GB', flag: '🇬🇧' },
  { name: 'Canada', code: 'CA', flag: '🇨🇦' },
  { name: 'Australia', code: 'AU', flag: '🇦🇺' },
  { name: 'Germany', code: 'DE', flag: '🇩🇪' },
  { name: 'France', code: 'FR', flag: '🇫🇷' },
  { name: 'Japan', code: 'JP', flag: '🇯🇵' },
  { name: 'China', code: 'CN', flag: '🇨🇳' },
  { name: 'India', code: 'IN', flag: '🇮🇳' },
  { name: 'Brazil', code: 'BR', flag: '🇧🇷' },
  { name: 'Mexico', code: 'MX', flag: '🇲🇽' },
  { name: 'South Africa', code: 'ZA', flag: '🇿🇦' },
  { name: 'Nigeria', code: 'NG', flag: '🇳🇬' },
  { name: 'Singapore', code: 'SG', flag: '🇸🇬' },
  { name: 'New Zealand', code: 'NZ', flag: '🇳🇿' },
  { name: 'Malaysia', code: 'MY', flag: '🇲🇾' },
];

// In-memory cache with expiration
const statesCache: Record<string, { data: State[], timestamp: number }> = {};

// Cache validity period - 24 hours in milliseconds
const CACHE_VALIDITY = 24 * 60 * 60 * 1000;

export async function GET(request: Request) {
  const url = new URL(request.url);
  const countryCode = url.searchParams.get('country');

  if (!countryCode) {
    return NextResponse.json({ error: 'Country code is required' }, { status: 400 });
  }

  // Return fallback data immediately if available
  if (FALLBACK_STATES[countryCode]) {
    // Check if we have a valid cache
    if (statesCache[countryCode] && (Date.now() - statesCache[countryCode].timestamp) < CACHE_VALIDITY) {
      return NextResponse.json(statesCache[countryCode].data);
    }
    
    // Update cache with fallback data
    statesCache[countryCode] = {
      data: FALLBACK_STATES[countryCode],
      timestamp: Date.now()
    };
    
    return NextResponse.json(FALLBACK_STATES[countryCode]);
  }

  try {
    // Find country in our fallback data instead of making another API call
    const country = FALLBACK_COUNTRIES.find(c => c.code === countryCode);
    const countryName = country?.name || countryCode;

    // Try to fetch states from external API
    const statesResponse = await fetch('https://countriesnow.space/api/v0.1/countries/states', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        country: countryName
      }),
      next: { revalidate: 86400 }, // Cache for 24 hours
    });

    if (!statesResponse.ok) {
      return NextResponse.json(FALLBACK_STATES[countryCode] || []);
    }

    const data = await statesResponse.json();
    
    if (!data.data?.states || data.data.states.length === 0) {
      return NextResponse.json(FALLBACK_STATES[countryCode] || []);
    }
    
    const states = data.data.states
      .map((state: unknown) => ({
        name: state.name,
        code: state.state_code || state.name
      }))
      .filter((state: State) => state.name && state.code)
      .sort((a: State, b: State) => a.name.localeCompare(b.name));
    
    // Update cache
    statesCache[countryCode] = {
      data: states,
      timestamp: Date.now()
    };

    return NextResponse.json(states);
  } catch (error) {
    console.error('Error fetching states:', error);
    return NextResponse.json(FALLBACK_STATES[countryCode] || []);
  }
} 