'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { EventWizard } from '@/components/events/event-wizard/wizard-container';
import { getEvent, getEventCategories, getEventFields } from '@/app/actions/events';
import { EventType } from '@/types/event-types';
import { toast } from '@/components/ui/use-toast';
import { Event } from '@/repositories/event-repository';

interface EventWizardWrapperProps {
  eventTypes: EventType[];
  pageTitle: string;
  key?: string; // Add key prop for forcing remounts
}

export function EventWizardWrapper({ eventTypes, pageTitle }: EventWizardWrapperProps) {
  const searchParams = useSearchParams();
  const eventId = searchParams.get('id');

  const [existingEvent, setExistingEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(!!eventId);
  const [error, setError] = useState<string | null>(null);



  useEffect(() => {
    async function loadEvent() {
      if (!eventId) {
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Add retry logic for network errors
        let eventResponse;
        let retryCount = 0;
        const maxRetries = 3;

        while (retryCount < maxRetries) {
          try {
            // Load the event data
            eventResponse = await getEvent(eventId);
            break; // If successful, break out of the retry loop
          } catch (fetchError) {
            retryCount++;

            if (retryCount >= maxRetries) {
              throw new Error(`Failed to fetch event after ${maxRetries} attempts: ${fetchError instanceof Error ? fetchError.message : String(fetchError)}`);
            }

            // Wait before retrying (exponential backoff)
            const delay = Math.min(1000 * Math.pow(2, retryCount), 5000);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }

        // Check for authentication errors specifically
        if (eventResponse && eventResponse.error === 'Authentication required') {
          // Instead of redirecting immediately, check if we&apos;re already authenticated
          // This helps prevent redirect loops
          try {
            // Try to get the current auth state from localStorage
            const hasLocalStorageAuth = Object.keys(localStorage).some(key =>
              key.startsWith('sb-') && localStorage.getItem(key)?.includes('"access_token"')
            );

            if (hasLocalStorageAuth) {
              // We appear to have auth data in localStorage but the server doesn't recognize it
              // This could be a race condition or cookie issue
              // Set a specific error that doesn't trigger a redirect
              setError('Authentication state is being refreshed. Please try again in a moment.');
              toast({
                title: 'Session Refreshing',
                description: 'Your session is being refreshed. Please try again in a moment.',
                variant: 'default',
              });
              return;
            }

            // If we don&apos;t have local storage auth, we can safely redirect
            // Store the current URL to redirect back after authentication
            const currentUrl = window.location.href;

            // Redirect to sign-in with the current URL as the redirect target
            window.location.href = `/sign-in?redirect_url=${encodeURIComponent(currentUrl)}`;
            return;
          } catch (authCheckError) {
            // If there&apos;s an error checking auth state, set a generic error
            setError('Authentication error. Please try refreshing the page.');
            return;
          }
        }

        if (!eventResponse || !eventResponse.success || !eventResponse.data) {
          setError(eventResponse?.error || 'Failed to load event');
          toast({
            title: 'Error',
            description: 'Failed to load event data. Please try again.',
            variant: 'destructive',
          });
          return;
        }

        const event = eventResponse.data;

        // Load categories for this event
        try {
          const categoriesResponse = await getEventCategories(eventId);

          if (categoriesResponse.success && categoriesResponse.data) {
            // Add categories to the event object
            event.categories = categoriesResponse.data;
          } else {
            // Initialize with empty array to prevent null errors
            event.categories = [];
          }
        } catch (categoryError) {
          // Initialize with empty array to prevent null errors
          event.categories = [];
        }

        // Load custom fields for this event
        try {
          const fieldsResponse = await getEventFields(eventId);

          if (fieldsResponse.success && fieldsResponse.data) {
            // Add custom fields to the event object
            event.customFields = fieldsResponse.data;
          } else {
            // Initialize with empty array to prevent null errors
            event.customFields = [];
          }
        } catch (fieldsError) {
          // Initialize with empty array to prevent null errors
          event.customFields = [];
        }

        // Set the event with all its related data
        setExistingEvent(event);

      } catch (err) {
        setError('An unexpected error occurred');
        toast({
          title: 'Error',
          description: 'Failed to load event data. Please try again.',
          variant: 'destructive',
        });
      } finally {
        // Always update loading state regardless of background refresh
        // This ensures the wizard doesn't get stuck in loading state
        setLoading(false);
      }
    }

    loadEvent();
  }, [eventId, searchParams]);



  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading event data...</p>
        </div>
      </div>
    );
  }

  if (error && eventId) {
    // Check if it&apos;s a network error
    const isNetworkError = error.includes('Failed to fetch') || error.includes('Network error');

    return (
      <div className="bg-destructive/10 p-6 rounded-lg text-center">
        <h3 className="text-xl font-semibold mb-2">Error Loading Event</h3>
        <p className="text-muted-foreground mb-4">{error}</p>

        {isNetworkError ? (
          <>
            <p className="mb-4">There seems to be a network issue. Please check your internet connection.</p>
            <button
              className="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary/90 transition-colors"
              onClick={() => window.location.reload()}
            >
              Refresh Page
            </button>
          </>
        ) : (
          <p>Please go back and try again, or create a new event.</p>
        )}
      </div>
    );
  }

  return <EventWizard eventTypes={eventTypes} existingEvent={existingEvent} pageTitle={pageTitle} />;
}
