import { Progress } from '@/components/ui/progress';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Info } from 'lucide-react';
import { Event } from '@/repositories/event-repository';

'use client';

// This function is adapted from the event-wizard completion rate calculator
export function calculateEventCompletionRate(event: Event): { rate: number; completed: string[]; missing: string[] } {
  // Define required fields for a complete event
  const requiredFields = [
    { name: 'eventTypeId', label: 'Event Type', weight: 1 },
    { name: 'title', label: 'Title', weight: 1 },
    { name: 'description', label: 'Description', weight: 1 },
    { name: 'location', label: 'Location', weight: 1 },
    { name: 'country', label: 'Country', weight: 1 },
    { name: 'state', label: 'State', weight: 1 },
    { name: 'city', label: 'City', weight: 1 },
    { name: 'startDate', label: 'Start Date', weight: 1 },
    { name: 'endDate', label: 'End Date', weight: 1 },
    { name: 'timezone', label: 'Timezone', weight: 0.5 },
    { name: 'categories', label: 'Categories', weight: 1, isArray: true },
    { name: 'customFields', label: 'Custom Fields', weight: 1, isArray: true },
    { name: 'posterImage', label: 'Poster Image', weight: 1.5 },
    { name: 'coverImage', label: 'Cover Image', weight: 1.5 },
  ];

  // Calculate total possible weight
  const totalWeight = requiredFields.reduce((sum, field) => sum + field.weight, 0);
  
  // Calculate completed weight
  let completedWeight = 0;
  const completed: string[] = [];
  const missing: string[] = [];

  requiredFields.forEach(field => {
    const value = event[field.name as keyof Event];
    const isCompleted = field.isArray 
      ? Array.isArray(value) && value.length > 0
      : value !== undefined && value !== null && value !== '';
    
    if (isCompleted) {
      completedWeight += field.weight;
      completed.push(field.label);
    } else {
      missing.push(field.label);
    }
  });

  // Calculate completion rate as a percentage
  const rate = Math.round((completedWeight / totalWeight) * 100);
  
  return { rate, completed, missing };
}

export function EventCompletionIndicator({ event }: { event: Event }) {
  const { rate, completed, missing } = calculateEventCompletionRate(event);
  
  // Determine color based on completion rate
  let progressColor = 'bg-red-500';
  if (rate >= 75) {
    progressColor = 'bg-green-500';
  } else if (rate >= 50) {
    progressColor = 'bg-yellow-500';
  } else if (rate >= 25) {
    progressColor = 'bg-orange-500';
  }

  return (
    <div className="mt-2 mb-1">
      <div className="flex items-center justify-between mb-1">
        <div className="text-xs text-[hsl(var(--muted-foreground))]">Completion</div>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex items-center cursor-help">
                <span className="text-xs font-medium mr-1">{rate}%</span>
                <Info className="h-3 w-3 text-[hsl(var(--muted-foreground))]" />
              </div>
            </TooltipTrigger>
            <TooltipContent className="max-w-xs">
              <div>
                <p className="font-semibold mb-1">Completed fields:</p>
                {completed.length > 0 ? (
                  <ul className="list-disc pl-4 mb-2">
                    {completed.map(field => (
                      <li key={field} className="text-xs">{field}</li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-xs mb-2">None yet</p>
                )}
                
                <p className="font-semibold mb-1">Missing fields:</p>
                {missing.length > 0 ? (
                  <ul className="list-disc pl-4">
                    {missing.map(field => (
                      <li key={field} className="text-xs">{field}</li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-xs">All fields completed!</p>
                )}
              </div>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <Progress value={rate} className={`h-1.5 ${progressColor}`} />
    </div>
  );
}
