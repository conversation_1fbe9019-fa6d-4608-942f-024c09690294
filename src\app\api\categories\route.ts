import { NextResponse } from "next/server";
import { logger } from '@/lib/logger';

export async function GET(request: Request) {
  try {
    const supabase = await createClient();
    
    const { data: categories, error: _error } = await supabase
      .from('event_categories')
      .select('name, description')
      .order('name');

    if (_error) {
      logger.error("Error fetching categories:", _error);
      return new NextResponse("Error fetching categories", { status: 500 });
    }

    return NextResponse.json(categories);
  } catch (_error) {
    logger.error("Error in categories API:", _error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
} 