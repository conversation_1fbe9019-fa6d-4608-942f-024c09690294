import { createId } from '@paralleldrive/cuid2';

// Database schema for data export requests

// Export status type - now using string for database compatibility
export type ExportStatus = string; // 'requested' | 'processing' | 'completed' | 'failed' | 'expired';

// Export format type - now using string for database compatibility
export type ExportFormat = string; // 'json' | 'csv';

// Export type - now using string for database compatibility
export type ExportType = string; // 'all' | 'profile' | 'activity' | 'consents';

// Valid export status values
export const EXPORT_STATUS = {
  REQUESTED: 'requested',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
  EXPIRED: 'expired'
} as const;

// Valid export format values
export const EXPORT_FORMAT = {
  JSON: 'json',
  CSV: 'csv'
} as const;

// Valid export type values
export const EXPORT_TYPE = {
  ALL: 'all',
  PROFILE: 'profile',
  ACTIVITY: 'activity',
  CONSENTS: 'consents'
} as const;

// Data export type for Supabase
export interface DataExport {
  id: string;
  user_id: string;
  status: ExportStatus;
  export_format: ExportFormat;
  export_type: ExportType;
  email: string;
  requested_at: string;
  processed_at?: string;
  completed_at?: string;
  expires_at?: string;
  download_url?: string;
  download_count: string;
  error_message?: string;
  is_deleted: boolean;
}

// Helper function to create a new data export object
export function createDataExport(params: {
  userId: string;
  exportFormat: ExportFormat;
  exportType: ExportType;
  email: string;
  expiresAt?: Date;
}): Omit<DataExport, 'requested_at'> {
  const { userId, exportFormat, exportType, email, expiresAt } = params;

  return {
    id: createId(),
    user_id: userId,
    status: EXPORT_STATUS.REQUESTED,
    export_format: exportFormat,
    export_type: exportType,
    email,
    expires_at: expiresAt ? expiresAt.toISOString() : "",
    download_count: '0',
    is_deleted: false
  };
}