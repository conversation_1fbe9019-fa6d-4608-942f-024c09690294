'use server';

import { isAdmin } from "../../lib/auth";
import { UserRole } from "../../types/roles";
import { adminSupabase } from '@/lib/supabase/admin-client';
import { createClient } from '@/lib/supabase/pages-client';
import { revalidatePath } from "next/cache";
import { z } from 'zod';

// Define validation schemas
const userRoleSchema = z.object({
  userId: z.string().min(1, "User ID is required"),
  role: z.nativeEnum(UserRole)
});

const userSearchSchema = z.object({
  query: z.string().optional(),
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(10),
  orderBy: z.enum(['created_at', 'last_active', 'email']).default('created_at'),
  orderDir: z.enum(['asc', 'desc']).default('desc')
});

const userIdSchema = z.object({
  userId: z.string().min(1, "User ID is required")
});

/**
 * Get users with pagination and filtering
 */
export async function getUsers(input: z.infer<typeof userSearchSchema>) {
  // Verify the current user is an admin
  if (!(await isAdmin())) {
    throw new Error("Unauthorized. Only admins can view user lists.");
  }

  try {
    // Validate input
    const validatedInput = userSearchSchema.parse(input);
    const { query, page, limit, orderBy, orderDir } = validatedInput;

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Get users from Supabase
    const supabase = await adminSupabase();

    // Build the query
    let userQuery = supabase
      .from('users')
      .select('id, first_name, last_name, email, role, created_at, last_sign_in_at, avatar_url, is_active', { count: 'exact' });

    // Add search filter if provided
    if (query) {
      userQuery = userQuery.or(`first_name.ilike.%${query}%,last_name.ilike.%${query}%,email.ilike.%${query}%`);
    }

    // Add ordering
    const dbOrderBy = orderBy === 'email' ? 'email' :
      orderBy === 'last_active' ? 'last_sign_in_at' : 'created_at';

    userQuery = userQuery
      .order(dbOrderBy, { ascending: orderDir === 'asc' })
      .range(offset, offset + limit - 1);

    const { data: users, count, error } = await userQuery;

    if (error) {
      throw new Error(`Database error: ${error.message}`);
    }

    // Map to serializable format
    const serializedUsers = users.map(user => {
      // Use type assertion to handle potential type errors
      const typedUser = user as unknown;
      return {
        id: typedUser.id ?? '',
        firstName: typedUser.first_name ?? '',
        lastName: typedUser.last_name ?? '',
        emailAddress: typedUser.email ?? '',
        role: typedUser.role || UserRole.USER,
        createdAt: typedUser.created_at ?? null,
        lastSignedIn: typedUser.last_sign_in_at ?? null,
        imageUrl: typedUser.avatar_url ?? null,
        isActive: typedUser.is_active ?? false
      };
    });

    return {
      users: serializedUsers,
      pagination: {
        total: count || 0,
        page,
        limit,
        totalPages: Math.ceil((count || 0) / limit)
      }
    };
  } catch (error) {
    console.error("Error getting users:", error);
    throw new Error("Failed to retrieve users. Please try again later.");
  }
}

/**
 * Get detailed information about a user
 */
export async function getUserDetails(input: z.infer<typeof userIdSchema>) {
  // Verify the current user is an admin
  if (!(await isAdmin())) {
    throw new Error("Unauthorized. Only admins can view user details.");
  }

  try {
    // Validate input
    const { userId } = userIdSchema.parse(input);

    // Get user data from Supabase
    const supabase = await adminSupabase();
    const { data: userData, error } = await supabase
      .from('users')
      .select('*, profiles(*)')
      .eq('id', userId)
      .single();

    if (error) {
      console.error("Error fetching user data from Supabase:", error);
      throw new Error(`Failed to retrieve user: ${error.message}`);
    }

    // Get auth user data
    // Use type assertion to handle potential type errors
    const typedUserData = userData as unknown;
    const authUserId = typedUserData.auth_user_id;

    let authData = null;
    let authError = null;

    if (authUserId) {
      const result = await supabase.auth.admin.getUserById(authUserId);
      authData = result.data;
      authError = result.error;
    }

    if (authError) {
      console.error("Error fetching auth user data:", authError);
    }

    // Format user details

    const userDetails = {
      id: typedUserData.id,
      firstName: typedUserData.first_name,
      lastName: typedUserData.last_name,
      emailAddress: typedUserData.email,
      role: typedUserData.role || UserRole.USER,
      createdAt: typedUserData.created_at,
      lastSignedIn: typedUserData.last_sign_in_at,
      imageUrl: typedUserData.avatar_url,
      isActive: typedUserData.is_active,
      authUserId: typedUserData.auth_user_id,
      authUser: authData?.user || null,
      profile: typedUserData.profiles || null
    };

    return { user: userDetails };
  } catch (error) {
    console.error("Error getting user details:", error);
    throw new Error("Failed to retrieve user details. Please try again later.");
  }
}

/**
 * Set a user's role
 */
export async function setUserRole(input: z.infer<typeof userRoleSchema>) {
  // Verify the current user is an admin
  if (!(await isAdmin())) {
    throw new Error("Unauthorized. Only admins can set roles.");
  }

  try {
    // Validate input
    const { userId, role } = userRoleSchema.parse(input);

    // Get the user to find their auth_user_id
    const supabase = await adminSupabase();
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('auth_user_id')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error("Error fetching user:", userError);
      throw new Error(`Failed to find user: ${userError.message}`);
    }

    // Update the role in Supabase Auth metadata
    // Use type assertion to handle potential type errors
    const typedUserData = userData as unknown;
    const authUserId = typedUserData.auth_user_id;

    if (authUserId) {
      const { error: authError } = await supabase.auth.admin.updateUserById(
        authUserId,
        { user_metadata: { role } }
      );

      if (authError) {
        console.error("Error updating role in Supabase Auth:", authError);
      }
    }

    // Update the role in the users table
    const { error } = await supabase
      .from('users')
      .update({ role })
      .eq('id', userId);

    if (error) {
      console.error("Error updating role in Supabase:", error);
      throw new Error(`Failed to update user role in database: ${error.message}`);
    }

    // Revalidate admin paths to reflect changes
    revalidatePath('/admin/users');
    revalidatePath(`/admin/users/${userId}`);

    return {
      success: true,
      message: `User ${userId} has been assigned the ${role} role.`
    };
  } catch (error) {
    console.error("Error setting user role:", error);
    if (error instanceof z.ZodError) {
      throw new Error(`Invalid input: ${error.errors?.[0]?.message || 'Invalid input data'}`);
    }
    throw new Error("Failed to update user role. Please try again later.");
  }
}

/**
 * Remove a user's role
 */
export async function removeUserRole(input: z.infer<typeof userIdSchema>) {
  // Verify the current user is an admin
  if (!(await isAdmin())) {
    throw new Error("Unauthorized. Only admins can remove roles.");
  }

  try {
    // Validate input
    const { userId } = userIdSchema.parse(input);

    // Get the user to find their auth_user_id
    const supabase = await adminSupabase();
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('auth_user_id')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error("Error fetching user:", userError);
      throw new Error(`Failed to find user: ${userError.message}`);
    }

    // Update the role in Supabase Auth metadata
    // Use type assertion to handle potential type errors
    const typedUserData = userData as unknown;
    const authUserId = typedUserData.auth_user_id;

    if (authUserId) {
      const { error: authError } = await supabase.auth.admin.updateUserById(
        authUserId,
        { user_metadata: { role: UserRole.USER } }
      );

      if (authError) {
        console.error("Error updating role in Supabase Auth:", authError);
      }
    }

    // Update the role in the users table
    const { error } = await supabase
      .from('users')
      .update({ role: UserRole.USER })
      .eq('id', userId);

    if (error) {
      console.error("Error removing role in Supabase:", error);
      throw new Error(`Failed to update user role in database: ${error.message}`);
    }

    // Revalidate admin paths to reflect changes
    revalidatePath('/admin/users');
    revalidatePath(`/admin/users/${userId}`);

    return {
      success: true,
      message: `Role has been reset for user ${userId}.`
    };
  } catch (error) {
    console.error("Error removing user role:", error);
    if (error instanceof z.ZodError) {
      throw new Error(`Invalid input: ${error.errors?.[0]?.message || 'Invalid input data'}`);
    }
    throw new Error("Failed to remove user role. Please try again later.");
  }
}

/**
 * Deactivate/ban a user
 */
export async function deactivateUser(input: z.infer<typeof userIdSchema>) {
  // Verify the current user is an admin
  if (!(await isAdmin())) {
    throw new Error("Unauthorized. Only admins can deactivate users.");
  }

  try {
    // Validate input
    const { userId } = userIdSchema.parse(input);

    // Get the user to find their auth_user_id
    const supabase = await adminSupabase();
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('auth_user_id')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error("Error fetching user:", userError);
      throw new Error(`Failed to find user: ${userError.message}`);
    }

    // Update the status in Supabase Auth metadata
    // Use type assertion to handle potential type errors
    const typedUserData = userData as unknown;
    const authUserId = typedUserData.auth_user_id;

    if (authUserId) {
      const { error: authError } = await supabase.auth.admin.updateUserById(
        authUserId,
        { user_metadata: { userStatus: 'deactivated' } }
      );

      if (authError) {
        console.error("Error updating status in Supabase Auth:", authError);
      }
    }

    // Mark as inactive in Supabase users table
    // Use a different property name to avoid TypeScript errors
    const updateData: Record<string, unknown> = {};
    updateData.is_active = false;

    const { error } = await supabase
      .from('users')
      .update(updateData)
      .eq('id', userId);

    if (error) {
      console.error("Error deactivating user in Supabase:", error);
      throw new Error(`Failed to deactivate user in database: ${error.message}`);
    }

    // Revalidate admin paths to reflect changes
    revalidatePath('/admin/users');
    revalidatePath(`/admin/users/${userId}`);

    return {
      success: true,
      message: `User ${userId} has been deactivated.`
    };
  } catch (error) {
    console.error("Error deactivating user:", error);
    if (error instanceof z.ZodError) {
      throw new Error(`Invalid input: ${error.errors?.[0]?.message || 'Invalid input data'}`);
    }
    throw new Error("Failed to deactivate user. Please try again later.");
  }
}

/**
 * Reactivate a previously banned user
 */
export async function reactivateUser(input: z.infer<typeof userIdSchema>) {
  // Verify the current user is an admin
  if (!(await isAdmin())) {
    throw new Error("Unauthorized. Only admins can reactivate users.");
  }

  try {
    // Validate input
    const { userId } = userIdSchema.parse(input);

    // Get the user to find their auth_user_id
    const supabase = await adminSupabase();
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('auth_user_id')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error("Error fetching user:", userError);
      throw new Error(`Failed to find user: ${userError.message}`);
    }

    // Update the status in Supabase Auth metadata
    // Use type assertion to handle potential type errors
    const typedUserData = userData as unknown;
    const authUserId = typedUserData.auth_user_id;

    if (authUserId) {
      const { error: authError } = await supabase.auth.admin.updateUserById(
        authUserId,
        { user_metadata: { userStatus: 'active' } }
      );

      if (authError) {
        console.error("Error updating status in Supabase Auth:", authError);
      }
    }

    // Mark as active in Supabase users table
    // Use a different property name to avoid TypeScript errors
    const updateData: Record<string, unknown> = {};
    updateData.is_active = true;

    const { error } = await supabase
      .from('users')
      .update(updateData)
      .eq('id', userId);

    if (error) {
      console.error("Error reactivating user in Supabase:", error);
      throw new Error(`Failed to reactivate user in database: ${error.message}`);
    }

    // Revalidate admin paths to reflect changes
    revalidatePath('/admin/users');
    revalidatePath(`/admin/users/${userId}`);

    return {
      success: true,
      message: `User ${userId} has been reactivated.`
    };
  } catch (error) {
    console.error("Error reactivating user:", error);
    if (error instanceof z.ZodError) {
      throw new Error(`Invalid input: ${error.errors?.[0]?.message || 'Invalid input data'}`);
    }
    throw new Error("Failed to reactivate user. Please try again later.");
  }
}