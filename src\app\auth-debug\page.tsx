'use client'

import { useAuth } from '@/contexts/auth-context'
import { useEffect, useState } from 'react'
import { createClient } from '@/lib/supabase/client'

export default function AuthDebugPage() {
  const { isSignedIn, user, loading, refreshSession } = useAuth()
  const [directUser, setDirectUser] = useState<unknown>(null)
  const [directLoading, setDirectLoading] = useState(true)
  const [refreshCount, setRefreshCount] = useState(0)

  // Directly check auth status
  useEffect(() => {
    const checkAuth = async () => {
      setDirectLoading(true)
      try {
        const supabase = createClient()
        const { data: { user: authUser }, error } = await supabase.auth.getUser()
        
        if (authUser) {
          // Get user data from database
          const { data: userData } = await supabase
            .from('users')
            .select('*')
            .eq('auth_user_id', authUser.id)
            .single()
          
          setDirectUser({
            authUser,
            userData
          })
        } else {
          setDirectUser(null)
        }
      } catch (error) {
        console.error('Error checking auth:', error)
      } finally {
        setDirectLoading(false)
      }
    }
    
    checkAuth()
  }, [refreshCount])

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">Auth Debug Page</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div className="bg-gray-100 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Auth Context State</h2>
          <div className="space-y-2">
            <p><strong>Is Signed In:</strong> {isSignedIn ? 'Yes' : 'No'}</p>
            <p><strong>Loading:</strong> {loading ? 'Yes' : 'No'}</p>
            <p><strong>Has User:</strong> {user ? 'Yes' : 'No'}</p>
            
            {user && (
              <div className="mt-4">
                <h3 className="text-lg font-medium mb-2">User Data</h3>
                <pre className="bg-gray-200 p-4 rounded overflow-auto text-xs">
                  {JSON.stringify(user, null, 2)}
                </pre>
              </div>
            )}
          </div>
          
          <button 
            onClick={() => refreshSession()}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Refresh Session
          </button>
        </div>
        
        <div className="bg-gray-100 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">Direct Auth Check</h2>
          <div className="space-y-2">
            <p><strong>Loading:</strong> {directLoading ? 'Yes' : 'No'}</p>
            <p><strong>Has User:</strong> {directUser ? 'Yes' : 'No'}</p>
            
            {directUser && (
              <div className="mt-4">
                <h3 className="text-lg font-medium mb-2">Auth User</h3>
                <pre className="bg-gray-200 p-4 rounded overflow-auto text-xs">
                  {JSON.stringify(directUser.authUser, null, 2)}
                </pre>
                
                <h3 className="text-lg font-medium mt-4 mb-2">Database User</h3>
                <pre className="bg-gray-200 p-4 rounded overflow-auto text-xs">
                  {JSON.stringify(directUser.userData, null, 2)}
                </pre>
              </div>
            )}
          </div>
          
          <button 
            onClick={() => setRefreshCount(prev => prev + 1)}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Refresh Direct Check
          </button>
        </div>
      </div>
    </div>
  )
}
