'use server';

import { createClient } from '@/lib/supabase/server';
import { UserRole } from '@/types/roles';
import { cache } from 'react';
import { redirectToSignIn, redirectToDashboard } from './redirect-actions';

/**
 * Get the authenticated user with caching
 * This function is cached to avoid redundant database queries
 * @returns The authenticated user or null if not authenticated
 */
export const getAuthUser = cache(async () => {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  return user;
});

/**
 * Get the authenticated session with caching
 * This function is cached to avoid redundant database queries
 * @returns The authenticated session or null if not authenticated
 */
export const getAuthSession = cache(async () => {
  const supabase = await createClient();
  const { data: { session } } = await supabase.auth.getSession();
  return session;
});

/**
 * Get the user data from the database with caching
 * This function is cached to avoid redundant database queries
 * @returns The user data from the database or null if not found
 */
export const getUserData = cache(async () => {
  const user = await getAuthUser();

  if (!user) {
    return null;
  }

  const supabase = await createClient();
  const { data: userData, error } = await supabase
    .from('users')
    .select('*')
    .eq('auth_user_id', user.id)
    .single();

  if (error || !userData) {
    console.error("Error fetching user data:", error);
    return null;
  }

  return userData;
});

/**
 * Get the user role with caching
 * This function is cached to avoid redundant database queries
 * @returns The user role or null if not authenticated
 */
export const getUserRole = cache(async (): Promise<UserRole | null> => {
  const userData = await getUserData();
  if (!userData || !userData.role) {
    return null;
  }

  // Validate that the role is a valid UserRole
  if (Object.values(UserRole).includes(userData.role as UserRole)) {
    return userData.role as UserRole;
  }

  return null;
});

/**
 * Check if the user has a specific role
 * @param role The role to check
 * @returns True if the user has the role, false otherwise
 */
export const hasRole = cache(async (role: UserRole): Promise<boolean> => {
  const userRole = await getUserRole();
  return userRole === role;
});

/**
 * Check if the user has any of the specified roles
 * @param roles The roles to check
 * @returns True if the user has any of the roles, false otherwise
 */
export const hasAnyRole = cache(async (roles: UserRole[]): Promise<boolean> => {
  const userRole = await getUserRole();
  return userRole ? roles.includes(userRole) : false;
});

/**
 * Check if the user is an admin
 * @returns True if the user is an admin, false otherwise
 */
export const isAdmin = cache(async (): Promise<boolean> => {
  const userRole = await getUserRole();
  return userRole === UserRole.ADMIN || userRole === UserRole.SUPER_ADMIN;
});

/**
 * Check if the user is a super admin
 * @returns True if the user is a super admin, false otherwise
 */
export const isSuperAdmin = cache(async (): Promise<boolean> => {
  const userRole = await getUserRole();
  return userRole === UserRole.SUPER_ADMIN;
});

/**
 * Check if the user is authenticated
 * This is a lightweight check that doesn't query the database
 * @returns True if the user is authenticated, false otherwise
 */
export const isAuthenticated = cache(async (): Promise<boolean> => {
  const user = await getAuthUser();
  const session = await getAuthSession();
  return !!user && !!session;
});

/**
 * Require authentication for a page or component
 * If the user is not authenticated, redirect to the sign-in page
 * @param redirectTo The URL to redirect to after sign-in (defaults to current URL)
 */
export async function requireAuth(redirectTo?: string) {
  const isUserAuthenticated = await isAuthenticated();

  if (!isUserAuthenticated) {
    await redirectToSignIn(redirectTo);
  }
}

/**
 * Require a specific role for a page or component
 * If the user does not have the required role, redirect to the dashboard
 * @param role The role required to access the page or component
 */
export async function requireRole(role: UserRole) {
  await requireAuth();

  const hasRequiredRole = await hasRole(role);

  if (!hasRequiredRole) {
    await redirectToDashboard();
  }
}

/**
 * Require any of the specified roles for a page or component
 * If the user does not have any of the required roles, redirect to the dashboard
 * @param roles The roles required to access the page or component
 */
export async function requireAnyRole(roles: UserRole[]) {
  await requireAuth();

  const hasRequiredRole = await hasAnyRole(roles);

  if (!hasRequiredRole) {
    await redirectToDashboard();
  }
}

/**
 * Require admin role for a page or component
 * If the user is not an admin, redirect to the dashboard
 */
export async function requireAdmin() {
  await requireAuth();

  const isUserAdmin = await isAdmin();

  if (!isUserAdmin) {
    await redirectToDashboard();
  }
}

/**
 * Require super admin role for a page or component
 * If the user is not a super admin, redirect to the dashboard
 */
export async function requireSuperAdmin() {
  await requireAuth();

  const isSuperAdminUser = await isSuperAdmin();

  if (!isSuperAdminUser) {
    await redirectToDashboard();
  }
}
