import { BaseRepository } from './base-repository';
import { createClient } from '@/lib/supabase/pages-client';

// Define the Event type
export interface Event {
  id: string;
  title: string;
  description: string;
  location: string;
  start_date: string;
  end_date: string;
  status: 'draft' | 'published' | 'cancelled';
  user_id: string;
  organization_id?: string;
  created_at: string;
  updated_at: string;
}

// Define the EventWithDetails type that includes related data
export interface EventWithDetails extends Event {
  organization?: {
    id: string;
    name: string;
    logo_url?: string;
  };
  ticket_types?: Array<{
    id: string;
    name: string;
    price: number;
    capacity: number;
    event_id: string;
  }>;
}

export class EventRepository extends BaseRepository<Event> {
  constructor() {
    super('events');
  }

  /**
   * Check if the current user has access to the event
   */
  async checkAccess(id: string, operation: 'read' | 'write'): Promise<boolean> {
    try {
      const supabase = await this.getClient();

      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        return false;
      }

      // Get the event
      const { data: event, error } = await supabase
        .from('events' as unknown)
        .select('user_id, organization_id')
        .eq('id', id)
        .single();

      // Convert to proper type
      const typedEvent = event as unknown as { user_id: string; organization_id?: string };

      if (error || !event) {
        return false;
      }

      // Check if the user is the owner
      if (typedEvent.user_id === user.id) {
        return true;
      }

      // Check if the user is part of the organization
      if (typedEvent.organization_id) {
        const { data: membership, error: membershipError } = await supabase
          .from('organization_members' as unknown)
          .select('role')
          .eq('organization_id', typedEvent.organization_id)
          .eq('user_id', user.id)
          .single();

        // Convert to proper type
        const typedMembership = membership as unknown as { role: string };

        if (!membershipError && membership) {
          // For read access, any member can access
          if (operation === 'read') {
            return true;
          }

          // For write access, only admins and owners can access
          if (operation === 'write' &&
            (typedMembership.role === 'admin' || typedMembership.role === 'owner')) {
            return true;
          }
        }
      }

      return false;
    } catch (error) {
      console.error('Error checking event access:', error);
      return false;
    }
  }

  /**
   * Get the current user ID
   */
  async getCurrentUserId(): Promise<string | null> {
    try {
      const supabase = await this.getClient();
      const { data: { user } } = await supabase.auth.getUser();
      return user?.id || null;
    } catch (error) {
      console.error('Error getting current user ID:', error);
      return null;
    }
  }

  /**
   * Get published events (public access)
   */
  async getPublishedEvents(limit = 10, offset = 0): Promise<Event[]> {
    try {
      const supabase = await this.getClient();

      const { data, error } = await supabase
        .from('events' as unknown)
        .select('*')
        .eq('status', 'published')
        .order('start_date', { ascending: true })
        .range(offset, offset + limit - 1);

      if (error) {
        console.error('Error fetching published events:', error);
        return [];
      }

      return data as unknown as Event[];
    } catch (error) {
      console.error('Error in getPublishedEvents:', error);
      return [];
    }
  }

  /**
   * Get event with related details
   */
  async getEventWithDetails(id: string): Promise<EventWithDetails | null> {
    try {
      // For published events, allow public access
      const supabase = await this.getClient();
      const { data: eventData, error: eventError } = await supabase
        .from('events' as unknown)
        .select('*')
        .eq('id', id)
        .single();

      if (eventError || !eventData) {
        console.error('Error fetching event:', eventError);
        return null;
      }

      // Convert to proper type
      const typedEventData = eventData as unknown as { status: string };

      // If the event is not published, check authorization
      if (typedEventData.status !== 'published') {
        const hasAccess = await this.checkAccess(id, 'read');
        if (!hasAccess) {
          throw new Error(`Unauthorized: Cannot read event with ID ${id}`);
        }
      }

      // Fetch related data
      const { data, error } = await supabase
        .from('events' as unknown)
        .select(`
          *,
          organization:organization_id(*),
          ticket_types:ticket_types(*)
        `)
        .eq('id', id)
        .single();

      if (error) {
        console.error('Error fetching event with details:', error);
        return null;
      }

      return data as unknown as EventWithDetails;
    } catch (error) {
      console.error('Error in getEventWithDetails:', error);
      return null;
    }
  }

  /**
   * Get events for an organization with authorization check
   */
  async getEventsByOrganization(organizationId: string): Promise<Event[]> {
    try {
      // Check if user is a member of the organization
      const userId = await this.getCurrentUserId();
      const supabase = await this.getClient();

      const { data: membership, error: membershipError } = await supabase
        .from('organization_members' as unknown)
        .select('*')
        .eq('organization_id', organizationId)
        .eq('user_id', userId);

      if (membershipError || !membership || membership.length === 0) {
        throw new Error(`Unauthorized: Not a member of organization ${organizationId}`);
      }

      const { data, error } = await supabase
        .from('events' as unknown)
        .select('*')
        .eq('organization_id', organizationId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching events for organization:', error);
        return [];
      }

      return data as unknown as Event[];
    } catch (error) {
      console.error('Error in getEventsByOrganization:', error);
      return [];
    }
  }

  /**
   * Search events by title, description, or location
   */
  async searchEvents(query: string, limit = 10): Promise<Event[]> {
    try {
      const supabase = await this.getClient();

      const { data, error } = await supabase
        .from('events' as unknown)
        .select('*')
        .eq('status', 'published')
        .or(`title.ilike.%${query}%,description.ilike.%${query}%,location.ilike.%${query}%`)
        .order('start_date', { ascending: true })
        .limit(limit);

      if (error) {
        console.error('Error searching events:', error);
        return [];
      }

      return data as unknown as Event[];
    } catch (error) {
      console.error('Error in searchEvents:', error);
      return [];
    }
  }

  /**
   * Get events created by the current user
   */
  async getByCurrentUser(): Promise<Event[]> {
    try {
      const userId = await this.getCurrentUserId();
      if (!userId) {
        return [];
      }

      const supabase = await this.getClient();

      const { data, error } = await supabase
        .from('events' as unknown)
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching events for current user:', error);
        return [];
      }

      return data as unknown as Event[];
    } catch (error) {
      console.error('Error in getByCurrentUser:', error);
      return [];
    }
  }

  /**
   * Publish an event
   */
  async publishEvent(id: string): Promise<Event | null> {
    try {
      // Check if the user has write access to the event
      const hasAccess = await this.checkAccess(id, 'write');
      if (!hasAccess) {
        throw new Error(`Unauthorized: Cannot publish event with ID ${id}`);
      }

      return this.update(id, { status: 'published' });
    } catch (error) {
      console.error('Error publishing event:', error);
      return null;
    }
  }

  /**
   * Cancel an event
   */
  async cancelEvent(id: string): Promise<Event | null> {
    try {
      // Check if the user has write access to the event
      const hasAccess = await this.checkAccess(id, 'write');
      if (!hasAccess) {
        throw new Error(`Unauthorized: Cannot cancel event with ID ${id}`);
      }

      return this.update(id, { status: 'cancelled' });
    } catch (error) {
      console.error('Error cancelling event:', error);
      return null;
    }
  }
}
