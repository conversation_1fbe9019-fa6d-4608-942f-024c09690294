'use client'

import { cn } from '@/lib/utils'

interface PasswordStrengthIndicatorProps {
  strength: number
  maxStrength?: number
}

/**
 * Password strength indicator component
 * 
 * @param strength The current password strength (0-5)
 * @param maxStrength The maximum strength value (default: 5)
 * @returns A visual indicator of password strength
 */
export function PasswordStrengthIndicator({
  strength,
  maxStrength = 5
}: PasswordStrengthIndicatorProps) {
  /**
   * Get the text description of the password strength
   */
  const getStrengthText = () => {
    if (strength === 0) return 'Very Weak'
    if (strength === 1) return 'Weak'
    if (strength === 2) return 'Fair'
    if (strength === 3) return 'Good'
    if (strength === 4) return 'Strong'
    return 'Very Strong'
  }
  
  /**
   * Get the color for the password strength indicator
   */
  const getStrengthColor = () => {
    if (strength === 0) return 'bg-red-500'
    if (strength === 1) return 'bg-red-400'
    if (strength === 2) return 'bg-yellow-500'
    if (strength === 3) return 'bg-yellow-400'
    if (strength === 4) return 'bg-green-500'
    return 'bg-green-400'
  }
  
  return (
    <div className="space-y-2">
      <div className="flex gap-1 h-1.5">
        {Array.from({ length: maxStrength }).map((_, index) => (
          <div
            key={index}
            className={cn(
              'h-full flex-1 rounded-full transition-colors',
              index < strength ? getStrengthColor() : 'bg-muted'
            )}
            aria-hidden="true"
          />
        ))}
      </div>
      <p className="text-xs text-muted-foreground">
        Password strength: <span className="font-medium">{getStrengthText()}</span>
      </p>
    </div>
  )
}
