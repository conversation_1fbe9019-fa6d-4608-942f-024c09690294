#!/usr/bin/env ts-node

import fs from 'fs/promises';
import path from 'path';
import { createAdminClient } from '@/lib/supabase/admin-client';
import { SchemaManager } from '@/lib/db/schema-manager';
import { asAny } from '@/lib/supabase/extended-types';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { logger } from '@/lib/logger';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Get directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Path to migrations directory
const MIGRATIONS_DIR = path.resolve(__dirname, '../db/migrations');

/**
 * Apply a migration
 */
async function applyMigration(filePath: string): Promise<boolean> {
  try {
    // Read migration file
    const sql = await fs.readFile(filePath, 'utf-8');

    // Get Supabase client
    const supabase = await createAdminClient();
    const supabaseAny = asAny(supabase);

    logger.info(`Applying migration: ${path.basename(filePath)}`);

    // Execute SQL
    const { error } = await supabaseAny.rpc('exec_sql', { sql });

    if (error) {
      logger.error(`Error applying migration: ${error.message}`);
      return false;
    }

    logger.info(`Migration applied successfully: ${path.basename(filePath)}`);
    return true;
  } catch (error) {
    logger.error(`Error applying migration: ${error instanceof Error ? error.message : String(error)}`);
    return false;
  }
}

/**
 * Get applied migrations from the database
 */
async function getAppliedMigrations(): Promise<string[]> {
  try {
    const supabase = await createAdminClient();
    const supabaseAny = asAny(supabase);

    // Check if schema_migrations table exists
    const { data: tableExists, error: tableCheckError } = await supabaseAny.rpc('exec_sql', {
      sql: `SELECT table_name FROM information_schema.tables
            WHERE table_name = 'schema_migrations'
            LIMIT 1`
    });

    const tablesResult = Array.isArray(tableExists) ? tableExists : [];

    if (tableCheckError) {
      logger.error(`Error checking schema_migrations table: ${tableCheckError.message}`);
      return [];
    }

    // Create schema_migrations table if it doesn't exist
    if (!tablesResult || tablesResult.length === 0) {
      logger.info('Creating schema_migrations table');

      const createTableSql = `
        CREATE TABLE IF NOT EXISTS schema_migrations (
          version TEXT PRIMARY KEY,
          applied_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
        );
      `;

      const { error: createError } = await supabaseAny.rpc('exec_sql', {
        sql: createTableSql
      });

      if (createError) {
        logger.error(`Error creating schema_migrations table: ${createError.message}`);
        return [];
      }

      return [];
    }

    // Get applied migrations
    const { data, error } = await supabaseAny
      .from('schema_migrations')
      .select('version')
      .order('applied_at', { ascending: true });

    if (error) {
      logger.error(`Error getting applied migrations: ${error.message}`);
      return [];
    }

    // Add type annotation to fix 'implicitly has an any type' error
    return data.map((m: { version: string }) => m.version);
  } catch (error) {
    logger.error(`Error getting applied migrations: ${error instanceof Error ? error.message : String(error)}`);
    return [];
  }
}

/**
 * Get migrations that need to be applied
 */
async function getPendingMigrations(): Promise<string[]> {
  try {
    // Get migration files
    const files = await fs.readdir(MIGRATIONS_DIR);
    const sqlFiles = files.filter(f => f.endsWith('.sql') && !f.includes('template'));

    // Sort files by version number
    sqlFiles.sort((a, b) => {
      const aParts = a.split('_');
      const bParts = b.split('_');

      const aVersion = aParts.length > 0 ? parseInt(aParts[0] || "0") : 0;
      const bVersion = bParts.length > 0 ? parseInt(bParts[0] || "0") : 0;

      return aVersion - bVersion;
    });

    // Get applied migrations
    const appliedMigrations = await getAppliedMigrations();

    // Get pending migrations
    return sqlFiles.filter(f => {
      const version = f.replace('.sql', '');
      return !appliedMigrations.includes(version);
    });
  } catch (error) {
    logger.error(`Error getting pending migrations: ${error instanceof Error ? error.message : String(error)}`);
    return [];
  }
}

/**
 * Apply pending migrations
 */
async function applyPendingMigrations(): Promise<void> {
  try {
    // Get pending migrations
    const pendingMigrations = await getPendingMigrations();

    if (pendingMigrations.length === 0) {
      logger.info('No pending migrations');
      return;
    }

    logger.info(`Found ${pendingMigrations.length} pending migrations`);

    // Apply migrations
    for (const migration of pendingMigrations) {
      const migrationPath = path.join(MIGRATIONS_DIR, migration);
      const success = await applyMigration(migrationPath);

      if (!success) {
        logger.error(`Failed to apply migration: ${migration}`);
        process.exit(1);
      }
    }

    // Refresh schema cache
    logger.info('Refreshing schema cache');
    const result = await SchemaManager.refreshSchemaCache();

    if (!result.success) {
      logger.error(`Error refreshing schema cache: ${result.message}`);
    } else {
      logger.info('Schema cache refreshed successfully');
    }

    // Validate schema health
    logger.info('Validating schema health');
    const health = await SchemaManager.checkSchemaHealth();

    if (!health.success) {
      logger.error('Schema health check failed:');
      logger.error(JSON.stringify(health, null, 2));
      process.exit(1);
    }

    logger.info('Schema health check passed');
    logger.info('All migrations applied successfully');
  } catch (error) {
    logger.error(`Error applying migrations: ${error instanceof Error ? error.message : String(error)}`);
    process.exit(1);
  }
}

/**
 * Create a new migration file
 */
async function createMigration(name: string): Promise<void> {
  try {
    // Get next version number
    const files = await fs.readdir(MIGRATIONS_DIR);
    const sqlFiles = files.filter(f => f.endsWith('.sql') && !f.includes('template'));

    let maxVersion = 0;

    for (const file of sqlFiles) {
      const parts = file.split('_');
      if (parts.length > 0) {
        const version = parseInt(parts[0] || "0");
        if (!isNaN(version) && version > maxVersion) {
          maxVersion = version;
        }
      }
    }

    const nextVersion = String(maxVersion + 1).padStart(4, '0');
    const fileName = `${nextVersion}_${name}.sql`;
    const filePath = path.join(MIGRATIONS_DIR, fileName);

    // Read template
    const templatePath = path.join(MIGRATIONS_DIR, 'template.sql');
    let template = await fs.readFile(templatePath, 'utf-8');

    // Replace placeholders
    template = template
      .replace(/{version}/g, nextVersion)
      .replace(/{name}/g, name)
      .replace(/{description}/g, `Migration ${nextVersion}: ${name}`)
      .replace(/{author}/g, process.env.USER || 'unknown')
      .replace(/{date}/g, new Date().toISOString());

    // Write file
    await fs.writeFile(filePath, template);

    logger.info(`Created migration: ${fileName}`);
  } catch (error) {
    logger.error(`Error creating migration: ${error instanceof Error ? error.message : String(error)}`);
    process.exit(1);
  }
}

/**
 * Main function
 */
async function main(): Promise<void> {
  const command = process.argv[2];

  if (!command) {
    logger.info('Usage:');
    logger.info('  migrate.ts up      Apply pending migrations');
    logger.info('  migrate.ts create <name>  Create a new migration');
    logger.info('  migrate.ts status    Show migration status');
    logger.info('  migrate.ts refresh    Refresh schema cache');
    process.exit(1);
  }

  switch (command) {
    case 'up':
      await applyPendingMigrations();
      break;

    case 'create': {
      const name = process.argv[3];

      if (!name) {
        logger.error('Missing migration name');
        logger.info('Usage: migrate.ts create <name>');
        process.exit(1);
      }

      await createMigration(name);
      break;
    }

    case 'status': {
      const pendingMigrations = await getPendingMigrations();
      const appliedMigrations = await getAppliedMigrations();

      logger.info(`Applied migrations (${appliedMigrations.length}):`);

      for (const migration of appliedMigrations) {
        logger.info(`  ✓ ${migration}`);
      }

      logger.info(`\nPending migrations (${pendingMigrations.length}):`);

      for (const migration of pendingMigrations) {
        logger.info(`  ∅ ${migration}`);
      }
      break;
    }

    case 'refresh': {
      logger.info('Refreshing schema cache');
      const result = await SchemaManager.refreshSchemaCache();

      if (!result.success) {
        logger.error(`Error refreshing schema cache: ${result.message}`);
        process.exit(1);
      }

      logger.info('Schema cache refreshed successfully');

      // Validate schema health
      logger.info('Validating schema health');
      const health = await SchemaManager.checkSchemaHealth();

      if (!health.success) {
        logger.error('Schema health check failed:');
        logger.error(JSON.stringify(health, null, 2));
        process.exit(1);
      }

      logger.info('Schema health check passed');
      break;
    }

    default:
      logger.error(`Unknown command: ${command}`);
      process.exit(1);
  }
}

// Run main function
main().catch(error => {
  logger.error(`Error: ${error instanceof Error ? error.message : String(error)}`);
  process.exit(1);
});