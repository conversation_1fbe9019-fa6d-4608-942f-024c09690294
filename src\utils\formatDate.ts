import { logger } from '@/lib/logger';
/**
 * Formats a date string (YYYY-MM-DD) or Date object into a more readable format
 */
export function formatDate(dateInput: string | Date | null | undefined): string {
  if (!dateInput) {
    return 'Date not available';
  }

  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  };

  try {
    const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;
    return date.toLocaleDateString('en-US', options);
  } catch (_error) {
    logger.error('Error formatting date:', _error);
    return 'Invalid date';
  }
}